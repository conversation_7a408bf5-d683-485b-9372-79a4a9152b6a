/* * Data converted from /home/<USER>/bysx/xiaozhi-esp32/main/assets/common/vibration.p3
 */
.data
#if !defined (__APPLE__) && !defined (__linux__)
.section .rodata.embedded
#endif

.global vibration_p3
vibration_p3:

.global _binary_vibration_p3_start
_binary_vibration_p3_start: /* for objcopy compatibility */
.byte 0x00, 0x00, 0x00, 0x38, 0x58, 0x22, 0xf9, 0x95, 0x86, 0xf0, 0x61, 0xa6, 0x13, 0xdd, 0x22, 0x92
.byte 0x40, 0xd5, 0xf8, 0x06, 0x4c, 0x2d, 0x3d, 0xc1, 0xe2, 0x7d, 0xb0, 0xe9, 0x8c, 0x43, 0x54, 0xf8
.byte 0xe0, 0x64, 0xe2, 0xc0, 0x3f, 0x44, 0x76, 0x2b, 0x79, 0x52, 0xc9, 0x01, 0xa7, 0x69, 0x2f, 0x09
.byte 0x16, 0x64, 0xb6, 0xbe, 0x77, 0x6b, 0x86, 0x87, 0xb1, 0x9f, 0x4a, 0x80, 0x00, 0x00, 0x00, 0x5d
.byte 0x58, 0xe1, 0x96, 0xb6, 0x53, 0xe6, 0x87, 0x36, 0xca, 0x03, 0x91, 0x65, 0xc8, 0xff, 0x1e, 0xf2
.byte 0x2b, 0x9a, 0x68, 0xf9, 0x1c, 0x24, 0xba, 0x45, 0x4e, 0x86, 0xed, 0xa1, 0xd4, 0x51, 0x16, 0x21
.byte 0x0c, 0x66, 0x26, 0x3e, 0x9c, 0x09, 0xac, 0x68, 0x0b, 0x6d, 0x7d, 0x93, 0x69, 0x22, 0x06, 0x5a
.byte 0xeb, 0x7b, 0xcd, 0xe0, 0xc9, 0x95, 0x32, 0x2b, 0x8e, 0x76, 0xab, 0x1d, 0xd3, 0x8c, 0x99, 0x47
.byte 0xc5, 0x2e, 0x98, 0xc8, 0x6c, 0x01, 0x1b, 0x73, 0x77, 0xb9, 0xcf, 0x88, 0x64, 0x14, 0x18, 0xdc
.byte 0xc8, 0xc8, 0xa9, 0x65, 0xdd, 0xa1, 0x8f, 0xec, 0x6d, 0xe4, 0x46, 0x5d, 0x5c, 0x00, 0x00, 0x00
.byte 0x64, 0x58, 0xc0, 0x47, 0x11, 0x78, 0x81, 0xd3, 0x69, 0x34, 0x2e, 0x43, 0x79, 0xaf, 0x52, 0x3c
.byte 0x3b, 0xa2, 0x31, 0x01, 0xd6, 0x8a, 0x61, 0x86, 0x89, 0x81, 0xf9, 0x15, 0x40, 0x8b, 0x43, 0xac
.byte 0xcb, 0xe1, 0x1d, 0xc0, 0xcc, 0x63, 0x7a, 0x1b, 0x70, 0x10, 0x81, 0x9b, 0x37, 0x03, 0xa0, 0x3e
.byte 0x5d, 0xa2, 0x14, 0xed, 0xc1, 0x70, 0x7e, 0x03, 0xc5, 0x22, 0x4d, 0x92, 0x0e, 0x79, 0x54, 0x4a
.byte 0xbb, 0x21, 0xa2, 0xe1, 0x14, 0x1d, 0x75, 0xc4, 0x73, 0x31, 0xad, 0xf1, 0x5b, 0x7f, 0xed, 0xeb
.byte 0x15, 0x28, 0xcf, 0x54, 0xdc, 0x6e, 0x6c, 0x01, 0x98, 0xa3, 0x12, 0xba, 0x5c, 0xe7, 0xc4, 0x97
.byte 0x86, 0x3a, 0x8a, 0xf3, 0x57, 0x00, 0x00, 0x00, 0x50, 0x58, 0x05, 0x36, 0x4e, 0x7b, 0x7e, 0x38
.byte 0x13, 0x71, 0x1b, 0xf9, 0xc0, 0x1e, 0x16, 0x17, 0x8c, 0x65, 0x79, 0x64, 0xe3, 0x8f, 0x87, 0xbf
.byte 0xe0, 0xda, 0x1e, 0xc4, 0xb3, 0xd8, 0x80, 0x34, 0x84, 0x49, 0xa7, 0x28, 0x18, 0xef, 0x6d, 0x4e
.byte 0x71, 0x1a, 0x22, 0xed, 0xdf, 0x55, 0x5b, 0x03, 0x65, 0x98, 0x0d, 0x34, 0x46, 0x3b, 0x27, 0x16
.byte 0x44, 0xa9, 0xa8, 0xea, 0x04, 0x01, 0xff, 0x20, 0xec, 0xdf, 0xcc, 0x92, 0x24, 0xee, 0xfa, 0xd2
.byte 0x61, 0x8d, 0x7d, 0xdd, 0x90, 0x58, 0xfb, 0x3e, 0xc8, 0x00, 0x00, 0x00, 0x4e, 0x58, 0x03, 0xc1
.byte 0x61, 0xd1, 0xd3, 0x9d, 0x1b, 0x45, 0x42, 0xdc, 0xeb, 0x7f, 0xbe, 0x22, 0x3a, 0xbe, 0x8e, 0xec
.byte 0xab, 0x1c, 0x0f, 0x92, 0x8b, 0xbe, 0x57, 0x98, 0x5e, 0xd1, 0x79, 0x9d, 0x16, 0x81, 0xa8, 0xa1
.byte 0xf4, 0xc2, 0x9f, 0x69, 0x61, 0xfd, 0xe3, 0xa8, 0x20, 0x50, 0x85, 0x4c, 0x29, 0x7a, 0x55, 0xfa
.byte 0x28, 0x3d, 0x15, 0x95, 0xa0, 0x5c, 0xad, 0x7b, 0x47, 0x30, 0x7b, 0xda, 0x5d, 0xbf, 0xb2, 0x5d
.byte 0x73, 0x29, 0xd7, 0x50, 0x2d, 0x5b, 0xa4, 0x08, 0x3a, 0x77, 0x60, 0x00, 0x00, 0x00, 0x5b, 0x58
.byte 0x27, 0x2b, 0x4a, 0x5f, 0x96, 0x00, 0xe8, 0xe7, 0xae, 0x8a, 0x1b, 0xe1, 0xbb, 0x01, 0x87, 0xcc
.byte 0x9e, 0xd2, 0x12, 0x42, 0x78, 0x58, 0xc3, 0xaf, 0x59, 0x60, 0xa4, 0xcd, 0x5c, 0x1a, 0x13, 0xd3
.byte 0xa8, 0x8d, 0xc4, 0x91, 0x1d, 0x1c, 0x4a, 0xd8, 0x52, 0xb4, 0xb6, 0xbd, 0x78, 0x8f, 0xa1, 0xa4
.byte 0xb0, 0x4b, 0x53, 0xf6, 0x97, 0x8b, 0x26, 0xc6, 0x48, 0xab, 0x9e, 0x1b, 0x73, 0x1d, 0xc4, 0xa2
.byte 0xf3, 0x66, 0xf4, 0xd5, 0xda, 0x66, 0x3f, 0xc4, 0xa3, 0xe8, 0x6a, 0x74, 0xe9, 0x01, 0x49, 0xd4
.byte 0xc6, 0xcc, 0x53, 0x28, 0x68, 0x3f, 0xb8, 0xdd, 0x18, 0xe2, 0x00, 0x00, 0x00, 0x8d, 0x58, 0xe0
.byte 0x11, 0xb5, 0x14, 0x55, 0x2b, 0x12, 0x07, 0x89, 0x09, 0xc7, 0xeb, 0xcc, 0x92, 0x9d, 0xae, 0x0e
.byte 0x75, 0xfa, 0xe7, 0xf2, 0x73, 0x09, 0x1e, 0xad, 0x6c, 0x1e, 0xaf, 0x31, 0xe2, 0x2a, 0x8b, 0x9a
.byte 0xb8, 0xa5, 0xeb, 0x7e, 0x91, 0xfb, 0x6b, 0x87, 0x2b, 0x74, 0xcc, 0x83, 0x00, 0x99, 0x10, 0xd0
.byte 0x10, 0x23, 0x6e, 0x92, 0x2f, 0x60, 0x19, 0x55, 0xe0, 0x9d, 0xa7, 0x85, 0x8e, 0x6c, 0x02, 0xd1
.byte 0x6e, 0xa3, 0x09, 0xec, 0xe0, 0x5b, 0xcf, 0xe7, 0xcd, 0x06, 0xa2, 0xc2, 0x04, 0x36, 0xff, 0x8f
.byte 0xcb, 0x9e, 0x4e, 0x22, 0x6f, 0x3d, 0xe3, 0xf8, 0xae, 0xba, 0xf2, 0x1f, 0xc7, 0x43, 0xbe, 0x72
.byte 0x4b, 0x45, 0x67, 0xc4, 0x63, 0xd6, 0x8a, 0x56, 0xaa, 0x73, 0xe0, 0x4e, 0x79, 0xa1, 0x47, 0x7b
.byte 0x81, 0xb2, 0xb2, 0x4e, 0x7f, 0xf9, 0x3c, 0x26, 0x03, 0xed, 0x5b, 0x94, 0x55, 0x3d, 0x7f, 0x50
.byte 0x47, 0xb0, 0x8d, 0xe5, 0x27, 0x77, 0x4a, 0x4c, 0x4a, 0x5f, 0x30, 0x00, 0x00, 0x00, 0x89, 0x58
.byte 0xe8, 0x36, 0x09, 0xd5, 0x0a, 0x15, 0xcd, 0x2b, 0x04, 0xa0, 0x48, 0x04, 0xa3, 0x4c, 0x68, 0xff
.byte 0x4c, 0x6f, 0xe8, 0xb0, 0x7f, 0xde, 0x33, 0xc2, 0xf3, 0xdf, 0x4b, 0x58, 0x1a, 0xee, 0xe9, 0x2a
.byte 0x71, 0x8f, 0x2e, 0xf6, 0xca, 0xed, 0x6f, 0x8f, 0x86, 0x25, 0x17, 0x74, 0x82, 0x78, 0xf9, 0x96
.byte 0x32, 0xef, 0x3a, 0xc0, 0x3b, 0x34, 0x6e, 0x1e, 0xb8, 0x5f, 0x2a, 0xcc, 0xd3, 0xd7, 0xdc, 0x3e
.byte 0xe6, 0x99, 0xb6, 0xcf, 0x4a, 0x07, 0x1e, 0x39, 0x66, 0x5d, 0xf7, 0xb6, 0xfb, 0xb0, 0xd5, 0x9b
.byte 0x4d, 0xf7, 0x20, 0x00, 0xaa, 0xa5, 0x76, 0x36, 0x51, 0xf2, 0x77, 0x83, 0x2c, 0x2c, 0x2a, 0xcb
.byte 0x80, 0x86, 0x51, 0xb0, 0xb2, 0xea, 0xfd, 0xc2, 0xa4, 0x76, 0xc8, 0x0a, 0x91, 0xba, 0x2c, 0xf6
.byte 0x37, 0x55, 0xf1, 0x85, 0xa0, 0x9d, 0xfd, 0xf5, 0xc9, 0xd4, 0x0b, 0xb5, 0xed, 0x56, 0x3f, 0xaa
.byte 0x02, 0x30, 0x7f, 0x85, 0x89, 0x74, 0x3e, 0x30, 0x00, 0x00, 0x00, 0x8b, 0x58, 0xe8, 0x55, 0xa0
.byte 0x5d, 0xc0, 0x4e, 0xd9, 0x87, 0x77, 0x9e, 0xd2, 0x65, 0x24, 0x89, 0x75, 0x10, 0xe6, 0xf8, 0x82
.byte 0xcc, 0x90, 0xb9, 0xa3, 0xaf, 0x5b, 0xfc, 0xa7, 0x5e, 0x6d, 0xbc, 0x8a, 0xa0, 0x01, 0x1c, 0xdf
.byte 0xac, 0x0f, 0xb6, 0xa6, 0xf1, 0x09, 0x4d, 0x1c, 0x5b, 0x23, 0xf5, 0x68, 0x76, 0x20, 0xe2, 0x07
.byte 0x05, 0x5a, 0x1a, 0xe1, 0x16, 0x09, 0x41, 0x05, 0xc9, 0xd5, 0xc6, 0xb6, 0x76, 0x55, 0x3b, 0xcb
.byte 0x84, 0x8d, 0xa2, 0x35, 0xe8, 0x05, 0xe0, 0x45, 0x09, 0xc7, 0xba, 0x27, 0x44, 0x87, 0xf7, 0xb0
.byte 0x9e, 0x3d, 0xec, 0x76, 0xd1, 0x7b, 0x8e, 0x92, 0xbd, 0x1e, 0xdb, 0xfc, 0xbb, 0x04, 0xc5, 0x73
.byte 0xaf, 0x71, 0x87, 0xca, 0x84, 0xfe, 0x2a, 0x18, 0xed, 0x9d, 0x1b, 0xb4, 0xa1, 0xe3, 0x9c, 0x64
.byte 0x06, 0x71, 0xfe, 0x29, 0x03, 0x06, 0x2f, 0xef, 0x1c, 0x89, 0xb1, 0xa9, 0xca, 0x8e, 0x33, 0x52
.byte 0x8c, 0x1d, 0xe1, 0x54, 0x14, 0x21, 0x40, 0x00, 0x00, 0x00, 0x72, 0x58, 0xe7, 0xe1, 0x3e, 0x19
.byte 0x94, 0xed, 0xb2, 0x2a, 0xce, 0x79, 0x11, 0xb9, 0x85, 0x39, 0x29, 0x9a, 0x53, 0x79, 0xe4, 0x54
.byte 0xeb, 0x89, 0x0d, 0xe8, 0xc1, 0xb7, 0x27, 0x62, 0x1e, 0x0c, 0x7e, 0xc2, 0x8b, 0x4b, 0x55, 0xbc
.byte 0xbc, 0x4b, 0x09, 0x18, 0xa2, 0x38, 0x22, 0x58, 0xc0, 0xfb, 0xbf, 0xff, 0x3f, 0xe8, 0xc0, 0x13
.byte 0xbf, 0x96, 0xd0, 0xe1, 0x15, 0x9d, 0xfe, 0x70, 0x83, 0x55, 0xc9, 0xba, 0x26, 0x23, 0xda, 0xf5
.byte 0x1b, 0x22, 0xb1, 0xa0, 0xf4, 0xf3, 0xb4, 0xa2, 0x6e, 0xd3, 0xea, 0xbe, 0x95, 0xb5, 0x76, 0x74
.byte 0xaf, 0x31, 0x6f, 0xd2, 0xd2, 0xa7, 0x9b, 0x9e, 0x90, 0x66, 0x9f, 0x07, 0x41, 0x9e, 0xfb, 0xc5
.byte 0x25, 0x26, 0x9f, 0x12, 0xac, 0x2e, 0xa3, 0xc1, 0xf4, 0x65, 0xdf, 0x52, 0xee, 0x00, 0x00, 0x00
.byte 0x8a, 0x58, 0xe2, 0x19, 0x73, 0xcf, 0x3a, 0xba, 0x6c, 0x52, 0x62, 0x17, 0xbd, 0x88, 0x10, 0xdb
.byte 0xe6, 0x4e, 0x7e, 0x5b, 0x39, 0x40, 0xe9, 0x7d, 0x09, 0x24, 0x1a, 0x26, 0xed, 0xa6, 0xd0, 0x2d
.byte 0xa9, 0x90, 0xef, 0xa3, 0xd4, 0xbd, 0x40, 0xed, 0xff, 0x56, 0x74, 0x30, 0x1e, 0x28, 0xea, 0xa3
.byte 0x2b, 0xbd, 0xc9, 0x62, 0x44, 0x1d, 0x4b, 0xf4, 0x43, 0xaa, 0x10, 0xf4, 0x43, 0x63, 0xec, 0x67
.byte 0x6f, 0x2d, 0xba, 0x8a, 0x8b, 0x80, 0x76, 0xf9, 0xea, 0x6d, 0x93, 0xf9, 0x6c, 0x21, 0x9a, 0x9c
.byte 0xa6, 0x8b, 0x79, 0x71, 0xae, 0x3a, 0xf8, 0x3e, 0x19, 0x94, 0xda, 0xcd, 0x39, 0xdc, 0xb2, 0xe8
.byte 0x6f, 0xbd, 0x05, 0x37, 0x0b, 0xb4, 0x22, 0xb8, 0xc7, 0xe7, 0xe1, 0x4f, 0x99, 0x58, 0x8e, 0xff
.byte 0x17, 0x11, 0x7a, 0xbe, 0x30, 0xc3, 0x51, 0xdf, 0xf3, 0xd4, 0x5f, 0x6d, 0xa5, 0x6f, 0xb9, 0xc9
.byte 0xd8, 0xb0, 0xfc, 0x60, 0xb8, 0xd2, 0xff, 0xa5, 0xbf, 0x5b, 0xe4, 0x00, 0x00, 0x00, 0x60, 0x58
.byte 0xe7, 0xff, 0xdc, 0x2e, 0x9d, 0x23, 0x71, 0x63, 0xc4, 0xdb, 0x20, 0x84, 0x84, 0xfc, 0xb2, 0xaa
.byte 0xe7, 0x1b, 0x8c, 0xe7, 0xc5, 0x0f, 0xec, 0x17, 0x00, 0x03, 0x18, 0x8e, 0x6a, 0x6a, 0x9c, 0xdd
.byte 0xd4, 0x49, 0xa8, 0x84, 0xc6, 0x7b, 0x4d, 0xcf, 0x8d, 0x0f, 0x3e, 0x26, 0xb5, 0xd7, 0xa0, 0xd8
.byte 0xe9, 0xdb, 0x7d, 0xb3, 0x8b, 0x3a, 0xad, 0x79, 0xff, 0x0c, 0x52, 0xd5, 0xd4, 0x0e, 0x7e, 0x88
.byte 0x72, 0xf1, 0xec, 0x95, 0xcd, 0xa4, 0xe7, 0x67, 0xbb, 0xf2, 0x3e, 0x75, 0xe2, 0xa8, 0xa1, 0x0a
.byte 0xf6, 0x55, 0xe8, 0x0f, 0xdf, 0x54, 0xbd, 0xf2, 0x31, 0x77, 0x06, 0x7f, 0xab, 0xfa, 0xa0, 0x00
.byte 0x00, 0x00, 0x79, 0x58, 0xe8, 0x5d, 0x57, 0x77, 0xb4, 0x23, 0x00, 0x78, 0x79, 0xbe, 0xdb, 0x49
.byte 0x39, 0x8c, 0x9b, 0xaf, 0x95, 0x5d, 0xf0, 0xde, 0x43, 0xb0, 0x8c, 0xfc, 0x38, 0x79, 0x77, 0x16
.byte 0x8e, 0x60, 0xc9, 0x31, 0xe0, 0xb0, 0x7d, 0x2f, 0x71, 0x5f, 0xc1, 0x42, 0xba, 0x0b, 0xa3, 0xa2
.byte 0xe4, 0x87, 0xc5, 0x15, 0x75, 0x6f, 0xe5, 0x6b, 0x8e, 0x10, 0xb8, 0x9e, 0xc3, 0xe1, 0x8c, 0xcf
.byte 0x80, 0x70, 0xd9, 0xf2, 0xdf, 0x71, 0xd3, 0x59, 0x29, 0xc8, 0xea, 0x6c, 0xe3, 0x5b, 0x98, 0x97
.byte 0xc6, 0x9e, 0x8f, 0xd7, 0xbc, 0x19, 0x4b, 0x9b, 0xfe, 0x1b, 0x59, 0x03, 0x20, 0x98, 0xf1, 0x59
.byte 0x1b, 0xaf, 0x01, 0x8f, 0x07, 0x42, 0xe6, 0xb0, 0x95, 0xb1, 0x78, 0xcf, 0x4b, 0x0c, 0x52, 0x8c
.byte 0x48, 0xc7, 0x20, 0x6c, 0xc5, 0x4a, 0x41, 0x78, 0x3a, 0xf2, 0x51, 0x7c, 0x00, 0x00, 0x00, 0x76
.byte 0x58, 0xe8, 0x81, 0xba, 0x31, 0xfa, 0xae, 0x93, 0x7e, 0x06, 0x02, 0xac, 0x70, 0x08, 0xaf, 0xb6
.byte 0xb2, 0x7d, 0x02, 0x7e, 0x23, 0x66, 0x53, 0xf8, 0xaf, 0xb7, 0x19, 0x07, 0xe2, 0x9f, 0x38, 0x2c
.byte 0x8d, 0x6d, 0x9e, 0xd1, 0xa5, 0xbb, 0xb1, 0x72, 0xf4, 0x8f, 0x63, 0xcb, 0x53, 0x51, 0x6b, 0x24
.byte 0x56, 0x4a, 0x67, 0x9a, 0x00, 0x52, 0x30, 0x37, 0xde, 0xb9, 0xfc, 0x0e, 0x59, 0x01, 0x4b, 0xcf
.byte 0x93, 0x14, 0x25, 0x8d, 0xd7, 0x0c, 0xa7, 0xc3, 0xdd, 0x46, 0x20, 0x6f, 0xf3, 0xea, 0xf0, 0xc3
.byte 0x55, 0x66, 0x21, 0xd9, 0xf0, 0xe9, 0xb0, 0xe5, 0x9c, 0x5c, 0xf4, 0xe4, 0xd4, 0xcc, 0x17, 0xfc
.byte 0xa8, 0xc4, 0x11, 0x31, 0x9d, 0xc9, 0x02, 0x11, 0x2a, 0x85, 0x71, 0x62, 0x97, 0x90, 0x94, 0xee
.byte 0xda, 0x49, 0x08, 0x2e, 0x3a, 0x80, 0x00, 0x00, 0x00, 0x54, 0x58, 0x0a, 0x37, 0x08, 0xe1, 0xaf
.byte 0xc2, 0x48, 0x41, 0x96, 0x08, 0x36, 0x36, 0x2a, 0x00, 0x47, 0x92, 0x3a, 0x7d, 0x59, 0x3b, 0x3b
.byte 0xcf, 0xcc, 0x16, 0x21, 0x74, 0x21, 0x31, 0x7e, 0xbe, 0xa9, 0xa6, 0x54, 0x25, 0x4b, 0x7e, 0xa7
.byte 0xbe, 0x06, 0x04, 0x0c, 0x4e, 0x3e, 0x64, 0xf0, 0xee, 0xb3, 0x32, 0x51, 0xbf, 0xb9, 0xd2, 0x1f
.byte 0x44, 0x33, 0x02, 0x2a, 0xdb, 0x36, 0x18, 0x67, 0x7c, 0x22, 0xc5, 0x7a, 0xdf, 0x3b, 0x98, 0xe9
.byte 0xfc, 0x72, 0x27, 0x73, 0xc9, 0x13, 0x29, 0x1d, 0xda, 0x32, 0x44, 0xa8, 0x6a, 0xde, 0x00, 0x00
.byte 0x00, 0x51, 0x58, 0x04, 0x7f, 0xbf, 0xec, 0xb7, 0xd7, 0x0b, 0xcb, 0x9b, 0xac, 0xbc, 0xe9, 0x5d
.byte 0xb7, 0xea, 0x59, 0xb7, 0x84, 0xea, 0xb8, 0xde, 0x7d, 0x85, 0x88, 0xf9, 0xe1, 0x6a, 0xfc, 0x96
.byte 0x96, 0x82, 0x3a, 0xed, 0xc5, 0xa6, 0xe4, 0x2d, 0xb5, 0x4d, 0xd2, 0x38, 0xcf, 0xa7, 0x5e, 0x71
.byte 0x88, 0xee, 0x07, 0x60, 0x64, 0x54, 0xb1, 0xf2, 0xf6, 0x15, 0xbe, 0xc5, 0x6e, 0xfe, 0xc2, 0x03
.byte 0x91, 0x1a, 0xa5, 0x11, 0x10, 0x1b, 0xe1, 0xe8, 0xb3, 0x0c, 0x0e, 0x6d, 0x7e, 0x73, 0x09, 0x9e
.byte 0xaf, 0x84, 0x68, 0x00, 0x00, 0x00, 0x50, 0x58, 0x03, 0xe6, 0xd0, 0x35, 0xdf, 0x6f, 0xf0, 0xff
.byte 0x84, 0xd1, 0xf1, 0xba, 0x68, 0x40, 0x46, 0x6f, 0xa3, 0xcd, 0xda, 0x79, 0xa0, 0x7b, 0x16, 0x09
.byte 0x87, 0x0a, 0xf0, 0x79, 0x49, 0x54, 0x75, 0x03, 0xec, 0xd8, 0x3b, 0x68, 0x1a, 0x4d, 0x43, 0x9c
.byte 0xc0, 0x60, 0x6d, 0x74, 0xe0, 0xd2, 0x85, 0x4e, 0xa9, 0xfb, 0xb9, 0x55, 0x0a, 0x61, 0xe2, 0xb5
.byte 0x43, 0xca, 0x48, 0x86, 0xd0, 0x8c, 0xaf, 0xbf, 0x4d, 0x4f, 0x6d, 0xa0, 0x82, 0x56, 0x98, 0xc5
.byte 0x15, 0x32, 0xc8, 0x99, 0x2b, 0xf0, 0x40

.global _binary_vibration_p3_end
_binary_vibration_p3_end: /* for objcopy compatibility */


.global vibration_p3_length
vibration_p3_length:
.long 1815
