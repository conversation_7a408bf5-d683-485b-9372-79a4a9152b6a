/* * Data converted from /home/<USER>/bysx/xiaozhi-esp32/main/assets/common/exclamation.p3
 */
.data
#if !defined (__APPLE__) && !defined (__linux__)
.section .rodata.embedded
#endif

.global exclamation_p3
exclamation_p3:

.global _binary_exclamation_p3_start
_binary_exclamation_p3_start: /* for objcopy compatibility */
.byte 0x00, 0x00, 0x00, 0x23, 0x58, 0x02, 0xf9, 0x96, 0xdc, 0x5e, 0xcc, 0x4e, 0xae, 0xd7, 0xe8, 0xe2
.byte 0xd2, 0xc0, 0xff, 0x76, 0x85, 0xfc, 0x73, 0x89, 0x7f, 0x4d, 0xab, 0xb3, 0x88, 0x0d, 0xf9, 0x4c
.byte 0x97, 0xb0, 0x00, 0x63, 0x2c, 0x82, 0x60, 0x00, 0x00, 0x00, 0x28, 0x58, 0x02, 0x64, 0xfb, 0xcc
.byte 0x6f, 0x14, 0xcb, 0x8b, 0xe2, 0x1d, 0x5f, 0x0f, 0x98, 0x56, 0x2c, 0x04, 0x50, 0xf3, 0x99, 0x51
.byte 0xd8, 0xe4, 0x7e, 0xbd, 0xe8, 0xd1, 0x49, 0x25, 0x38, 0xd2, 0xb1, 0xc3, 0x08, 0x63, 0x04, 0x5b
.byte 0x6a, 0x07, 0x88, 0x00, 0x00, 0x00, 0x29, 0x58, 0x02, 0x64, 0xe5, 0xa1, 0xf9, 0xc2, 0x42, 0x01
.byte 0x8f, 0x86, 0x4d, 0x10, 0xe6, 0xd4, 0xff, 0x61, 0xda, 0x12, 0x74, 0x01, 0x7a, 0x33, 0xe0, 0x66
.byte 0x40, 0x6b, 0x3e, 0xf8, 0x5a, 0x43, 0x38, 0x99, 0x5f, 0x51, 0x9f, 0x00, 0x12, 0x06, 0xc8, 0x15
.byte 0x00, 0x00, 0x00, 0x27, 0x58, 0x02, 0x64, 0xee, 0x46, 0x1d, 0x1d, 0x4b, 0xe3, 0x3a, 0xf9, 0x09
.byte 0xba, 0xdf, 0x34, 0xe6, 0xa3, 0xeb, 0x4e, 0x86, 0x71, 0x0d, 0x0f, 0x47, 0xf4, 0x26, 0xc5, 0xa8
.byte 0x0a, 0x5d, 0x2f, 0x56, 0x10, 0xe0, 0xee, 0x27, 0x20, 0xc6, 0x20, 0x00, 0x00, 0x00, 0x5e, 0x58
.byte 0x62, 0x64, 0xfc, 0x12, 0xef, 0x63, 0xd1, 0x0a, 0x4d, 0xbc, 0xbd, 0x00, 0xca, 0x77, 0xfb, 0xb2
.byte 0x78, 0xab, 0xd6, 0xbf, 0x6f, 0x13, 0x31, 0xbb, 0x62, 0xa0, 0x1d, 0x33, 0xc9, 0x3f, 0x85, 0xc6
.byte 0x8d, 0xc1, 0x4b, 0x89, 0x77, 0xe1, 0x4b, 0xaf, 0xac, 0x2f, 0x51, 0xfb, 0x0c, 0xa9, 0x07, 0x1f
.byte 0x38, 0xff, 0x50, 0xea, 0x3f, 0xcd, 0x4a, 0xbe, 0x63, 0xe9, 0x0b, 0x87, 0x5b, 0x57, 0xfa, 0xde
.byte 0x9c, 0xf1, 0x71, 0x00, 0x11, 0x9e, 0xfa, 0xf5, 0xdd, 0x17, 0xc8, 0x93, 0x6a, 0x47, 0x0e, 0x5c
.byte 0x60, 0x17, 0x16, 0xa0, 0xc0, 0x97, 0x4a, 0x7a, 0x59, 0x2c, 0x5a, 0x12, 0x5c, 0x00, 0x00, 0x00
.byte 0x7c, 0x58, 0xe7, 0x5a, 0x3d, 0xc4, 0x92, 0x1f, 0xa5, 0x85, 0x07, 0xe6, 0xd4, 0x81, 0xfb, 0x4c
.byte 0xa5, 0xf4, 0x77, 0x1e, 0xb4, 0x89, 0xba, 0xc4, 0x2e, 0x90, 0x35, 0xca, 0xe0, 0x45, 0x3c, 0xe5
.byte 0x66, 0x8c, 0x8d, 0x9b, 0xe7, 0xfd, 0x07, 0xfb, 0x4e, 0xa8, 0xfe, 0x5d, 0x70, 0x8a, 0x98, 0xd4
.byte 0x40, 0xb5, 0x3e, 0x06, 0xe0, 0x11, 0x62, 0x60, 0x11, 0x7c, 0x4c, 0xa9, 0xfb, 0x48, 0xf1, 0x88
.byte 0x1f, 0xb9, 0xe2, 0x96, 0xd6, 0x64, 0xfb, 0x3e, 0x39, 0x15, 0x3f, 0x97, 0x5c, 0x9f, 0x4c, 0xaa
.byte 0xb8, 0x63, 0x7e, 0x18, 0x26, 0xe7, 0x4d, 0x46, 0x3d, 0xcf, 0x77, 0x22, 0xb1, 0x47, 0x55, 0xc6
.byte 0xd1, 0xb4, 0x10, 0xb2, 0xf3, 0xd4, 0xf4, 0x06, 0x2d, 0x73, 0x22, 0x66, 0x1d, 0x5d, 0xb3, 0xc9
.byte 0x75, 0xeb, 0x49, 0xb2, 0xbf, 0x91, 0xe4, 0xb5, 0xc2, 0x9a, 0xe0, 0x5f, 0x60, 0x00, 0x00, 0x00
.byte 0x92, 0x58, 0xe7, 0x5a, 0x3d, 0xc3, 0xdc, 0xaa, 0xee, 0x66, 0xdf, 0x29, 0x0b, 0x06, 0x44, 0x9e
.byte 0x72, 0x47, 0x4f, 0xa4, 0xb0, 0xbc, 0x3d, 0x0e, 0xea, 0x59, 0x45, 0x91, 0x98, 0xa3, 0xf5, 0x37
.byte 0xda, 0x1e, 0x3d, 0xdb, 0x2a, 0xa3, 0xb2, 0x78, 0x14, 0xdd, 0x39, 0x48, 0x1c, 0x1f, 0xda, 0xe1
.byte 0xbd, 0xbf, 0xc6, 0x8e, 0x6e, 0xe5, 0xc2, 0xef, 0x57, 0x8c, 0xbd, 0xd8, 0xe9, 0x30, 0x6c, 0xbc
.byte 0x64, 0x2e, 0xd2, 0x42, 0xf4, 0xb3, 0x05, 0xa0, 0x21, 0x37, 0xae, 0xb4, 0x98, 0x3b, 0x64, 0x3f
.byte 0xae, 0xa9, 0xc1, 0x68, 0xb8, 0x8d, 0x20, 0xc5, 0xd1, 0x2a, 0x39, 0x72, 0x31, 0xd7, 0x8e, 0xc1
.byte 0xea, 0xc6, 0xf4, 0x52, 0x15, 0x0f, 0xa7, 0x82, 0x8a, 0xd9, 0xed, 0xd8, 0x78, 0x59, 0x2c, 0xc5
.byte 0x36, 0xbd, 0xa7, 0x7f, 0xaa, 0x7d, 0x57, 0x5b, 0x14, 0x3e, 0xfe, 0x9b, 0xa9, 0x2e, 0xac, 0x1f
.byte 0xb5, 0xb8, 0x3c, 0x39, 0xb9, 0x41, 0xcd, 0x48, 0x46, 0x79, 0xcc, 0xdb, 0x98, 0xfe, 0xed, 0x72
.byte 0xc3, 0x64, 0xd4, 0x00, 0x00, 0x00, 0x58, 0x58, 0xc6, 0x41, 0xaf, 0xea, 0x94, 0xef, 0x29, 0x6d
.byte 0x27, 0x95, 0x1b, 0xc3, 0xf8, 0xca, 0x99, 0x32, 0x31, 0x20, 0x3e, 0x30, 0x68, 0x79, 0xb1, 0x7e
.byte 0x1d, 0xa9, 0xfa, 0x26, 0x53, 0x16, 0xc7, 0x96, 0x1e, 0xbf, 0x54, 0xa4, 0xf8, 0x8c, 0xa7, 0xe6
.byte 0x23, 0x30, 0xa8, 0x76, 0x8d, 0xbb, 0x98, 0xe7, 0x50, 0x82, 0xf2, 0xe9, 0x2f, 0x7b, 0x9d, 0x97
.byte 0xf2, 0x5a, 0x51, 0xfc, 0x8f, 0x9f, 0x8e, 0x10, 0x0d, 0x97, 0xd5, 0xdc, 0x82, 0x95, 0x71, 0x42
.byte 0xc5, 0x6f, 0x60, 0x05, 0xfb, 0x0b, 0xf5, 0x25, 0x15, 0x4e, 0x1e, 0x62, 0x4f, 0xa5, 0x31, 0x00
.byte 0x00, 0x00, 0x4a, 0x58, 0x22, 0xa2, 0x16, 0xfe, 0xb1, 0xcd, 0xc9, 0x97, 0x74, 0x9e, 0xfd, 0x3f
.byte 0x07, 0x92, 0x53, 0x41, 0x4a, 0xc1, 0xb5, 0x60, 0xa2, 0xe4, 0xe5, 0xd3, 0xd7, 0x31, 0x14, 0x14
.byte 0x08, 0x99, 0xda, 0x29, 0x8b, 0x56, 0x3c, 0xb9, 0x14, 0x6f, 0xd5, 0x78, 0x5f, 0xe2, 0x81, 0x5d
.byte 0xb7, 0xdd, 0xd0, 0x99, 0x09, 0x4e, 0x16, 0x72, 0x00, 0xff, 0xa1, 0xae, 0xd7, 0x59, 0x10, 0xcc
.byte 0xf3, 0xb3, 0xda, 0xd3, 0xc9, 0x40, 0x50, 0x46, 0x5c, 0xac, 0xfb, 0x7f, 0x6b, 0x00, 0x00, 0x00
.byte 0x87, 0x58, 0xe7, 0xe5, 0x5f, 0x81, 0x36, 0xa4, 0x5e, 0xa5, 0x7c, 0x35, 0x32, 0xcd, 0x81, 0x3c
.byte 0xdd, 0x3f, 0x4b, 0x22, 0x07, 0xf7, 0x1b, 0x4e, 0x50, 0x3c, 0xff, 0x0c, 0x9f, 0x5e, 0x0a, 0xad
.byte 0x60, 0xfb, 0x48, 0x3f, 0x8a, 0xd2, 0x39, 0xba, 0x41, 0xb7, 0x33, 0xd6, 0xe1, 0x0e, 0xea, 0x44
.byte 0x1e, 0xcc, 0xa0, 0xd4, 0x3d, 0x5e, 0x28, 0xa4, 0x19, 0x2f, 0x34, 0x53, 0xa2, 0x11, 0x7b, 0xff
.byte 0x30, 0xd0, 0x44, 0x3f, 0x40, 0x38, 0x35, 0xc7, 0x32, 0x7f, 0x54, 0xc7, 0xcc, 0x59, 0x3e, 0x34
.byte 0x63, 0x89, 0x86, 0x5b, 0x75, 0x77, 0xf7, 0xbc, 0xc0, 0x4d, 0xc8, 0x73, 0x54, 0xd9, 0x46, 0xa0
.byte 0xdb, 0xf9, 0x98, 0x23, 0xa1, 0x05, 0x3f, 0x06, 0xb6, 0x3c, 0xcb, 0xe5, 0xd4, 0x7b, 0x02, 0x2b
.byte 0x1d, 0xcb, 0x35, 0x87, 0x9f, 0xb5, 0xd3, 0xf6, 0x76, 0x1c, 0x28, 0x7e, 0xf4, 0xfc, 0x6e, 0xd9
.byte 0x69, 0x51, 0x72, 0xd3, 0x08, 0x35, 0xa0, 0x43, 0x00, 0x00, 0x00, 0x8c, 0x58, 0xe7, 0x57, 0xbd
.byte 0x31, 0x80, 0x60, 0x88, 0x56, 0x56, 0x86, 0x1a, 0xa2, 0x11, 0xbd, 0xf3, 0x69, 0x8d, 0x34, 0x60
.byte 0x1d, 0x2c, 0xeb, 0x4f, 0x33, 0xb6, 0x27, 0x9d, 0x61, 0x9e, 0xf2, 0x8d, 0x35, 0x22, 0x12, 0x47
.byte 0x5e, 0x42, 0x3e, 0xe7, 0x24, 0xda, 0x08, 0x70, 0xfe, 0xe5, 0xac, 0x3b, 0x85, 0x51, 0x53, 0x31
.byte 0x3c, 0xbb, 0x43, 0x15, 0xeb, 0x10, 0x1d, 0xe6, 0x83, 0x3b, 0x69, 0x1f, 0xb2, 0xde, 0x76, 0x3e
.byte 0x2f, 0xd0, 0x53, 0x62, 0xa8, 0x51, 0xb3, 0x30, 0x4c, 0x15, 0x47, 0xae, 0xba, 0x60, 0x8d, 0xdb
.byte 0x83, 0xfc, 0x4d, 0x81, 0x66, 0x94, 0x10, 0x08, 0x25, 0x97, 0x44, 0x88, 0xb4, 0x42, 0xb5, 0x50
.byte 0xf5, 0x47, 0x94, 0xd9, 0xdb, 0x38, 0xa5, 0xfe, 0x6f, 0xc9, 0xad, 0x44, 0x4d, 0xa1, 0x24, 0x20
.byte 0xf5, 0x7b, 0x20, 0x9b, 0x91, 0xeb, 0x91, 0x25, 0xaa, 0xf8, 0x65, 0x4f, 0x0c, 0xdf, 0x3f, 0xf0
.byte 0xd6, 0xac, 0xdf, 0x59, 0xb3, 0x29, 0xc7, 0x40, 0x00, 0x00, 0x00, 0x5c, 0x58, 0xc6, 0x9f, 0xf2
.byte 0x85, 0x59, 0x9d, 0xce, 0x80, 0x36, 0x9e, 0xaa, 0xd9, 0x4f, 0x3b, 0x47, 0xe0, 0xb2, 0x85, 0xa0
.byte 0xd1, 0xff, 0x17, 0xb9, 0xae, 0x5c, 0x08, 0x43, 0x00, 0xc3, 0x0b, 0x7c, 0x6c, 0x32, 0x06, 0x19
.byte 0x4d, 0x9e, 0xcb, 0x2d, 0x80, 0x29, 0x11, 0xf2, 0xaf, 0x82, 0x64, 0x9f, 0x75, 0x39, 0xcb, 0x86
.byte 0x52, 0xd7, 0x6b, 0xac, 0x07, 0x5f, 0x9a, 0xa9, 0x07, 0x17, 0x57, 0x5d, 0xf4, 0xde, 0x2b, 0xf2
.byte 0xd5, 0x5a, 0x9b, 0x23, 0xeb, 0x91, 0x4a, 0x3f, 0xbd, 0x5d, 0xa4, 0x9c, 0x7b, 0xfb, 0x53, 0xdf
.byte 0x2e, 0xb4, 0xe3, 0x36, 0x72, 0x9f, 0x87, 0xf8, 0x00, 0x00, 0x00, 0x4a, 0x58, 0x23, 0x09, 0xbe
.byte 0xa3, 0x2d, 0x78, 0xc0, 0x10, 0x00, 0xde, 0xb8, 0xbe, 0xdc, 0xe3, 0x32, 0xe4, 0x46, 0x53, 0x0c
.byte 0xd5, 0x75, 0x77, 0x54, 0x91, 0xdf, 0x5c, 0x57, 0xab, 0x25, 0xa2, 0x58, 0xf1, 0x56, 0x32, 0xc2
.byte 0x4d, 0xfd, 0x18, 0x8d, 0x95, 0xb9, 0xe3, 0xe7, 0xf5, 0x4e, 0x8c, 0xb0, 0x88, 0xcb, 0xfb, 0x82
.byte 0xba, 0xb8, 0x97, 0x7d, 0x93, 0xbe, 0x59, 0xba, 0xa1, 0x93, 0x1e, 0xb7, 0x7b, 0xe1, 0x4b, 0xaa
.byte 0x94, 0x2f, 0x5a, 0xdd, 0x8f, 0xa7, 0x00, 0x00, 0x00, 0x92, 0x58, 0xe8, 0x18, 0x25, 0xd9, 0xfe
.byte 0x39, 0x27, 0x35, 0xcc, 0x2b, 0x66, 0x2f, 0xac, 0x01, 0xfe, 0x3f, 0x31, 0x9e, 0x9a, 0xe9, 0x00
.byte 0x4b, 0xdd, 0x75, 0xd5, 0xd2, 0x6c, 0xcd, 0x50, 0xc7, 0x26, 0x73, 0x87, 0x58, 0x8e, 0x07, 0x0a
.byte 0xdf, 0x2b, 0x9b, 0xfe, 0x24, 0x0b, 0x6a, 0xd6, 0x06, 0xd3, 0x51, 0x40, 0xfb, 0xc6, 0x14, 0xbd
.byte 0xe7, 0xb4, 0xd7, 0x8d, 0x7b, 0xc4, 0x6c, 0x04, 0x23, 0x7a, 0xd1, 0x23, 0x3d, 0xbb, 0x10, 0x48
.byte 0xd4, 0xd4, 0x0b, 0xce, 0xa9, 0x48, 0x72, 0xbf, 0xd1, 0xee, 0x05, 0x63, 0xc1, 0x2e, 0xd2, 0xae
.byte 0x13, 0xf9, 0xfc, 0xe1, 0x82, 0x3d, 0x38, 0xd0, 0x43, 0xd7, 0x4b, 0x94, 0x31, 0x89, 0x20, 0x91
.byte 0x6c, 0x1c, 0x3d, 0x13, 0x1b, 0x41, 0x75, 0x01, 0x3e, 0x82, 0xca, 0xcf, 0x4d, 0xbd, 0x66, 0x37
.byte 0x45, 0x35, 0xe4, 0x4b, 0xb6, 0xd9, 0xfc, 0x44, 0x76, 0xe0, 0xf3, 0x90, 0x77, 0xd1, 0xac, 0xb4
.byte 0x6c, 0x2e, 0x3e, 0x1d, 0xb1, 0xb4, 0x74, 0x7c, 0xd6, 0x2e, 0x14, 0x40, 0x00, 0x00, 0x00, 0x71
.byte 0x58, 0xe7, 0xad, 0xc9, 0xa5, 0x5e, 0xe3, 0xc6, 0x33, 0x12, 0x08, 0xf5, 0x9c, 0x13, 0x20, 0x01
.byte 0x48, 0x84, 0xdb, 0x21, 0xd0, 0xcc, 0x49, 0xfc, 0xc3, 0x2b, 0x99, 0x54, 0x55, 0x26, 0x22, 0x86
.byte 0x85, 0x0c, 0x30, 0xb8, 0x4e, 0xb9, 0x20, 0xe1, 0xbc, 0x9e, 0xe2, 0x6f, 0xb9, 0x2c, 0x3f, 0x83
.byte 0x52, 0xfb, 0x43, 0x87, 0x7a, 0x52, 0x53, 0xba, 0x80, 0x18, 0x2d, 0x4b, 0xea, 0x6a, 0x2a, 0x77
.byte 0xd8, 0xe3, 0x7a, 0x23, 0xc2, 0x8d, 0xe1, 0x49, 0xaa, 0x75, 0x60, 0x9d, 0xfe, 0x08, 0x70, 0x41
.byte 0xd8, 0xb8, 0x72, 0x0d, 0xeb, 0xd1, 0x55, 0x47, 0x0e, 0x32, 0x85, 0xae, 0x04, 0x74, 0x6e, 0x07
.byte 0x0c, 0xf4, 0x7d, 0x8d, 0x68, 0x41, 0x36, 0x64, 0xf7, 0x07, 0x68, 0x3b, 0x12, 0x94, 0xb8, 0x49
.byte 0x80, 0x00, 0x00, 0x00, 0x80, 0x58, 0xe7, 0x57, 0xbd, 0x31, 0x81, 0x5f, 0x04, 0x0c, 0x49, 0x9e
.byte 0x4f, 0x96, 0x16, 0x75, 0xef, 0xaa, 0xf2, 0x16, 0x0b, 0x04, 0x2f, 0xea, 0x73, 0xcf, 0x9a, 0x0b
.byte 0x48, 0x9f, 0x67, 0x7c, 0x53, 0x7a, 0x61, 0xc2, 0x6e, 0x35, 0xa2, 0x9a, 0xc5, 0x9d, 0x33, 0x75
.byte 0x1e, 0xdd, 0x91, 0xaf, 0x09, 0x73, 0xf6, 0x70, 0x58, 0x09, 0xa3, 0x4d, 0x4c, 0x56, 0x41, 0x4b
.byte 0x04, 0x62, 0x25, 0x12, 0x44, 0x93, 0xa7, 0x33, 0x4c, 0x61, 0xc6, 0x1d, 0x1b, 0x2b, 0xb7, 0xc7
.byte 0xb5, 0x7d, 0xa4, 0xc1, 0x81, 0x6a, 0x71, 0x98, 0xf2, 0xfe, 0xa5, 0x19, 0x9d, 0x7e, 0x0b, 0xc4
.byte 0x32, 0x1d, 0xd1, 0xa0, 0xf2, 0x94, 0xde, 0x15, 0x55, 0x15, 0x50, 0x12, 0xaa, 0xe8, 0xac, 0x22
.byte 0x23, 0xf5, 0xaa, 0x01, 0x7c, 0x0f, 0xc9, 0xf8, 0xfe, 0x9a, 0x8b, 0x55, 0xfe, 0x5d, 0x79, 0xc3
.byte 0xd9, 0x7d, 0xf8, 0xfd, 0x3c, 0x00, 0x00, 0x00, 0x7d, 0x58, 0xe7, 0x5b, 0xb1, 0xa5, 0x64, 0x86
.byte 0x72, 0x3f, 0xb1, 0x58, 0xa2, 0xab, 0x6a, 0x3f, 0x25, 0xa4, 0xeb, 0xbe, 0x0d, 0xc8, 0xae, 0x10
.byte 0xd4, 0x00, 0x98, 0x94, 0xc3, 0x98, 0x24, 0x18, 0x96, 0xdf, 0xc7, 0x6f, 0xe7, 0x40, 0xbf, 0x08
.byte 0x25, 0xf4, 0x7d, 0x9b, 0x33, 0x54, 0x5a, 0x28, 0x94, 0x39, 0x7f, 0xfb, 0x4c, 0x46, 0xae, 0x17
.byte 0x45, 0xa9, 0xd2, 0xee, 0xa1, 0x00, 0xd3, 0x37, 0x3c, 0x3d, 0x19, 0xb0, 0x44, 0x09, 0x85, 0x03
.byte 0xdc, 0x9a, 0x4f, 0x64, 0x51, 0x58, 0x9b, 0x86, 0x8f, 0xda, 0x72, 0x03, 0xe1, 0x5d, 0x0c, 0x05
.byte 0xe0, 0x61, 0x02, 0x0f, 0xd2, 0xd7, 0x40, 0x13, 0xaa, 0x45, 0xa3, 0x90, 0x3e, 0xa9, 0x71, 0x12
.byte 0x43, 0x78, 0x04, 0x17, 0x83, 0xab, 0x59, 0xf6, 0xec, 0x14, 0x56, 0x42, 0x0e, 0x4a, 0x39, 0xf6
.byte 0xde, 0x1d, 0xfd, 0x20, 0xd6, 0x20

.global _binary_exclamation_p3_end
_binary_exclamation_p3_end: /* for objcopy compatibility */


.global exclamation_p3_length
exclamation_p3_length:
.long 1702
