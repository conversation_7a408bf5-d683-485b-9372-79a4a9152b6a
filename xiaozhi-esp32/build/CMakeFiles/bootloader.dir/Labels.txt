# Target labels
 bootloader
# Source files and their labels
/home/<USER>/bysx/xiaozhi-esp32/build/CMakeFiles/bootloader
/home/<USER>/bysx/xiaozhi-esp32/build/CMakeFiles/bootloader.rule
/home/<USER>/bysx/xiaozhi-esp32/build/CMakeFiles/bootloader-complete.rule
/home/<USER>/bysx/xiaozhi-esp32/build/bootloader-prefix/src/bootloader-stamp/bootloader-build.rule
/home/<USER>/bysx/xiaozhi-esp32/build/bootloader-prefix/src/bootloader-stamp/bootloader-configure.rule
/home/<USER>/bysx/xiaozhi-esp32/build/bootloader-prefix/src/bootloader-stamp/bootloader-download.rule
/home/<USER>/bysx/xiaozhi-esp32/build/bootloader-prefix/src/bootloader-stamp/bootloader-install.rule
/home/<USER>/bysx/xiaozhi-esp32/build/bootloader-prefix/src/bootloader-stamp/bootloader-mkdir.rule
/home/<USER>/bysx/xiaozhi-esp32/build/bootloader-prefix/src/bootloader-stamp/bootloader-patch.rule
/home/<USER>/bysx/xiaozhi-esp32/build/bootloader-prefix/src/bootloader-stamp/bootloader-update.rule
