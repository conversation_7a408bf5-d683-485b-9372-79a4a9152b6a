{"sources": [{"file": "/home/<USER>/bysx/xiaozhi-esp32/build/CMakeFiles/bootloader"}, {"file": "/home/<USER>/bysx/xiaozhi-esp32/build/CMakeFiles/bootloader.rule"}, {"file": "/home/<USER>/bysx/xiaozhi-esp32/build/CMakeFiles/bootloader-complete.rule"}, {"file": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader-prefix/src/bootloader-stamp/bootloader-build.rule"}, {"file": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader-prefix/src/bootloader-stamp/bootloader-configure.rule"}, {"file": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader-prefix/src/bootloader-stamp/bootloader-download.rule"}, {"file": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader-prefix/src/bootloader-stamp/bootloader-install.rule"}, {"file": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader-prefix/src/bootloader-stamp/bootloader-mkdir.rule"}, {"file": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader-prefix/src/bootloader-stamp/bootloader-patch.rule"}, {"file": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader-prefix/src/bootloader-stamp/bootloader-update.rule"}], "target": {"labels": ["bootloader"], "name": "bootloader"}}