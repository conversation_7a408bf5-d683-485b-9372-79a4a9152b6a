[{"directory": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader", "command": "/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -I/home/<USER>/esp32/esp-idf/components/xtensa/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/xtensa/include -I/home/<USER>/esp32/esp-idf/components/xtensa/deprecated_include -I/home/<USER>/bysx/xiaozhi-esp32/build/bootloader/config -I/home/<USER>/esp32/esp-idf/components/log/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_common/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/dma/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/ldo/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/debug_probe/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/newlib/platform_include -I/home/<USER>/esp32/esp-idf/components/soc/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/register -I/home/<USER>/esp32/esp-idf/components/bootloader/subproject/components/micro-ecc -I/home/<USER>/esp32/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -I/home/<USER>/esp32/esp-idf/components/hal/platform_port/include -I/home/<USER>/esp32/esp-idf/components/hal/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/hal/include -I/home/<USER>/esp32/esp-idf/components/spi_flash/include -I/home/<USER>/esp32/esp-idf/components/esp_bootloader_format/include -I/home/<USER>/esp32/esp-idf/components/esp_app_format/include -I/home/<USER>/esp32/esp-idf/components/bootloader_support/include -I/home/<USER>/esp32/esp-idf/components/bootloader_support/bootloader_flash/include -I/home/<USER>/esp32/esp-idf/components/bootloader_support/private_include -I/home/<USER>/esp32/esp-idf/components/efuse/include -I/home/<USER>/esp32/esp-idf/components/efuse/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_security/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -o CMakeFiles/bootloader.elf.dir/project_elf_src_esp32s3.c.obj -c /home/<USER>/bysx/xiaozhi-esp32/build/bootloader/project_elf_src_esp32s3.c", "file": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader/project_elf_src_esp32s3.c"}, {"directory": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader", "command": "/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.1-dirty\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/home/<USER>/bysx/xiaozhi-esp32/build/bootloader/config -I/home/<USER>/esp32/esp-idf/components/xtensa/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/xtensa/include -I/home/<USER>/esp32/esp-idf/components/xtensa/deprecated_include -I/home/<USER>/esp32/esp-idf/components/log/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_common/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/dma/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/ldo/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/debug_probe/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/newlib/platform_include -I/home/<USER>/esp32/esp-idf/components/soc/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/register -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -O2 -fmacro-prefix-map=/home/<USER>/esp32/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=/home/<USER>/esp32/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/eri.c.obj -c /home/<USER>/esp32/esp-idf/components/xtensa/eri.c", "file": "/home/<USER>/esp32/esp-idf/components/xtensa/eri.c"}, {"directory": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader", "command": "/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.1-dirty\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/home/<USER>/bysx/xiaozhi-esp32/build/bootloader/config -I/home/<USER>/esp32/esp-idf/components/xtensa/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/xtensa/include -I/home/<USER>/esp32/esp-idf/components/xtensa/deprecated_include -I/home/<USER>/esp32/esp-idf/components/log/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_common/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/dma/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/ldo/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/debug_probe/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/newlib/platform_include -I/home/<USER>/esp32/esp-idf/components/soc/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/register -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -O2 -fmacro-prefix-map=/home/<USER>/esp32/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=/home/<USER>/esp32/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/xt_trax.c.obj -c /home/<USER>/esp32/esp-idf/components/xtensa/xt_trax.c", "file": "/home/<USER>/esp32/esp-idf/components/xtensa/xt_trax.c"}, {"directory": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader", "command": "/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.1-dirty\\\" -DN<PERSON>_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/home/<USER>/bysx/xiaozhi-esp32/build/bootloader/config -I/home/<USER>/esp32/esp-idf/components/soc/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/register -I/home/<USER>/esp32/esp-idf/components/log/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_common/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/dma/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/ldo/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/debug_probe/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/newlib/platform_include -I/home/<USER>/esp32/esp-idf/components/xtensa/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/xtensa/include -I/home/<USER>/esp32/esp-idf/components/xtensa/deprecated_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -O2 -fmacro-prefix-map=/home/<USER>/esp32/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=/home/<USER>/esp32/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/soc/CMakeFiles/__idf_soc.dir/lldesc.c.obj -c /home/<USER>/esp32/esp-idf/components/soc/lldesc.c", "file": "/home/<USER>/esp32/esp-idf/components/soc/lldesc.c"}, {"directory": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader", "command": "/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.1-dirty\\\" -DN<PERSON>_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/home/<USER>/bysx/xiaozhi-esp32/build/bootloader/config -I/home/<USER>/esp32/esp-idf/components/soc/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/register -I/home/<USER>/esp32/esp-idf/components/log/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_common/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/dma/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/ldo/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/debug_probe/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/newlib/platform_include -I/home/<USER>/esp32/esp-idf/components/xtensa/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/xtensa/include -I/home/<USER>/esp32/esp-idf/components/xtensa/deprecated_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -O2 -fmacro-prefix-map=/home/<USER>/esp32/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=/home/<USER>/esp32/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/soc/CMakeFiles/__idf_soc.dir/dport_access_common.c.obj -c /home/<USER>/esp32/esp-idf/components/soc/dport_access_common.c", "file": "/home/<USER>/esp32/esp-idf/components/soc/dport_access_common.c"}, {"directory": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader", "command": "/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.1-dirty\\\" -DN<PERSON>_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/home/<USER>/bysx/xiaozhi-esp32/build/bootloader/config -I/home/<USER>/esp32/esp-idf/components/soc/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/register -I/home/<USER>/esp32/esp-idf/components/log/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_common/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/dma/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/ldo/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/debug_probe/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/newlib/platform_include -I/home/<USER>/esp32/esp-idf/components/xtensa/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/xtensa/include -I/home/<USER>/esp32/esp-idf/components/xtensa/deprecated_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -O2 -fmacro-prefix-map=/home/<USER>/esp32/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=/home/<USER>/esp32/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/interrupts.c.obj -c /home/<USER>/esp32/esp-idf/components/soc/esp32s3/interrupts.c", "file": "/home/<USER>/esp32/esp-idf/components/soc/esp32s3/interrupts.c"}, {"directory": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader", "command": "/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.1-dirty\\\" -DN<PERSON>_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/home/<USER>/bysx/xiaozhi-esp32/build/bootloader/config -I/home/<USER>/esp32/esp-idf/components/soc/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/register -I/home/<USER>/esp32/esp-idf/components/log/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_common/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/dma/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/ldo/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/debug_probe/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/newlib/platform_include -I/home/<USER>/esp32/esp-idf/components/xtensa/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/xtensa/include -I/home/<USER>/esp32/esp-idf/components/xtensa/deprecated_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -O2 -fmacro-prefix-map=/home/<USER>/esp32/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=/home/<USER>/esp32/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/gpio_periph.c.obj -c /home/<USER>/esp32/esp-idf/components/soc/esp32s3/gpio_periph.c", "file": "/home/<USER>/esp32/esp-idf/components/soc/esp32s3/gpio_periph.c"}, {"directory": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader", "command": "/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.1-dirty\\\" -DN<PERSON>_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/home/<USER>/bysx/xiaozhi-esp32/build/bootloader/config -I/home/<USER>/esp32/esp-idf/components/soc/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/register -I/home/<USER>/esp32/esp-idf/components/log/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_common/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/dma/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/ldo/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/debug_probe/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/newlib/platform_include -I/home/<USER>/esp32/esp-idf/components/xtensa/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/xtensa/include -I/home/<USER>/esp32/esp-idf/components/xtensa/deprecated_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -O2 -fmacro-prefix-map=/home/<USER>/esp32/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=/home/<USER>/esp32/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/uart_periph.c.obj -c /home/<USER>/esp32/esp-idf/components/soc/esp32s3/uart_periph.c", "file": "/home/<USER>/esp32/esp-idf/components/soc/esp32s3/uart_periph.c"}, {"directory": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader", "command": "/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.1-dirty\\\" -DN<PERSON>_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/home/<USER>/bysx/xiaozhi-esp32/build/bootloader/config -I/home/<USER>/esp32/esp-idf/components/soc/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/register -I/home/<USER>/esp32/esp-idf/components/log/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_common/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/dma/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/ldo/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/debug_probe/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/newlib/platform_include -I/home/<USER>/esp32/esp-idf/components/xtensa/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/xtensa/include -I/home/<USER>/esp32/esp-idf/components/xtensa/deprecated_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -O2 -fmacro-prefix-map=/home/<USER>/esp32/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=/home/<USER>/esp32/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/adc_periph.c.obj -c /home/<USER>/esp32/esp-idf/components/soc/esp32s3/adc_periph.c", "file": "/home/<USER>/esp32/esp-idf/components/soc/esp32s3/adc_periph.c"}, {"directory": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader", "command": "/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.1-dirty\\\" -DN<PERSON>_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/home/<USER>/bysx/xiaozhi-esp32/build/bootloader/config -I/home/<USER>/esp32/esp-idf/components/soc/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/register -I/home/<USER>/esp32/esp-idf/components/log/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_common/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/dma/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/ldo/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/debug_probe/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/newlib/platform_include -I/home/<USER>/esp32/esp-idf/components/xtensa/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/xtensa/include -I/home/<USER>/esp32/esp-idf/components/xtensa/deprecated_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -O2 -fmacro-prefix-map=/home/<USER>/esp32/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=/home/<USER>/esp32/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/dedic_gpio_periph.c.obj -c /home/<USER>/esp32/esp-idf/components/soc/esp32s3/dedic_gpio_periph.c", "file": "/home/<USER>/esp32/esp-idf/components/soc/esp32s3/dedic_gpio_periph.c"}, {"directory": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader", "command": "/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.1-dirty\\\" -DN<PERSON>_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/home/<USER>/bysx/xiaozhi-esp32/build/bootloader/config -I/home/<USER>/esp32/esp-idf/components/soc/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/register -I/home/<USER>/esp32/esp-idf/components/log/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_common/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/dma/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/ldo/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/debug_probe/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/newlib/platform_include -I/home/<USER>/esp32/esp-idf/components/xtensa/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/xtensa/include -I/home/<USER>/esp32/esp-idf/components/xtensa/deprecated_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -O2 -fmacro-prefix-map=/home/<USER>/esp32/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=/home/<USER>/esp32/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/gdma_periph.c.obj -c /home/<USER>/esp32/esp-idf/components/soc/esp32s3/gdma_periph.c", "file": "/home/<USER>/esp32/esp-idf/components/soc/esp32s3/gdma_periph.c"}, {"directory": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader", "command": "/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.1-dirty\\\" -DN<PERSON>_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/home/<USER>/bysx/xiaozhi-esp32/build/bootloader/config -I/home/<USER>/esp32/esp-idf/components/soc/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/register -I/home/<USER>/esp32/esp-idf/components/log/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_common/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/dma/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/ldo/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/debug_probe/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/newlib/platform_include -I/home/<USER>/esp32/esp-idf/components/xtensa/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/xtensa/include -I/home/<USER>/esp32/esp-idf/components/xtensa/deprecated_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -O2 -fmacro-prefix-map=/home/<USER>/esp32/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=/home/<USER>/esp32/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/spi_periph.c.obj -c /home/<USER>/esp32/esp-idf/components/soc/esp32s3/spi_periph.c", "file": "/home/<USER>/esp32/esp-idf/components/soc/esp32s3/spi_periph.c"}, {"directory": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader", "command": "/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.1-dirty\\\" -DN<PERSON>_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/home/<USER>/bysx/xiaozhi-esp32/build/bootloader/config -I/home/<USER>/esp32/esp-idf/components/soc/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/register -I/home/<USER>/esp32/esp-idf/components/log/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_common/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/dma/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/ldo/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/debug_probe/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/newlib/platform_include -I/home/<USER>/esp32/esp-idf/components/xtensa/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/xtensa/include -I/home/<USER>/esp32/esp-idf/components/xtensa/deprecated_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -O2 -fmacro-prefix-map=/home/<USER>/esp32/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=/home/<USER>/esp32/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/ledc_periph.c.obj -c /home/<USER>/esp32/esp-idf/components/soc/esp32s3/ledc_periph.c", "file": "/home/<USER>/esp32/esp-idf/components/soc/esp32s3/ledc_periph.c"}, {"directory": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader", "command": "/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.1-dirty\\\" -DN<PERSON>_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/home/<USER>/bysx/xiaozhi-esp32/build/bootloader/config -I/home/<USER>/esp32/esp-idf/components/soc/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/register -I/home/<USER>/esp32/esp-idf/components/log/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_common/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/dma/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/ldo/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/debug_probe/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/newlib/platform_include -I/home/<USER>/esp32/esp-idf/components/xtensa/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/xtensa/include -I/home/<USER>/esp32/esp-idf/components/xtensa/deprecated_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -O2 -fmacro-prefix-map=/home/<USER>/esp32/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=/home/<USER>/esp32/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/pcnt_periph.c.obj -c /home/<USER>/esp32/esp-idf/components/soc/esp32s3/pcnt_periph.c", "file": "/home/<USER>/esp32/esp-idf/components/soc/esp32s3/pcnt_periph.c"}, {"directory": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader", "command": "/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.1-dirty\\\" -DN<PERSON>_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/home/<USER>/bysx/xiaozhi-esp32/build/bootloader/config -I/home/<USER>/esp32/esp-idf/components/soc/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/register -I/home/<USER>/esp32/esp-idf/components/log/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_common/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/dma/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/ldo/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/debug_probe/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/newlib/platform_include -I/home/<USER>/esp32/esp-idf/components/xtensa/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/xtensa/include -I/home/<USER>/esp32/esp-idf/components/xtensa/deprecated_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -O2 -fmacro-prefix-map=/home/<USER>/esp32/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=/home/<USER>/esp32/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/rmt_periph.c.obj -c /home/<USER>/esp32/esp-idf/components/soc/esp32s3/rmt_periph.c", "file": "/home/<USER>/esp32/esp-idf/components/soc/esp32s3/rmt_periph.c"}, {"directory": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader", "command": "/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.1-dirty\\\" -DN<PERSON>_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/home/<USER>/bysx/xiaozhi-esp32/build/bootloader/config -I/home/<USER>/esp32/esp-idf/components/soc/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/register -I/home/<USER>/esp32/esp-idf/components/log/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_common/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/dma/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/ldo/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/debug_probe/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/newlib/platform_include -I/home/<USER>/esp32/esp-idf/components/xtensa/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/xtensa/include -I/home/<USER>/esp32/esp-idf/components/xtensa/deprecated_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -O2 -fmacro-prefix-map=/home/<USER>/esp32/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=/home/<USER>/esp32/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/sdm_periph.c.obj -c /home/<USER>/esp32/esp-idf/components/soc/esp32s3/sdm_periph.c", "file": "/home/<USER>/esp32/esp-idf/components/soc/esp32s3/sdm_periph.c"}, {"directory": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader", "command": "/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.1-dirty\\\" -DN<PERSON>_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/home/<USER>/bysx/xiaozhi-esp32/build/bootloader/config -I/home/<USER>/esp32/esp-idf/components/soc/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/register -I/home/<USER>/esp32/esp-idf/components/log/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_common/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/dma/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/ldo/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/debug_probe/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/newlib/platform_include -I/home/<USER>/esp32/esp-idf/components/xtensa/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/xtensa/include -I/home/<USER>/esp32/esp-idf/components/xtensa/deprecated_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -O2 -fmacro-prefix-map=/home/<USER>/esp32/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=/home/<USER>/esp32/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/i2s_periph.c.obj -c /home/<USER>/esp32/esp-idf/components/soc/esp32s3/i2s_periph.c", "file": "/home/<USER>/esp32/esp-idf/components/soc/esp32s3/i2s_periph.c"}, {"directory": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader", "command": "/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.1-dirty\\\" -DN<PERSON>_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/home/<USER>/bysx/xiaozhi-esp32/build/bootloader/config -I/home/<USER>/esp32/esp-idf/components/soc/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/register -I/home/<USER>/esp32/esp-idf/components/log/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_common/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/dma/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/ldo/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/debug_probe/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/newlib/platform_include -I/home/<USER>/esp32/esp-idf/components/xtensa/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/xtensa/include -I/home/<USER>/esp32/esp-idf/components/xtensa/deprecated_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -O2 -fmacro-prefix-map=/home/<USER>/esp32/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=/home/<USER>/esp32/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/i2c_periph.c.obj -c /home/<USER>/esp32/esp-idf/components/soc/esp32s3/i2c_periph.c", "file": "/home/<USER>/esp32/esp-idf/components/soc/esp32s3/i2c_periph.c"}, {"directory": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader", "command": "/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.1-dirty\\\" -DN<PERSON>_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/home/<USER>/bysx/xiaozhi-esp32/build/bootloader/config -I/home/<USER>/esp32/esp-idf/components/soc/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/register -I/home/<USER>/esp32/esp-idf/components/log/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_common/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/dma/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/ldo/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/debug_probe/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/newlib/platform_include -I/home/<USER>/esp32/esp-idf/components/xtensa/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/xtensa/include -I/home/<USER>/esp32/esp-idf/components/xtensa/deprecated_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -O2 -fmacro-prefix-map=/home/<USER>/esp32/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=/home/<USER>/esp32/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/temperature_sensor_periph.c.obj -c /home/<USER>/esp32/esp-idf/components/soc/esp32s3/temperature_sensor_periph.c", "file": "/home/<USER>/esp32/esp-idf/components/soc/esp32s3/temperature_sensor_periph.c"}, {"directory": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader", "command": "/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.1-dirty\\\" -DN<PERSON>_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/home/<USER>/bysx/xiaozhi-esp32/build/bootloader/config -I/home/<USER>/esp32/esp-idf/components/soc/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/register -I/home/<USER>/esp32/esp-idf/components/log/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_common/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/dma/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/ldo/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/debug_probe/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/newlib/platform_include -I/home/<USER>/esp32/esp-idf/components/xtensa/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/xtensa/include -I/home/<USER>/esp32/esp-idf/components/xtensa/deprecated_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -O2 -fmacro-prefix-map=/home/<USER>/esp32/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=/home/<USER>/esp32/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/timer_periph.c.obj -c /home/<USER>/esp32/esp-idf/components/soc/esp32s3/timer_periph.c", "file": "/home/<USER>/esp32/esp-idf/components/soc/esp32s3/timer_periph.c"}, {"directory": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader", "command": "/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.1-dirty\\\" -DN<PERSON>_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/home/<USER>/bysx/xiaozhi-esp32/build/bootloader/config -I/home/<USER>/esp32/esp-idf/components/soc/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/register -I/home/<USER>/esp32/esp-idf/components/log/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_common/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/dma/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/ldo/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/debug_probe/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/newlib/platform_include -I/home/<USER>/esp32/esp-idf/components/xtensa/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/xtensa/include -I/home/<USER>/esp32/esp-idf/components/xtensa/deprecated_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -O2 -fmacro-prefix-map=/home/<USER>/esp32/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=/home/<USER>/esp32/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/lcd_periph.c.obj -c /home/<USER>/esp32/esp-idf/components/soc/esp32s3/lcd_periph.c", "file": "/home/<USER>/esp32/esp-idf/components/soc/esp32s3/lcd_periph.c"}, {"directory": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader", "command": "/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.1-dirty\\\" -DN<PERSON>_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/home/<USER>/bysx/xiaozhi-esp32/build/bootloader/config -I/home/<USER>/esp32/esp-idf/components/soc/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/register -I/home/<USER>/esp32/esp-idf/components/log/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_common/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/dma/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/ldo/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/debug_probe/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/newlib/platform_include -I/home/<USER>/esp32/esp-idf/components/xtensa/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/xtensa/include -I/home/<USER>/esp32/esp-idf/components/xtensa/deprecated_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -O2 -fmacro-prefix-map=/home/<USER>/esp32/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=/home/<USER>/esp32/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/mcpwm_periph.c.obj -c /home/<USER>/esp32/esp-idf/components/soc/esp32s3/mcpwm_periph.c", "file": "/home/<USER>/esp32/esp-idf/components/soc/esp32s3/mcpwm_periph.c"}, {"directory": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader", "command": "/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.1-dirty\\\" -DN<PERSON>_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/home/<USER>/bysx/xiaozhi-esp32/build/bootloader/config -I/home/<USER>/esp32/esp-idf/components/soc/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/register -I/home/<USER>/esp32/esp-idf/components/log/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_common/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/dma/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/ldo/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/debug_probe/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/newlib/platform_include -I/home/<USER>/esp32/esp-idf/components/xtensa/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/xtensa/include -I/home/<USER>/esp32/esp-idf/components/xtensa/deprecated_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -O2 -fmacro-prefix-map=/home/<USER>/esp32/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=/home/<USER>/esp32/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/mpi_periph.c.obj -c /home/<USER>/esp32/esp-idf/components/soc/esp32s3/mpi_periph.c", "file": "/home/<USER>/esp32/esp-idf/components/soc/esp32s3/mpi_periph.c"}, {"directory": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader", "command": "/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.1-dirty\\\" -DN<PERSON>_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/home/<USER>/bysx/xiaozhi-esp32/build/bootloader/config -I/home/<USER>/esp32/esp-idf/components/soc/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/register -I/home/<USER>/esp32/esp-idf/components/log/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_common/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/dma/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/ldo/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/debug_probe/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/newlib/platform_include -I/home/<USER>/esp32/esp-idf/components/xtensa/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/xtensa/include -I/home/<USER>/esp32/esp-idf/components/xtensa/deprecated_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -O2 -fmacro-prefix-map=/home/<USER>/esp32/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=/home/<USER>/esp32/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/sdmmc_periph.c.obj -c /home/<USER>/esp32/esp-idf/components/soc/esp32s3/sdmmc_periph.c", "file": "/home/<USER>/esp32/esp-idf/components/soc/esp32s3/sdmmc_periph.c"}, {"directory": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader", "command": "/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.1-dirty\\\" -DN<PERSON>_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/home/<USER>/bysx/xiaozhi-esp32/build/bootloader/config -I/home/<USER>/esp32/esp-idf/components/soc/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/register -I/home/<USER>/esp32/esp-idf/components/log/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_common/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/dma/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/ldo/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/debug_probe/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/newlib/platform_include -I/home/<USER>/esp32/esp-idf/components/xtensa/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/xtensa/include -I/home/<USER>/esp32/esp-idf/components/xtensa/deprecated_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -O2 -fmacro-prefix-map=/home/<USER>/esp32/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=/home/<USER>/esp32/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/touch_sensor_periph.c.obj -c /home/<USER>/esp32/esp-idf/components/soc/esp32s3/touch_sensor_periph.c", "file": "/home/<USER>/esp32/esp-idf/components/soc/esp32s3/touch_sensor_periph.c"}, {"directory": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader", "command": "/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.1-dirty\\\" -DN<PERSON>_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/home/<USER>/bysx/xiaozhi-esp32/build/bootloader/config -I/home/<USER>/esp32/esp-idf/components/soc/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/register -I/home/<USER>/esp32/esp-idf/components/log/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_common/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/dma/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/ldo/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/debug_probe/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/newlib/platform_include -I/home/<USER>/esp32/esp-idf/components/xtensa/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/xtensa/include -I/home/<USER>/esp32/esp-idf/components/xtensa/deprecated_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -O2 -fmacro-prefix-map=/home/<USER>/esp32/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=/home/<USER>/esp32/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/twai_periph.c.obj -c /home/<USER>/esp32/esp-idf/components/soc/esp32s3/twai_periph.c", "file": "/home/<USER>/esp32/esp-idf/components/soc/esp32s3/twai_periph.c"}, {"directory": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader", "command": "/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.1-dirty\\\" -DN<PERSON>_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/home/<USER>/bysx/xiaozhi-esp32/build/bootloader/config -I/home/<USER>/esp32/esp-idf/components/soc/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/register -I/home/<USER>/esp32/esp-idf/components/log/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_common/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/dma/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/ldo/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/debug_probe/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/newlib/platform_include -I/home/<USER>/esp32/esp-idf/components/xtensa/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/xtensa/include -I/home/<USER>/esp32/esp-idf/components/xtensa/deprecated_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -O2 -fmacro-prefix-map=/home/<USER>/esp32/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=/home/<USER>/esp32/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/wdt_periph.c.obj -c /home/<USER>/esp32/esp-idf/components/soc/esp32s3/wdt_periph.c", "file": "/home/<USER>/esp32/esp-idf/components/soc/esp32s3/wdt_periph.c"}, {"directory": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader", "command": "/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.1-dirty\\\" -DN<PERSON>_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/home/<USER>/bysx/xiaozhi-esp32/build/bootloader/config -I/home/<USER>/esp32/esp-idf/components/soc/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/register -I/home/<USER>/esp32/esp-idf/components/log/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_common/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/dma/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/ldo/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/debug_probe/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/newlib/platform_include -I/home/<USER>/esp32/esp-idf/components/xtensa/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/xtensa/include -I/home/<USER>/esp32/esp-idf/components/xtensa/deprecated_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -O2 -fmacro-prefix-map=/home/<USER>/esp32/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=/home/<USER>/esp32/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/usb_dwc_periph.c.obj -c /home/<USER>/esp32/esp-idf/components/soc/esp32s3/usb_dwc_periph.c", "file": "/home/<USER>/esp32/esp-idf/components/soc/esp32s3/usb_dwc_periph.c"}, {"directory": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader", "command": "/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.1-dirty\\\" -DN<PERSON>_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/home/<USER>/bysx/xiaozhi-esp32/build/bootloader/config -I/home/<USER>/esp32/esp-idf/components/soc/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/register -I/home/<USER>/esp32/esp-idf/components/log/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_common/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/dma/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/ldo/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/debug_probe/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/newlib/platform_include -I/home/<USER>/esp32/esp-idf/components/xtensa/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/xtensa/include -I/home/<USER>/esp32/esp-idf/components/xtensa/deprecated_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -O2 -fmacro-prefix-map=/home/<USER>/esp32/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=/home/<USER>/esp32/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/rtc_io_periph.c.obj -c /home/<USER>/esp32/esp-idf/components/soc/esp32s3/rtc_io_periph.c", "file": "/home/<USER>/esp32/esp-idf/components/soc/esp32s3/rtc_io_periph.c"}, {"directory": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader", "command": "/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.1-dirty\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/home/<USER>/bysx/xiaozhi-esp32/build/bootloader/config -I/home/<USER>/esp32/esp-idf/components/bootloader/subproject/components/micro-ecc -I/home/<USER>/esp32/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -I/home/<USER>/esp32/esp-idf/components/log/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_common/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/dma/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/ldo/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/debug_probe/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/newlib/platform_include -I/home/<USER>/esp32/esp-idf/components/xtensa/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/xtensa/include -I/home/<USER>/esp32/esp-idf/components/xtensa/deprecated_include -I/home/<USER>/esp32/esp-idf/components/soc/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/register -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -O2 -fmacro-prefix-map=/home/<USER>/esp32/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=/home/<USER>/esp32/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/micro-ecc/CMakeFiles/__idf_micro-ecc.dir/uECC_verify_antifault.c.obj -c /home/<USER>/esp32/esp-idf/components/bootloader/subproject/components/micro-ecc/uECC_verify_antifault.c", "file": "/home/<USER>/esp32/esp-idf/components/bootloader/subproject/components/micro-ecc/uECC_verify_antifault.c"}, {"directory": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader", "command": "/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.1-dirty\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/home/<USER>/bysx/xiaozhi-esp32/build/bootloader/config -I/home/<USER>/esp32/esp-idf/components/hal/platform_port/include -I/home/<USER>/esp32/esp-idf/components/hal/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/hal/include -I/home/<USER>/esp32/esp-idf/components/log/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_common/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/dma/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/ldo/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/debug_probe/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/newlib/platform_include -I/home/<USER>/esp32/esp-idf/components/xtensa/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/xtensa/include -I/home/<USER>/esp32/esp-idf/components/xtensa/deprecated_include -I/home/<USER>/esp32/esp-idf/components/soc/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/register -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -O2 -fmacro-prefix-map=/home/<USER>/esp32/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=/home/<USER>/esp32/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/hal/CMakeFiles/__idf_hal.dir/hal_utils.c.obj -c /home/<USER>/esp32/esp-idf/components/hal/hal_utils.c", "file": "/home/<USER>/esp32/esp-idf/components/hal/hal_utils.c"}, {"directory": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader", "command": "/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.1-dirty\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/home/<USER>/bysx/xiaozhi-esp32/build/bootloader/config -I/home/<USER>/esp32/esp-idf/components/hal/platform_port/include -I/home/<USER>/esp32/esp-idf/components/hal/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/hal/include -I/home/<USER>/esp32/esp-idf/components/log/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_common/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/dma/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/ldo/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/debug_probe/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/newlib/platform_include -I/home/<USER>/esp32/esp-idf/components/xtensa/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/xtensa/include -I/home/<USER>/esp32/esp-idf/components/xtensa/deprecated_include -I/home/<USER>/esp32/esp-idf/components/soc/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/register -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -O2 -fmacro-prefix-map=/home/<USER>/esp32/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=/home/<USER>/esp32/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/hal/CMakeFiles/__idf_hal.dir/mpu_hal.c.obj -c /home/<USER>/esp32/esp-idf/components/hal/mpu_hal.c", "file": "/home/<USER>/esp32/esp-idf/components/hal/mpu_hal.c"}, {"directory": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader", "command": "/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.1-dirty\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/home/<USER>/bysx/xiaozhi-esp32/build/bootloader/config -I/home/<USER>/esp32/esp-idf/components/hal/platform_port/include -I/home/<USER>/esp32/esp-idf/components/hal/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/hal/include -I/home/<USER>/esp32/esp-idf/components/log/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_common/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/dma/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/ldo/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/debug_probe/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/newlib/platform_include -I/home/<USER>/esp32/esp-idf/components/xtensa/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/xtensa/include -I/home/<USER>/esp32/esp-idf/components/xtensa/deprecated_include -I/home/<USER>/esp32/esp-idf/components/soc/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/register -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -O2 -fmacro-prefix-map=/home/<USER>/esp32/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=/home/<USER>/esp32/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/hal/CMakeFiles/__idf_hal.dir/efuse_hal.c.obj -c /home/<USER>/esp32/esp-idf/components/hal/efuse_hal.c", "file": "/home/<USER>/esp32/esp-idf/components/hal/efuse_hal.c"}, {"directory": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader", "command": "/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.1-dirty\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/home/<USER>/bysx/xiaozhi-esp32/build/bootloader/config -I/home/<USER>/esp32/esp-idf/components/hal/platform_port/include -I/home/<USER>/esp32/esp-idf/components/hal/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/hal/include -I/home/<USER>/esp32/esp-idf/components/log/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_common/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/dma/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/ldo/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/debug_probe/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/newlib/platform_include -I/home/<USER>/esp32/esp-idf/components/xtensa/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/xtensa/include -I/home/<USER>/esp32/esp-idf/components/xtensa/deprecated_include -I/home/<USER>/esp32/esp-idf/components/soc/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/register -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -O2 -fmacro-prefix-map=/home/<USER>/esp32/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=/home/<USER>/esp32/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/hal/CMakeFiles/__idf_hal.dir/esp32s3/efuse_hal.c.obj -c /home/<USER>/esp32/esp-idf/components/hal/esp32s3/efuse_hal.c", "file": "/home/<USER>/esp32/esp-idf/components/hal/esp32s3/efuse_hal.c"}, {"directory": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader", "command": "/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.1-dirty\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/home/<USER>/bysx/xiaozhi-esp32/build/bootloader/config -I/home/<USER>/esp32/esp-idf/components/hal/platform_port/include -I/home/<USER>/esp32/esp-idf/components/hal/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/hal/include -I/home/<USER>/esp32/esp-idf/components/log/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_common/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/dma/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/ldo/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/debug_probe/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/newlib/platform_include -I/home/<USER>/esp32/esp-idf/components/xtensa/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/xtensa/include -I/home/<USER>/esp32/esp-idf/components/xtensa/deprecated_include -I/home/<USER>/esp32/esp-idf/components/soc/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/register -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -O2 -fmacro-prefix-map=/home/<USER>/esp32/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=/home/<USER>/esp32/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/hal/CMakeFiles/__idf_hal.dir/mmu_hal.c.obj -c /home/<USER>/esp32/esp-idf/components/hal/mmu_hal.c", "file": "/home/<USER>/esp32/esp-idf/components/hal/mmu_hal.c"}, {"directory": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader", "command": "/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.1-dirty\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/home/<USER>/bysx/xiaozhi-esp32/build/bootloader/config -I/home/<USER>/esp32/esp-idf/components/hal/platform_port/include -I/home/<USER>/esp32/esp-idf/components/hal/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/hal/include -I/home/<USER>/esp32/esp-idf/components/log/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_common/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/dma/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/ldo/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/debug_probe/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/newlib/platform_include -I/home/<USER>/esp32/esp-idf/components/xtensa/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/xtensa/include -I/home/<USER>/esp32/esp-idf/components/xtensa/deprecated_include -I/home/<USER>/esp32/esp-idf/components/soc/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/register -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -O2 -fmacro-prefix-map=/home/<USER>/esp32/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=/home/<USER>/esp32/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/hal/CMakeFiles/__idf_hal.dir/cache_hal.c.obj -c /home/<USER>/esp32/esp-idf/components/hal/cache_hal.c", "file": "/home/<USER>/esp32/esp-idf/components/hal/cache_hal.c"}, {"directory": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader", "command": "/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.1-dirty\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/home/<USER>/bysx/xiaozhi-esp32/build/bootloader/config -I/home/<USER>/esp32/esp-idf/components/spi_flash/include -I/home/<USER>/esp32/esp-idf/components/spi_flash/include/spi_flash -I/home/<USER>/esp32/esp-idf/components/log/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_common/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/dma/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/ldo/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/debug_probe/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/newlib/platform_include -I/home/<USER>/esp32/esp-idf/components/xtensa/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/xtensa/include -I/home/<USER>/esp32/esp-idf/components/xtensa/deprecated_include -I/home/<USER>/esp32/esp-idf/components/soc/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/register -I/home/<USER>/esp32/esp-idf/components/hal/platform_port/include -I/home/<USER>/esp32/esp-idf/components/hal/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/hal/include -I/home/<USER>/esp32/esp-idf/components/bootloader_support/include -I/home/<USER>/esp32/esp-idf/components/bootloader_support/bootloader_flash/include -I/home/<USER>/esp32/esp-idf/components/bootloader_support/private_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -O2 -fmacro-prefix-map=/home/<USER>/esp32/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=/home/<USER>/esp32/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/spi_flash_wrap.c.obj -c /home/<USER>/esp32/esp-idf/components/spi_flash/spi_flash_wrap.c", "file": "/home/<USER>/esp32/esp-idf/components/spi_flash/spi_flash_wrap.c"}, {"directory": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader", "command": "/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.1-dirty\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/home/<USER>/bysx/xiaozhi-esp32/build/bootloader/config -I/home/<USER>/esp32/esp-idf/components/esp_bootloader_format/include -I/home/<USER>/esp32/esp-idf/components/log/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_common/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/dma/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/ldo/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/debug_probe/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/newlib/platform_include -I/home/<USER>/esp32/esp-idf/components/xtensa/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/xtensa/include -I/home/<USER>/esp32/esp-idf/components/xtensa/deprecated_include -I/home/<USER>/esp32/esp-idf/components/soc/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/register -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -O2 -fmacro-prefix-map=/home/<USER>/esp32/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=/home/<USER>/esp32/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/esp_bootloader_format/CMakeFiles/__idf_esp_bootloader_format.dir/esp_bootloader_desc.c.obj -c /home/<USER>/esp32/esp-idf/components/esp_bootloader_format/esp_bootloader_desc.c", "file": "/home/<USER>/esp32/esp-idf/components/esp_bootloader_format/esp_bootloader_desc.c"}, {"directory": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader", "command": "/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.1-dirty\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/home/<USER>/bysx/xiaozhi-esp32/build/bootloader/config -I/home/<USER>/esp32/esp-idf/components/bootloader_support/include -I/home/<USER>/esp32/esp-idf/components/bootloader_support/bootloader_flash/include -I/home/<USER>/esp32/esp-idf/components/bootloader_support/private_include -I/home/<USER>/esp32/esp-idf/components/log/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_common/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/dma/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/ldo/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/debug_probe/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/newlib/platform_include -I/home/<USER>/esp32/esp-idf/components/xtensa/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/xtensa/include -I/home/<USER>/esp32/esp-idf/components/xtensa/deprecated_include -I/home/<USER>/esp32/esp-idf/components/soc/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/register -I/home/<USER>/esp32/esp-idf/components/bootloader/subproject/components/micro-ecc -I/home/<USER>/esp32/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -I/home/<USER>/esp32/esp-idf/components/spi_flash/include -I/home/<USER>/esp32/esp-idf/components/hal/platform_port/include -I/home/<USER>/esp32/esp-idf/components/hal/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/hal/include -I/home/<USER>/esp32/esp-idf/components/efuse/include -I/home/<USER>/esp32/esp-idf/components/efuse/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_bootloader_format/include -I/home/<USER>/esp32/esp-idf/components/esp_app_format/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -O2 -fmacro-prefix-map=/home/<USER>/esp32/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=/home/<USER>/esp32/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common.c.obj -c /home/<USER>/esp32/esp-idf/components/bootloader_support/src/bootloader_common.c", "file": "/home/<USER>/esp32/esp-idf/components/bootloader_support/src/bootloader_common.c"}, {"directory": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader", "command": "/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.1-dirty\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/home/<USER>/bysx/xiaozhi-esp32/build/bootloader/config -I/home/<USER>/esp32/esp-idf/components/bootloader_support/include -I/home/<USER>/esp32/esp-idf/components/bootloader_support/bootloader_flash/include -I/home/<USER>/esp32/esp-idf/components/bootloader_support/private_include -I/home/<USER>/esp32/esp-idf/components/log/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_common/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/dma/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/ldo/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/debug_probe/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/newlib/platform_include -I/home/<USER>/esp32/esp-idf/components/xtensa/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/xtensa/include -I/home/<USER>/esp32/esp-idf/components/xtensa/deprecated_include -I/home/<USER>/esp32/esp-idf/components/soc/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/register -I/home/<USER>/esp32/esp-idf/components/bootloader/subproject/components/micro-ecc -I/home/<USER>/esp32/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -I/home/<USER>/esp32/esp-idf/components/spi_flash/include -I/home/<USER>/esp32/esp-idf/components/hal/platform_port/include -I/home/<USER>/esp32/esp-idf/components/hal/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/hal/include -I/home/<USER>/esp32/esp-idf/components/efuse/include -I/home/<USER>/esp32/esp-idf/components/efuse/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_bootloader_format/include -I/home/<USER>/esp32/esp-idf/components/esp_app_format/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -O2 -fmacro-prefix-map=/home/<USER>/esp32/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=/home/<USER>/esp32/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common_loader.c.obj -c /home/<USER>/esp32/esp-idf/components/bootloader_support/src/bootloader_common_loader.c", "file": "/home/<USER>/esp32/esp-idf/components/bootloader_support/src/bootloader_common_loader.c"}, {"directory": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader", "command": "/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.1-dirty\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/home/<USER>/bysx/xiaozhi-esp32/build/bootloader/config -I/home/<USER>/esp32/esp-idf/components/bootloader_support/include -I/home/<USER>/esp32/esp-idf/components/bootloader_support/bootloader_flash/include -I/home/<USER>/esp32/esp-idf/components/bootloader_support/private_include -I/home/<USER>/esp32/esp-idf/components/log/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_common/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/dma/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/ldo/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/debug_probe/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/newlib/platform_include -I/home/<USER>/esp32/esp-idf/components/xtensa/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/xtensa/include -I/home/<USER>/esp32/esp-idf/components/xtensa/deprecated_include -I/home/<USER>/esp32/esp-idf/components/soc/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/register -I/home/<USER>/esp32/esp-idf/components/bootloader/subproject/components/micro-ecc -I/home/<USER>/esp32/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -I/home/<USER>/esp32/esp-idf/components/spi_flash/include -I/home/<USER>/esp32/esp-idf/components/hal/platform_port/include -I/home/<USER>/esp32/esp-idf/components/hal/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/hal/include -I/home/<USER>/esp32/esp-idf/components/efuse/include -I/home/<USER>/esp32/esp-idf/components/efuse/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_bootloader_format/include -I/home/<USER>/esp32/esp-idf/components/esp_app_format/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -O2 -fmacro-prefix-map=/home/<USER>/esp32/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=/home/<USER>/esp32/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_init.c.obj -c /home/<USER>/esp32/esp-idf/components/bootloader_support/src/bootloader_clock_init.c", "file": "/home/<USER>/esp32/esp-idf/components/bootloader_support/src/bootloader_clock_init.c"}, {"directory": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader", "command": "/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.1-dirty\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/home/<USER>/bysx/xiaozhi-esp32/build/bootloader/config -I/home/<USER>/esp32/esp-idf/components/bootloader_support/include -I/home/<USER>/esp32/esp-idf/components/bootloader_support/bootloader_flash/include -I/home/<USER>/esp32/esp-idf/components/bootloader_support/private_include -I/home/<USER>/esp32/esp-idf/components/log/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_common/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/dma/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/ldo/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/debug_probe/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/newlib/platform_include -I/home/<USER>/esp32/esp-idf/components/xtensa/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/xtensa/include -I/home/<USER>/esp32/esp-idf/components/xtensa/deprecated_include -I/home/<USER>/esp32/esp-idf/components/soc/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/register -I/home/<USER>/esp32/esp-idf/components/bootloader/subproject/components/micro-ecc -I/home/<USER>/esp32/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -I/home/<USER>/esp32/esp-idf/components/spi_flash/include -I/home/<USER>/esp32/esp-idf/components/hal/platform_port/include -I/home/<USER>/esp32/esp-idf/components/hal/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/hal/include -I/home/<USER>/esp32/esp-idf/components/efuse/include -I/home/<USER>/esp32/esp-idf/components/efuse/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_bootloader_format/include -I/home/<USER>/esp32/esp-idf/components/esp_app_format/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -O2 -fmacro-prefix-map=/home/<USER>/esp32/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=/home/<USER>/esp32/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_mem.c.obj -c /home/<USER>/esp32/esp-idf/components/bootloader_support/src/bootloader_mem.c", "file": "/home/<USER>/esp32/esp-idf/components/bootloader_support/src/bootloader_mem.c"}, {"directory": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader", "command": "/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.1-dirty\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/home/<USER>/bysx/xiaozhi-esp32/build/bootloader/config -I/home/<USER>/esp32/esp-idf/components/bootloader_support/include -I/home/<USER>/esp32/esp-idf/components/bootloader_support/bootloader_flash/include -I/home/<USER>/esp32/esp-idf/components/bootloader_support/private_include -I/home/<USER>/esp32/esp-idf/components/log/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_common/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/dma/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/ldo/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/debug_probe/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/newlib/platform_include -I/home/<USER>/esp32/esp-idf/components/xtensa/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/xtensa/include -I/home/<USER>/esp32/esp-idf/components/xtensa/deprecated_include -I/home/<USER>/esp32/esp-idf/components/soc/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/register -I/home/<USER>/esp32/esp-idf/components/bootloader/subproject/components/micro-ecc -I/home/<USER>/esp32/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -I/home/<USER>/esp32/esp-idf/components/spi_flash/include -I/home/<USER>/esp32/esp-idf/components/hal/platform_port/include -I/home/<USER>/esp32/esp-idf/components/hal/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/hal/include -I/home/<USER>/esp32/esp-idf/components/efuse/include -I/home/<USER>/esp32/esp-idf/components/efuse/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_bootloader_format/include -I/home/<USER>/esp32/esp-idf/components/esp_app_format/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -O2 -fmacro-prefix-map=/home/<USER>/esp32/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=/home/<USER>/esp32/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random.c.obj -c /home/<USER>/esp32/esp-idf/components/bootloader_support/src/bootloader_random.c", "file": "/home/<USER>/esp32/esp-idf/components/bootloader_support/src/bootloader_random.c"}, {"directory": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader", "command": "/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.1-dirty\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/home/<USER>/bysx/xiaozhi-esp32/build/bootloader/config -I/home/<USER>/esp32/esp-idf/components/bootloader_support/include -I/home/<USER>/esp32/esp-idf/components/bootloader_support/bootloader_flash/include -I/home/<USER>/esp32/esp-idf/components/bootloader_support/private_include -I/home/<USER>/esp32/esp-idf/components/log/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_common/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/dma/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/ldo/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/debug_probe/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/newlib/platform_include -I/home/<USER>/esp32/esp-idf/components/xtensa/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/xtensa/include -I/home/<USER>/esp32/esp-idf/components/xtensa/deprecated_include -I/home/<USER>/esp32/esp-idf/components/soc/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/register -I/home/<USER>/esp32/esp-idf/components/bootloader/subproject/components/micro-ecc -I/home/<USER>/esp32/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -I/home/<USER>/esp32/esp-idf/components/spi_flash/include -I/home/<USER>/esp32/esp-idf/components/hal/platform_port/include -I/home/<USER>/esp32/esp-idf/components/hal/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/hal/include -I/home/<USER>/esp32/esp-idf/components/efuse/include -I/home/<USER>/esp32/esp-idf/components/efuse/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_bootloader_format/include -I/home/<USER>/esp32/esp-idf/components/esp_app_format/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -O2 -fmacro-prefix-map=/home/<USER>/esp32/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=/home/<USER>/esp32/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_efuse.c.obj -c /home/<USER>/esp32/esp-idf/components/bootloader_support/src/bootloader_efuse.c", "file": "/home/<USER>/esp32/esp-idf/components/bootloader_support/src/bootloader_efuse.c"}, {"directory": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader", "command": "/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.1-dirty\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/home/<USER>/bysx/xiaozhi-esp32/build/bootloader/config -I/home/<USER>/esp32/esp-idf/components/bootloader_support/include -I/home/<USER>/esp32/esp-idf/components/bootloader_support/bootloader_flash/include -I/home/<USER>/esp32/esp-idf/components/bootloader_support/private_include -I/home/<USER>/esp32/esp-idf/components/log/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_common/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/dma/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/ldo/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/debug_probe/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/newlib/platform_include -I/home/<USER>/esp32/esp-idf/components/xtensa/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/xtensa/include -I/home/<USER>/esp32/esp-idf/components/xtensa/deprecated_include -I/home/<USER>/esp32/esp-idf/components/soc/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/register -I/home/<USER>/esp32/esp-idf/components/bootloader/subproject/components/micro-ecc -I/home/<USER>/esp32/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -I/home/<USER>/esp32/esp-idf/components/spi_flash/include -I/home/<USER>/esp32/esp-idf/components/hal/platform_port/include -I/home/<USER>/esp32/esp-idf/components/hal/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/hal/include -I/home/<USER>/esp32/esp-idf/components/efuse/include -I/home/<USER>/esp32/esp-idf/components/efuse/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_bootloader_format/include -I/home/<USER>/esp32/esp-idf/components/esp_app_format/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -O2 -fmacro-prefix-map=/home/<USER>/esp32/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=/home/<USER>/esp32/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_encrypt.c.obj -c /home/<USER>/esp32/esp-idf/components/bootloader_support/src/flash_encrypt.c", "file": "/home/<USER>/esp32/esp-idf/components/bootloader_support/src/flash_encrypt.c"}, {"directory": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader", "command": "/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.1-dirty\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/home/<USER>/bysx/xiaozhi-esp32/build/bootloader/config -I/home/<USER>/esp32/esp-idf/components/bootloader_support/include -I/home/<USER>/esp32/esp-idf/components/bootloader_support/bootloader_flash/include -I/home/<USER>/esp32/esp-idf/components/bootloader_support/private_include -I/home/<USER>/esp32/esp-idf/components/log/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_common/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/dma/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/ldo/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/debug_probe/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/newlib/platform_include -I/home/<USER>/esp32/esp-idf/components/xtensa/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/xtensa/include -I/home/<USER>/esp32/esp-idf/components/xtensa/deprecated_include -I/home/<USER>/esp32/esp-idf/components/soc/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/register -I/home/<USER>/esp32/esp-idf/components/bootloader/subproject/components/micro-ecc -I/home/<USER>/esp32/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -I/home/<USER>/esp32/esp-idf/components/spi_flash/include -I/home/<USER>/esp32/esp-idf/components/hal/platform_port/include -I/home/<USER>/esp32/esp-idf/components/hal/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/hal/include -I/home/<USER>/esp32/esp-idf/components/efuse/include -I/home/<USER>/esp32/esp-idf/components/efuse/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_bootloader_format/include -I/home/<USER>/esp32/esp-idf/components/esp_app_format/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -O2 -fmacro-prefix-map=/home/<USER>/esp32/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=/home/<USER>/esp32/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/secure_boot.c.obj -c /home/<USER>/esp32/esp-idf/components/bootloader_support/src/secure_boot.c", "file": "/home/<USER>/esp32/esp-idf/components/bootloader_support/src/secure_boot.c"}, {"directory": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader", "command": "/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.1-dirty\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/home/<USER>/bysx/xiaozhi-esp32/build/bootloader/config -I/home/<USER>/esp32/esp-idf/components/bootloader_support/include -I/home/<USER>/esp32/esp-idf/components/bootloader_support/bootloader_flash/include -I/home/<USER>/esp32/esp-idf/components/bootloader_support/private_include -I/home/<USER>/esp32/esp-idf/components/log/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_common/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/dma/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/ldo/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/debug_probe/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/newlib/platform_include -I/home/<USER>/esp32/esp-idf/components/xtensa/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/xtensa/include -I/home/<USER>/esp32/esp-idf/components/xtensa/deprecated_include -I/home/<USER>/esp32/esp-idf/components/soc/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/register -I/home/<USER>/esp32/esp-idf/components/bootloader/subproject/components/micro-ecc -I/home/<USER>/esp32/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -I/home/<USER>/esp32/esp-idf/components/spi_flash/include -I/home/<USER>/esp32/esp-idf/components/hal/platform_port/include -I/home/<USER>/esp32/esp-idf/components/hal/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/hal/include -I/home/<USER>/esp32/esp-idf/components/efuse/include -I/home/<USER>/esp32/esp-idf/components/efuse/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_bootloader_format/include -I/home/<USER>/esp32/esp-idf/components/esp_app_format/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -O2 -fmacro-prefix-map=/home/<USER>/esp32/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=/home/<USER>/esp32/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random_esp32s3.c.obj -c /home/<USER>/esp32/esp-idf/components/bootloader_support/src/bootloader_random_esp32s3.c", "file": "/home/<USER>/esp32/esp-idf/components/bootloader_support/src/bootloader_random_esp32s3.c"}, {"directory": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader", "command": "/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.1-dirty\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/home/<USER>/bysx/xiaozhi-esp32/build/bootloader/config -I/home/<USER>/esp32/esp-idf/components/bootloader_support/include -I/home/<USER>/esp32/esp-idf/components/bootloader_support/bootloader_flash/include -I/home/<USER>/esp32/esp-idf/components/bootloader_support/private_include -I/home/<USER>/esp32/esp-idf/components/log/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_common/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/dma/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/ldo/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/debug_probe/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/newlib/platform_include -I/home/<USER>/esp32/esp-idf/components/xtensa/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/xtensa/include -I/home/<USER>/esp32/esp-idf/components/xtensa/deprecated_include -I/home/<USER>/esp32/esp-idf/components/soc/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/register -I/home/<USER>/esp32/esp-idf/components/bootloader/subproject/components/micro-ecc -I/home/<USER>/esp32/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -I/home/<USER>/esp32/esp-idf/components/spi_flash/include -I/home/<USER>/esp32/esp-idf/components/hal/platform_port/include -I/home/<USER>/esp32/esp-idf/components/hal/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/hal/include -I/home/<USER>/esp32/esp-idf/components/efuse/include -I/home/<USER>/esp32/esp-idf/components/efuse/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_bootloader_format/include -I/home/<USER>/esp32/esp-idf/components/esp_app_format/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -O2 -fmacro-prefix-map=/home/<USER>/esp32/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=/home/<USER>/esp32/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash.c.obj -c /home/<USER>/esp32/esp-idf/components/bootloader_support/bootloader_flash/src/bootloader_flash.c", "file": "/home/<USER>/esp32/esp-idf/components/bootloader_support/bootloader_flash/src/bootloader_flash.c"}, {"directory": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader", "command": "/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.1-dirty\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/home/<USER>/bysx/xiaozhi-esp32/build/bootloader/config -I/home/<USER>/esp32/esp-idf/components/bootloader_support/include -I/home/<USER>/esp32/esp-idf/components/bootloader_support/bootloader_flash/include -I/home/<USER>/esp32/esp-idf/components/bootloader_support/private_include -I/home/<USER>/esp32/esp-idf/components/log/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_common/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/dma/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/ldo/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/debug_probe/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/newlib/platform_include -I/home/<USER>/esp32/esp-idf/components/xtensa/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/xtensa/include -I/home/<USER>/esp32/esp-idf/components/xtensa/deprecated_include -I/home/<USER>/esp32/esp-idf/components/soc/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/register -I/home/<USER>/esp32/esp-idf/components/bootloader/subproject/components/micro-ecc -I/home/<USER>/esp32/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -I/home/<USER>/esp32/esp-idf/components/spi_flash/include -I/home/<USER>/esp32/esp-idf/components/hal/platform_port/include -I/home/<USER>/esp32/esp-idf/components/hal/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/hal/include -I/home/<USER>/esp32/esp-idf/components/efuse/include -I/home/<USER>/esp32/esp-idf/components/efuse/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_bootloader_format/include -I/home/<USER>/esp32/esp-idf/components/esp_app_format/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -O2 -fmacro-prefix-map=/home/<USER>/esp32/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=/home/<USER>/esp32/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/flash_qio_mode.c.obj -c /home/<USER>/esp32/esp-idf/components/bootloader_support/bootloader_flash/src/flash_qio_mode.c", "file": "/home/<USER>/esp32/esp-idf/components/bootloader_support/bootloader_flash/src/flash_qio_mode.c"}, {"directory": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader", "command": "/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.1-dirty\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/home/<USER>/bysx/xiaozhi-esp32/build/bootloader/config -I/home/<USER>/esp32/esp-idf/components/bootloader_support/include -I/home/<USER>/esp32/esp-idf/components/bootloader_support/bootloader_flash/include -I/home/<USER>/esp32/esp-idf/components/bootloader_support/private_include -I/home/<USER>/esp32/esp-idf/components/log/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_common/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/dma/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/ldo/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/debug_probe/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/newlib/platform_include -I/home/<USER>/esp32/esp-idf/components/xtensa/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/xtensa/include -I/home/<USER>/esp32/esp-idf/components/xtensa/deprecated_include -I/home/<USER>/esp32/esp-idf/components/soc/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/register -I/home/<USER>/esp32/esp-idf/components/bootloader/subproject/components/micro-ecc -I/home/<USER>/esp32/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -I/home/<USER>/esp32/esp-idf/components/spi_flash/include -I/home/<USER>/esp32/esp-idf/components/hal/platform_port/include -I/home/<USER>/esp32/esp-idf/components/hal/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/hal/include -I/home/<USER>/esp32/esp-idf/components/efuse/include -I/home/<USER>/esp32/esp-idf/components/efuse/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_bootloader_format/include -I/home/<USER>/esp32/esp-idf/components/esp_app_format/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -O2 -fmacro-prefix-map=/home/<USER>/esp32/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=/home/<USER>/esp32/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash_config_esp32s3.c.obj -c /home/<USER>/esp32/esp-idf/components/bootloader_support/bootloader_flash/src/bootloader_flash_config_esp32s3.c", "file": "/home/<USER>/esp32/esp-idf/components/bootloader_support/bootloader_flash/src/bootloader_flash_config_esp32s3.c"}, {"directory": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader", "command": "/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.1-dirty\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/home/<USER>/bysx/xiaozhi-esp32/build/bootloader/config -I/home/<USER>/esp32/esp-idf/components/bootloader_support/include -I/home/<USER>/esp32/esp-idf/components/bootloader_support/bootloader_flash/include -I/home/<USER>/esp32/esp-idf/components/bootloader_support/private_include -I/home/<USER>/esp32/esp-idf/components/log/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_common/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/dma/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/ldo/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/debug_probe/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/newlib/platform_include -I/home/<USER>/esp32/esp-idf/components/xtensa/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/xtensa/include -I/home/<USER>/esp32/esp-idf/components/xtensa/deprecated_include -I/home/<USER>/esp32/esp-idf/components/soc/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/register -I/home/<USER>/esp32/esp-idf/components/bootloader/subproject/components/micro-ecc -I/home/<USER>/esp32/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -I/home/<USER>/esp32/esp-idf/components/spi_flash/include -I/home/<USER>/esp32/esp-idf/components/hal/platform_port/include -I/home/<USER>/esp32/esp-idf/components/hal/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/hal/include -I/home/<USER>/esp32/esp-idf/components/efuse/include -I/home/<USER>/esp32/esp-idf/components/efuse/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_bootloader_format/include -I/home/<USER>/esp32/esp-idf/components/esp_app_format/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -O2 -fmacro-prefix-map=/home/<USER>/esp32/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=/home/<USER>/esp32/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_utility.c.obj -c /home/<USER>/esp32/esp-idf/components/bootloader_support/src/bootloader_utility.c", "file": "/home/<USER>/esp32/esp-idf/components/bootloader_support/src/bootloader_utility.c"}, {"directory": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader", "command": "/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.1-dirty\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/home/<USER>/bysx/xiaozhi-esp32/build/bootloader/config -I/home/<USER>/esp32/esp-idf/components/bootloader_support/include -I/home/<USER>/esp32/esp-idf/components/bootloader_support/bootloader_flash/include -I/home/<USER>/esp32/esp-idf/components/bootloader_support/private_include -I/home/<USER>/esp32/esp-idf/components/log/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_common/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/dma/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/ldo/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/debug_probe/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/newlib/platform_include -I/home/<USER>/esp32/esp-idf/components/xtensa/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/xtensa/include -I/home/<USER>/esp32/esp-idf/components/xtensa/deprecated_include -I/home/<USER>/esp32/esp-idf/components/soc/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/register -I/home/<USER>/esp32/esp-idf/components/bootloader/subproject/components/micro-ecc -I/home/<USER>/esp32/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -I/home/<USER>/esp32/esp-idf/components/spi_flash/include -I/home/<USER>/esp32/esp-idf/components/hal/platform_port/include -I/home/<USER>/esp32/esp-idf/components/hal/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/hal/include -I/home/<USER>/esp32/esp-idf/components/efuse/include -I/home/<USER>/esp32/esp-idf/components/efuse/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_bootloader_format/include -I/home/<USER>/esp32/esp-idf/components/esp_app_format/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -O2 -fmacro-prefix-map=/home/<USER>/esp32/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=/home/<USER>/esp32/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_partitions.c.obj -c /home/<USER>/esp32/esp-idf/components/bootloader_support/src/flash_partitions.c", "file": "/home/<USER>/esp32/esp-idf/components/bootloader_support/src/flash_partitions.c"}, {"directory": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader", "command": "/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.1-dirty\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/home/<USER>/bysx/xiaozhi-esp32/build/bootloader/config -I/home/<USER>/esp32/esp-idf/components/bootloader_support/include -I/home/<USER>/esp32/esp-idf/components/bootloader_support/bootloader_flash/include -I/home/<USER>/esp32/esp-idf/components/bootloader_support/private_include -I/home/<USER>/esp32/esp-idf/components/log/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_common/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/dma/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/ldo/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/debug_probe/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/newlib/platform_include -I/home/<USER>/esp32/esp-idf/components/xtensa/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/xtensa/include -I/home/<USER>/esp32/esp-idf/components/xtensa/deprecated_include -I/home/<USER>/esp32/esp-idf/components/soc/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/register -I/home/<USER>/esp32/esp-idf/components/bootloader/subproject/components/micro-ecc -I/home/<USER>/esp32/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -I/home/<USER>/esp32/esp-idf/components/spi_flash/include -I/home/<USER>/esp32/esp-idf/components/hal/platform_port/include -I/home/<USER>/esp32/esp-idf/components/hal/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/hal/include -I/home/<USER>/esp32/esp-idf/components/efuse/include -I/home/<USER>/esp32/esp-idf/components/efuse/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_bootloader_format/include -I/home/<USER>/esp32/esp-idf/components/esp_app_format/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -O2 -fmacro-prefix-map=/home/<USER>/esp32/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=/home/<USER>/esp32/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp_image_format.c.obj -c /home/<USER>/esp32/esp-idf/components/bootloader_support/src/esp_image_format.c", "file": "/home/<USER>/esp32/esp-idf/components/bootloader_support/src/esp_image_format.c"}, {"directory": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader", "command": "/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.1-dirty\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/home/<USER>/bysx/xiaozhi-esp32/build/bootloader/config -I/home/<USER>/esp32/esp-idf/components/bootloader_support/include -I/home/<USER>/esp32/esp-idf/components/bootloader_support/bootloader_flash/include -I/home/<USER>/esp32/esp-idf/components/bootloader_support/private_include -I/home/<USER>/esp32/esp-idf/components/log/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_common/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/dma/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/ldo/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/debug_probe/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/newlib/platform_include -I/home/<USER>/esp32/esp-idf/components/xtensa/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/xtensa/include -I/home/<USER>/esp32/esp-idf/components/xtensa/deprecated_include -I/home/<USER>/esp32/esp-idf/components/soc/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/register -I/home/<USER>/esp32/esp-idf/components/bootloader/subproject/components/micro-ecc -I/home/<USER>/esp32/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -I/home/<USER>/esp32/esp-idf/components/spi_flash/include -I/home/<USER>/esp32/esp-idf/components/hal/platform_port/include -I/home/<USER>/esp32/esp-idf/components/hal/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/hal/include -I/home/<USER>/esp32/esp-idf/components/efuse/include -I/home/<USER>/esp32/esp-idf/components/efuse/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_bootloader_format/include -I/home/<USER>/esp32/esp-idf/components/esp_app_format/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -O2 -fmacro-prefix-map=/home/<USER>/esp32/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=/home/<USER>/esp32/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_init.c.obj -c /home/<USER>/esp32/esp-idf/components/bootloader_support/src/bootloader_init.c", "file": "/home/<USER>/esp32/esp-idf/components/bootloader_support/src/bootloader_init.c"}, {"directory": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader", "command": "/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.1-dirty\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/home/<USER>/bysx/xiaozhi-esp32/build/bootloader/config -I/home/<USER>/esp32/esp-idf/components/bootloader_support/include -I/home/<USER>/esp32/esp-idf/components/bootloader_support/bootloader_flash/include -I/home/<USER>/esp32/esp-idf/components/bootloader_support/private_include -I/home/<USER>/esp32/esp-idf/components/log/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_common/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/dma/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/ldo/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/debug_probe/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/newlib/platform_include -I/home/<USER>/esp32/esp-idf/components/xtensa/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/xtensa/include -I/home/<USER>/esp32/esp-idf/components/xtensa/deprecated_include -I/home/<USER>/esp32/esp-idf/components/soc/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/register -I/home/<USER>/esp32/esp-idf/components/bootloader/subproject/components/micro-ecc -I/home/<USER>/esp32/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -I/home/<USER>/esp32/esp-idf/components/spi_flash/include -I/home/<USER>/esp32/esp-idf/components/hal/platform_port/include -I/home/<USER>/esp32/esp-idf/components/hal/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/hal/include -I/home/<USER>/esp32/esp-idf/components/efuse/include -I/home/<USER>/esp32/esp-idf/components/efuse/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_bootloader_format/include -I/home/<USER>/esp32/esp-idf/components/esp_app_format/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -O2 -fmacro-prefix-map=/home/<USER>/esp32/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=/home/<USER>/esp32/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_loader.c.obj -c /home/<USER>/esp32/esp-idf/components/bootloader_support/src/bootloader_clock_loader.c", "file": "/home/<USER>/esp32/esp-idf/components/bootloader_support/src/bootloader_clock_loader.c"}, {"directory": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader", "command": "/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.1-dirty\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/home/<USER>/bysx/xiaozhi-esp32/build/bootloader/config -I/home/<USER>/esp32/esp-idf/components/bootloader_support/include -I/home/<USER>/esp32/esp-idf/components/bootloader_support/bootloader_flash/include -I/home/<USER>/esp32/esp-idf/components/bootloader_support/private_include -I/home/<USER>/esp32/esp-idf/components/log/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_common/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/dma/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/ldo/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/debug_probe/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/newlib/platform_include -I/home/<USER>/esp32/esp-idf/components/xtensa/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/xtensa/include -I/home/<USER>/esp32/esp-idf/components/xtensa/deprecated_include -I/home/<USER>/esp32/esp-idf/components/soc/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/register -I/home/<USER>/esp32/esp-idf/components/bootloader/subproject/components/micro-ecc -I/home/<USER>/esp32/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -I/home/<USER>/esp32/esp-idf/components/spi_flash/include -I/home/<USER>/esp32/esp-idf/components/hal/platform_port/include -I/home/<USER>/esp32/esp-idf/components/hal/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/hal/include -I/home/<USER>/esp32/esp-idf/components/efuse/include -I/home/<USER>/esp32/esp-idf/components/efuse/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_bootloader_format/include -I/home/<USER>/esp32/esp-idf/components/esp_app_format/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -O2 -fmacro-prefix-map=/home/<USER>/esp32/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=/home/<USER>/esp32/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console.c.obj -c /home/<USER>/esp32/esp-idf/components/bootloader_support/src/bootloader_console.c", "file": "/home/<USER>/esp32/esp-idf/components/bootloader_support/src/bootloader_console.c"}, {"directory": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader", "command": "/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.1-dirty\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/home/<USER>/bysx/xiaozhi-esp32/build/bootloader/config -I/home/<USER>/esp32/esp-idf/components/bootloader_support/include -I/home/<USER>/esp32/esp-idf/components/bootloader_support/bootloader_flash/include -I/home/<USER>/esp32/esp-idf/components/bootloader_support/private_include -I/home/<USER>/esp32/esp-idf/components/log/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_common/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/dma/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/ldo/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/debug_probe/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/newlib/platform_include -I/home/<USER>/esp32/esp-idf/components/xtensa/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/xtensa/include -I/home/<USER>/esp32/esp-idf/components/xtensa/deprecated_include -I/home/<USER>/esp32/esp-idf/components/soc/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/register -I/home/<USER>/esp32/esp-idf/components/bootloader/subproject/components/micro-ecc -I/home/<USER>/esp32/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -I/home/<USER>/esp32/esp-idf/components/spi_flash/include -I/home/<USER>/esp32/esp-idf/components/hal/platform_port/include -I/home/<USER>/esp32/esp-idf/components/hal/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/hal/include -I/home/<USER>/esp32/esp-idf/components/efuse/include -I/home/<USER>/esp32/esp-idf/components/efuse/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_bootloader_format/include -I/home/<USER>/esp32/esp-idf/components/esp_app_format/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -O2 -fmacro-prefix-map=/home/<USER>/esp32/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=/home/<USER>/esp32/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console_loader.c.obj -c /home/<USER>/esp32/esp-idf/components/bootloader_support/src/bootloader_console_loader.c", "file": "/home/<USER>/esp32/esp-idf/components/bootloader_support/src/bootloader_console_loader.c"}, {"directory": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader", "command": "/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.1-dirty\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/home/<USER>/bysx/xiaozhi-esp32/build/bootloader/config -I/home/<USER>/esp32/esp-idf/components/bootloader_support/include -I/home/<USER>/esp32/esp-idf/components/bootloader_support/bootloader_flash/include -I/home/<USER>/esp32/esp-idf/components/bootloader_support/private_include -I/home/<USER>/esp32/esp-idf/components/log/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_common/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/dma/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/ldo/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/debug_probe/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/newlib/platform_include -I/home/<USER>/esp32/esp-idf/components/xtensa/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/xtensa/include -I/home/<USER>/esp32/esp-idf/components/xtensa/deprecated_include -I/home/<USER>/esp32/esp-idf/components/soc/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/register -I/home/<USER>/esp32/esp-idf/components/bootloader/subproject/components/micro-ecc -I/home/<USER>/esp32/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -I/home/<USER>/esp32/esp-idf/components/spi_flash/include -I/home/<USER>/esp32/esp-idf/components/hal/platform_port/include -I/home/<USER>/esp32/esp-idf/components/hal/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/hal/include -I/home/<USER>/esp32/esp-idf/components/efuse/include -I/home/<USER>/esp32/esp-idf/components/efuse/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_bootloader_format/include -I/home/<USER>/esp32/esp-idf/components/esp_app_format/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -O2 -fmacro-prefix-map=/home/<USER>/esp32/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=/home/<USER>/esp32/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32s3/bootloader_sha.c.obj -c /home/<USER>/esp32/esp-idf/components/bootloader_support/src/esp32s3/bootloader_sha.c", "file": "/home/<USER>/esp32/esp-idf/components/bootloader_support/src/esp32s3/bootloader_sha.c"}, {"directory": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader", "command": "/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.1-dirty\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/home/<USER>/bysx/xiaozhi-esp32/build/bootloader/config -I/home/<USER>/esp32/esp-idf/components/bootloader_support/include -I/home/<USER>/esp32/esp-idf/components/bootloader_support/bootloader_flash/include -I/home/<USER>/esp32/esp-idf/components/bootloader_support/private_include -I/home/<USER>/esp32/esp-idf/components/log/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_common/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/dma/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/ldo/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/debug_probe/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/newlib/platform_include -I/home/<USER>/esp32/esp-idf/components/xtensa/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/xtensa/include -I/home/<USER>/esp32/esp-idf/components/xtensa/deprecated_include -I/home/<USER>/esp32/esp-idf/components/soc/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/register -I/home/<USER>/esp32/esp-idf/components/bootloader/subproject/components/micro-ecc -I/home/<USER>/esp32/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -I/home/<USER>/esp32/esp-idf/components/spi_flash/include -I/home/<USER>/esp32/esp-idf/components/hal/platform_port/include -I/home/<USER>/esp32/esp-idf/components/hal/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/hal/include -I/home/<USER>/esp32/esp-idf/components/efuse/include -I/home/<USER>/esp32/esp-idf/components/efuse/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_bootloader_format/include -I/home/<USER>/esp32/esp-idf/components/esp_app_format/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -O2 -fmacro-prefix-map=/home/<USER>/esp32/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=/home/<USER>/esp32/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32s3/bootloader_soc.c.obj -c /home/<USER>/esp32/esp-idf/components/bootloader_support/src/esp32s3/bootloader_soc.c", "file": "/home/<USER>/esp32/esp-idf/components/bootloader_support/src/esp32s3/bootloader_soc.c"}, {"directory": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader", "command": "/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.1-dirty\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/home/<USER>/bysx/xiaozhi-esp32/build/bootloader/config -I/home/<USER>/esp32/esp-idf/components/bootloader_support/include -I/home/<USER>/esp32/esp-idf/components/bootloader_support/bootloader_flash/include -I/home/<USER>/esp32/esp-idf/components/bootloader_support/private_include -I/home/<USER>/esp32/esp-idf/components/log/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_common/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/dma/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/ldo/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/debug_probe/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/newlib/platform_include -I/home/<USER>/esp32/esp-idf/components/xtensa/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/xtensa/include -I/home/<USER>/esp32/esp-idf/components/xtensa/deprecated_include -I/home/<USER>/esp32/esp-idf/components/soc/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/register -I/home/<USER>/esp32/esp-idf/components/bootloader/subproject/components/micro-ecc -I/home/<USER>/esp32/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -I/home/<USER>/esp32/esp-idf/components/spi_flash/include -I/home/<USER>/esp32/esp-idf/components/hal/platform_port/include -I/home/<USER>/esp32/esp-idf/components/hal/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/hal/include -I/home/<USER>/esp32/esp-idf/components/efuse/include -I/home/<USER>/esp32/esp-idf/components/efuse/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_bootloader_format/include -I/home/<USER>/esp32/esp-idf/components/esp_app_format/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -O2 -fmacro-prefix-map=/home/<USER>/esp32/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=/home/<USER>/esp32/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32s3/bootloader_esp32s3.c.obj -c /home/<USER>/esp32/esp-idf/components/bootloader_support/src/esp32s3/bootloader_esp32s3.c", "file": "/home/<USER>/esp32/esp-idf/components/bootloader_support/src/esp32s3/bootloader_esp32s3.c"}, {"directory": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader", "command": "/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.1-dirty\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/home/<USER>/bysx/xiaozhi-esp32/build/bootloader/config -I/home/<USER>/esp32/esp-idf/components/bootloader_support/include -I/home/<USER>/esp32/esp-idf/components/bootloader_support/bootloader_flash/include -I/home/<USER>/esp32/esp-idf/components/bootloader_support/private_include -I/home/<USER>/esp32/esp-idf/components/log/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_common/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/dma/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/ldo/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/debug_probe/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/newlib/platform_include -I/home/<USER>/esp32/esp-idf/components/xtensa/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/xtensa/include -I/home/<USER>/esp32/esp-idf/components/xtensa/deprecated_include -I/home/<USER>/esp32/esp-idf/components/soc/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/register -I/home/<USER>/esp32/esp-idf/components/bootloader/subproject/components/micro-ecc -I/home/<USER>/esp32/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -I/home/<USER>/esp32/esp-idf/components/spi_flash/include -I/home/<USER>/esp32/esp-idf/components/hal/platform_port/include -I/home/<USER>/esp32/esp-idf/components/hal/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/hal/include -I/home/<USER>/esp32/esp-idf/components/efuse/include -I/home/<USER>/esp32/esp-idf/components/efuse/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_bootloader_format/include -I/home/<USER>/esp32/esp-idf/components/esp_app_format/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -O2 -fmacro-prefix-map=/home/<USER>/esp32/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=/home/<USER>/esp32/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_panic.c.obj -c /home/<USER>/esp32/esp-idf/components/bootloader_support/src/bootloader_panic.c", "file": "/home/<USER>/esp32/esp-idf/components/bootloader_support/src/bootloader_panic.c"}, {"directory": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader", "command": "/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.1-dirty\\\" -DN<PERSON>_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/home/<USER>/bysx/xiaozhi-esp32/build/bootloader/config -I/home/<USER>/esp32/esp-idf/components/efuse/include -I/home/<USER>/esp32/esp-idf/components/efuse/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/efuse/private_include -I/home/<USER>/esp32/esp-idf/components/efuse/esp32s3/private_include -I/home/<USER>/esp32/esp-idf/components/log/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_common/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/dma/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/ldo/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/debug_probe/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/newlib/platform_include -I/home/<USER>/esp32/esp-idf/components/xtensa/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/xtensa/include -I/home/<USER>/esp32/esp-idf/components/xtensa/deprecated_include -I/home/<USER>/esp32/esp-idf/components/soc/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/register -I/home/<USER>/esp32/esp-idf/components/bootloader_support/include -I/home/<USER>/esp32/esp-idf/components/bootloader_support/bootloader_flash/include -I/home/<USER>/esp32/esp-idf/components/bootloader_support/private_include -I/home/<USER>/esp32/esp-idf/components/spi_flash/include -I/home/<USER>/esp32/esp-idf/components/hal/platform_port/include -I/home/<USER>/esp32/esp-idf/components/hal/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/hal/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -O2 -fmacro-prefix-map=/home/<USER>/esp32/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=/home/<USER>/esp32/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_table.c.obj -c /home/<USER>/esp32/esp-idf/components/efuse/esp32s3/esp_efuse_table.c", "file": "/home/<USER>/esp32/esp-idf/components/efuse/esp32s3/esp_efuse_table.c"}, {"directory": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader", "command": "/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.1-dirty\\\" -DN<PERSON>_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/home/<USER>/bysx/xiaozhi-esp32/build/bootloader/config -I/home/<USER>/esp32/esp-idf/components/efuse/include -I/home/<USER>/esp32/esp-idf/components/efuse/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/efuse/private_include -I/home/<USER>/esp32/esp-idf/components/efuse/esp32s3/private_include -I/home/<USER>/esp32/esp-idf/components/log/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_common/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/dma/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/ldo/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/debug_probe/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/newlib/platform_include -I/home/<USER>/esp32/esp-idf/components/xtensa/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/xtensa/include -I/home/<USER>/esp32/esp-idf/components/xtensa/deprecated_include -I/home/<USER>/esp32/esp-idf/components/soc/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/register -I/home/<USER>/esp32/esp-idf/components/bootloader_support/include -I/home/<USER>/esp32/esp-idf/components/bootloader_support/bootloader_flash/include -I/home/<USER>/esp32/esp-idf/components/bootloader_support/private_include -I/home/<USER>/esp32/esp-idf/components/spi_flash/include -I/home/<USER>/esp32/esp-idf/components/hal/platform_port/include -I/home/<USER>/esp32/esp-idf/components/hal/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/hal/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -O2 -fmacro-prefix-map=/home/<USER>/esp32/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=/home/<USER>/esp32/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_fields.c.obj -c /home/<USER>/esp32/esp-idf/components/efuse/esp32s3/esp_efuse_fields.c", "file": "/home/<USER>/esp32/esp-idf/components/efuse/esp32s3/esp_efuse_fields.c"}, {"directory": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader", "command": "/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.1-dirty\\\" -DN<PERSON>_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/home/<USER>/bysx/xiaozhi-esp32/build/bootloader/config -I/home/<USER>/esp32/esp-idf/components/efuse/include -I/home/<USER>/esp32/esp-idf/components/efuse/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/efuse/private_include -I/home/<USER>/esp32/esp-idf/components/efuse/esp32s3/private_include -I/home/<USER>/esp32/esp-idf/components/log/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_common/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/dma/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/ldo/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/debug_probe/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/newlib/platform_include -I/home/<USER>/esp32/esp-idf/components/xtensa/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/xtensa/include -I/home/<USER>/esp32/esp-idf/components/xtensa/deprecated_include -I/home/<USER>/esp32/esp-idf/components/soc/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/register -I/home/<USER>/esp32/esp-idf/components/bootloader_support/include -I/home/<USER>/esp32/esp-idf/components/bootloader_support/bootloader_flash/include -I/home/<USER>/esp32/esp-idf/components/bootloader_support/private_include -I/home/<USER>/esp32/esp-idf/components/spi_flash/include -I/home/<USER>/esp32/esp-idf/components/hal/platform_port/include -I/home/<USER>/esp32/esp-idf/components/hal/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/hal/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -O2 -fmacro-prefix-map=/home/<USER>/esp32/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=/home/<USER>/esp32/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_rtc_calib.c.obj -c /home/<USER>/esp32/esp-idf/components/efuse/esp32s3/esp_efuse_rtc_calib.c", "file": "/home/<USER>/esp32/esp-idf/components/efuse/esp32s3/esp_efuse_rtc_calib.c"}, {"directory": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader", "command": "/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.1-dirty\\\" -DN<PERSON>_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/home/<USER>/bysx/xiaozhi-esp32/build/bootloader/config -I/home/<USER>/esp32/esp-idf/components/efuse/include -I/home/<USER>/esp32/esp-idf/components/efuse/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/efuse/private_include -I/home/<USER>/esp32/esp-idf/components/efuse/esp32s3/private_include -I/home/<USER>/esp32/esp-idf/components/log/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_common/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/dma/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/ldo/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/debug_probe/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/newlib/platform_include -I/home/<USER>/esp32/esp-idf/components/xtensa/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/xtensa/include -I/home/<USER>/esp32/esp-idf/components/xtensa/deprecated_include -I/home/<USER>/esp32/esp-idf/components/soc/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/register -I/home/<USER>/esp32/esp-idf/components/bootloader_support/include -I/home/<USER>/esp32/esp-idf/components/bootloader_support/bootloader_flash/include -I/home/<USER>/esp32/esp-idf/components/bootloader_support/private_include -I/home/<USER>/esp32/esp-idf/components/spi_flash/include -I/home/<USER>/esp32/esp-idf/components/hal/platform_port/include -I/home/<USER>/esp32/esp-idf/components/hal/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/hal/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -O2 -fmacro-prefix-map=/home/<USER>/esp32/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=/home/<USER>/esp32/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_utility.c.obj -c /home/<USER>/esp32/esp-idf/components/efuse/esp32s3/esp_efuse_utility.c", "file": "/home/<USER>/esp32/esp-idf/components/efuse/esp32s3/esp_efuse_utility.c"}, {"directory": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader", "command": "/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.1-dirty\\\" -DN<PERSON>_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/home/<USER>/bysx/xiaozhi-esp32/build/bootloader/config -I/home/<USER>/esp32/esp-idf/components/efuse/include -I/home/<USER>/esp32/esp-idf/components/efuse/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/efuse/private_include -I/home/<USER>/esp32/esp-idf/components/efuse/esp32s3/private_include -I/home/<USER>/esp32/esp-idf/components/log/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_common/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/dma/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/ldo/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/debug_probe/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/newlib/platform_include -I/home/<USER>/esp32/esp-idf/components/xtensa/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/xtensa/include -I/home/<USER>/esp32/esp-idf/components/xtensa/deprecated_include -I/home/<USER>/esp32/esp-idf/components/soc/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/register -I/home/<USER>/esp32/esp-idf/components/bootloader_support/include -I/home/<USER>/esp32/esp-idf/components/bootloader_support/bootloader_flash/include -I/home/<USER>/esp32/esp-idf/components/bootloader_support/private_include -I/home/<USER>/esp32/esp-idf/components/spi_flash/include -I/home/<USER>/esp32/esp-idf/components/hal/platform_port/include -I/home/<USER>/esp32/esp-idf/components/hal/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/hal/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -O2 -fmacro-prefix-map=/home/<USER>/esp32/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=/home/<USER>/esp32/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_api.c.obj -c /home/<USER>/esp32/esp-idf/components/efuse/src/esp_efuse_api.c", "file": "/home/<USER>/esp32/esp-idf/components/efuse/src/esp_efuse_api.c"}, {"directory": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader", "command": "/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.1-dirty\\\" -DN<PERSON>_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/home/<USER>/bysx/xiaozhi-esp32/build/bootloader/config -I/home/<USER>/esp32/esp-idf/components/efuse/include -I/home/<USER>/esp32/esp-idf/components/efuse/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/efuse/private_include -I/home/<USER>/esp32/esp-idf/components/efuse/esp32s3/private_include -I/home/<USER>/esp32/esp-idf/components/log/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_common/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/dma/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/ldo/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/debug_probe/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/newlib/platform_include -I/home/<USER>/esp32/esp-idf/components/xtensa/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/xtensa/include -I/home/<USER>/esp32/esp-idf/components/xtensa/deprecated_include -I/home/<USER>/esp32/esp-idf/components/soc/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/register -I/home/<USER>/esp32/esp-idf/components/bootloader_support/include -I/home/<USER>/esp32/esp-idf/components/bootloader_support/bootloader_flash/include -I/home/<USER>/esp32/esp-idf/components/bootloader_support/private_include -I/home/<USER>/esp32/esp-idf/components/spi_flash/include -I/home/<USER>/esp32/esp-idf/components/hal/platform_port/include -I/home/<USER>/esp32/esp-idf/components/hal/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/hal/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -O2 -fmacro-prefix-map=/home/<USER>/esp32/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=/home/<USER>/esp32/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_fields.c.obj -c /home/<USER>/esp32/esp-idf/components/efuse/src/esp_efuse_fields.c", "file": "/home/<USER>/esp32/esp-idf/components/efuse/src/esp_efuse_fields.c"}, {"directory": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader", "command": "/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.1-dirty\\\" -DN<PERSON>_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/home/<USER>/bysx/xiaozhi-esp32/build/bootloader/config -I/home/<USER>/esp32/esp-idf/components/efuse/include -I/home/<USER>/esp32/esp-idf/components/efuse/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/efuse/private_include -I/home/<USER>/esp32/esp-idf/components/efuse/esp32s3/private_include -I/home/<USER>/esp32/esp-idf/components/log/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_common/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/dma/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/ldo/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/debug_probe/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/newlib/platform_include -I/home/<USER>/esp32/esp-idf/components/xtensa/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/xtensa/include -I/home/<USER>/esp32/esp-idf/components/xtensa/deprecated_include -I/home/<USER>/esp32/esp-idf/components/soc/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/register -I/home/<USER>/esp32/esp-idf/components/bootloader_support/include -I/home/<USER>/esp32/esp-idf/components/bootloader_support/bootloader_flash/include -I/home/<USER>/esp32/esp-idf/components/bootloader_support/private_include -I/home/<USER>/esp32/esp-idf/components/spi_flash/include -I/home/<USER>/esp32/esp-idf/components/hal/platform_port/include -I/home/<USER>/esp32/esp-idf/components/hal/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/hal/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -O2 -fmacro-prefix-map=/home/<USER>/esp32/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=/home/<USER>/esp32/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_utility.c.obj -c /home/<USER>/esp32/esp-idf/components/efuse/src/esp_efuse_utility.c", "file": "/home/<USER>/esp32/esp-idf/components/efuse/src/esp_efuse_utility.c"}, {"directory": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader", "command": "/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.1-dirty\\\" -DN<PERSON>_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/home/<USER>/bysx/xiaozhi-esp32/build/bootloader/config -I/home/<USER>/esp32/esp-idf/components/efuse/include -I/home/<USER>/esp32/esp-idf/components/efuse/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/efuse/private_include -I/home/<USER>/esp32/esp-idf/components/efuse/esp32s3/private_include -I/home/<USER>/esp32/esp-idf/components/log/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_common/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/dma/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/ldo/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/debug_probe/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/newlib/platform_include -I/home/<USER>/esp32/esp-idf/components/xtensa/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/xtensa/include -I/home/<USER>/esp32/esp-idf/components/xtensa/deprecated_include -I/home/<USER>/esp32/esp-idf/components/soc/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/register -I/home/<USER>/esp32/esp-idf/components/bootloader_support/include -I/home/<USER>/esp32/esp-idf/components/bootloader_support/bootloader_flash/include -I/home/<USER>/esp32/esp-idf/components/bootloader_support/private_include -I/home/<USER>/esp32/esp-idf/components/spi_flash/include -I/home/<USER>/esp32/esp-idf/components/hal/platform_port/include -I/home/<USER>/esp32/esp-idf/components/hal/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/hal/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -O2 -fmacro-prefix-map=/home/<USER>/esp32/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=/home/<USER>/esp32/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/efuse_controller/keys/with_key_purposes/esp_efuse_api_key.c.obj -c /home/<USER>/esp32/esp-idf/components/efuse/src/efuse_controller/keys/with_key_purposes/esp_efuse_api_key.c", "file": "/home/<USER>/esp32/esp-idf/components/efuse/src/efuse_controller/keys/with_key_purposes/esp_efuse_api_key.c"}, {"directory": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader", "command": "/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.1-dirty\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/home/<USER>/bysx/xiaozhi-esp32/build/bootloader/config -I/home/<USER>/esp32/esp-idf/components/log/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_common/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/dma/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/ldo/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/debug_probe/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/newlib/platform_include -I/home/<USER>/esp32/esp-idf/components/xtensa/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/xtensa/include -I/home/<USER>/esp32/esp-idf/components/xtensa/deprecated_include -I/home/<USER>/esp32/esp-idf/components/soc/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/register -I/home/<USER>/esp32/esp-idf/components/spi_flash/include -I/home/<USER>/esp32/esp-idf/components/hal/platform_port/include -I/home/<USER>/esp32/esp-idf/components/hal/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/hal/include -I/home/<USER>/esp32/esp-idf/components/esp_app_format/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -O2 -fmacro-prefix-map=/home/<USER>/esp32/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=/home/<USER>/esp32/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/esp_err.c.obj -c /home/<USER>/esp32/esp-idf/components/esp_system/esp_err.c", "file": "/home/<USER>/esp32/esp-idf/components/esp_system/esp_err.c"}, {"directory": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader", "command": "/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.1-dirty\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/home/<USER>/bysx/xiaozhi-esp32/build/bootloader/config -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/dma/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/ldo/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/debug_probe/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/esp_private -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/log/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_common/include -I/home/<USER>/esp32/esp-idf/components/newlib/platform_include -I/home/<USER>/esp32/esp-idf/components/xtensa/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/xtensa/include -I/home/<USER>/esp32/esp-idf/components/xtensa/deprecated_include -I/home/<USER>/esp32/esp-idf/components/soc/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/register -I/home/<USER>/esp32/esp-idf/components/efuse/include -I/home/<USER>/esp32/esp-idf/components/efuse/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/spi_flash/include -I/home/<USER>/esp32/esp-idf/components/hal/platform_port/include -I/home/<USER>/esp32/esp-idf/components/hal/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/hal/include -I/home/<USER>/esp32/esp-idf/components/bootloader_support/include -I/home/<USER>/esp32/esp-idf/components/bootloader_support/bootloader_flash/include -I/home/<USER>/esp32/esp-idf/components/bootloader_support/private_include -I/home/<USER>/esp32/esp-idf/components/esp_security/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -O2 -fmacro-prefix-map=/home/<USER>/esp32/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=/home/<USER>/esp32/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/cpu.c.obj -c /home/<USER>/esp32/esp-idf/components/esp_hw_support/cpu.c", "file": "/home/<USER>/esp32/esp-idf/components/esp_hw_support/cpu.c"}, {"directory": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader", "command": "/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.1-dirty\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/home/<USER>/bysx/xiaozhi-esp32/build/bootloader/config -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/dma/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/ldo/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/debug_probe/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/esp_private -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/log/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_common/include -I/home/<USER>/esp32/esp-idf/components/newlib/platform_include -I/home/<USER>/esp32/esp-idf/components/xtensa/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/xtensa/include -I/home/<USER>/esp32/esp-idf/components/xtensa/deprecated_include -I/home/<USER>/esp32/esp-idf/components/soc/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/register -I/home/<USER>/esp32/esp-idf/components/efuse/include -I/home/<USER>/esp32/esp-idf/components/efuse/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/spi_flash/include -I/home/<USER>/esp32/esp-idf/components/hal/platform_port/include -I/home/<USER>/esp32/esp-idf/components/hal/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/hal/include -I/home/<USER>/esp32/esp-idf/components/bootloader_support/include -I/home/<USER>/esp32/esp-idf/components/bootloader_support/bootloader_flash/include -I/home/<USER>/esp32/esp-idf/components/bootloader_support/private_include -I/home/<USER>/esp32/esp-idf/components/esp_security/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -O2 -fmacro-prefix-map=/home/<USER>/esp32/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=/home/<USER>/esp32/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/esp_cpu_intr.c.obj -c /home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/esp_cpu_intr.c", "file": "/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/esp_cpu_intr.c"}, {"directory": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader", "command": "/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.1-dirty\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/home/<USER>/bysx/xiaozhi-esp32/build/bootloader/config -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/dma/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/ldo/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/debug_probe/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/esp_private -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/log/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_common/include -I/home/<USER>/esp32/esp-idf/components/newlib/platform_include -I/home/<USER>/esp32/esp-idf/components/xtensa/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/xtensa/include -I/home/<USER>/esp32/esp-idf/components/xtensa/deprecated_include -I/home/<USER>/esp32/esp-idf/components/soc/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/register -I/home/<USER>/esp32/esp-idf/components/efuse/include -I/home/<USER>/esp32/esp-idf/components/efuse/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/spi_flash/include -I/home/<USER>/esp32/esp-idf/components/hal/platform_port/include -I/home/<USER>/esp32/esp-idf/components/hal/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/hal/include -I/home/<USER>/esp32/esp-idf/components/bootloader_support/include -I/home/<USER>/esp32/esp-idf/components/bootloader_support/bootloader_flash/include -I/home/<USER>/esp32/esp-idf/components/bootloader_support/private_include -I/home/<USER>/esp32/esp-idf/components/esp_security/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -O2 -fmacro-prefix-map=/home/<USER>/esp32/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=/home/<USER>/esp32/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/esp_memory_utils.c.obj -c /home/<USER>/esp32/esp-idf/components/esp_hw_support/esp_memory_utils.c", "file": "/home/<USER>/esp32/esp-idf/components/esp_hw_support/esp_memory_utils.c"}, {"directory": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader", "command": "/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.1-dirty\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/home/<USER>/bysx/xiaozhi-esp32/build/bootloader/config -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/dma/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/ldo/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/debug_probe/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/esp_private -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/log/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_common/include -I/home/<USER>/esp32/esp-idf/components/newlib/platform_include -I/home/<USER>/esp32/esp-idf/components/xtensa/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/xtensa/include -I/home/<USER>/esp32/esp-idf/components/xtensa/deprecated_include -I/home/<USER>/esp32/esp-idf/components/soc/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/register -I/home/<USER>/esp32/esp-idf/components/efuse/include -I/home/<USER>/esp32/esp-idf/components/efuse/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/spi_flash/include -I/home/<USER>/esp32/esp-idf/components/hal/platform_port/include -I/home/<USER>/esp32/esp-idf/components/hal/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/hal/include -I/home/<USER>/esp32/esp-idf/components/bootloader_support/include -I/home/<USER>/esp32/esp-idf/components/bootloader_support/bootloader_flash/include -I/home/<USER>/esp32/esp-idf/components/bootloader_support/private_include -I/home/<USER>/esp32/esp-idf/components/esp_security/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -O2 -fmacro-prefix-map=/home/<USER>/esp32/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=/home/<USER>/esp32/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/cpu_region_protect.c.obj -c /home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/cpu_region_protect.c", "file": "/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/cpu_region_protect.c"}, {"directory": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader", "command": "/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.1-dirty\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/home/<USER>/bysx/xiaozhi-esp32/build/bootloader/config -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/dma/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/ldo/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/debug_probe/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/esp_private -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/log/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_common/include -I/home/<USER>/esp32/esp-idf/components/newlib/platform_include -I/home/<USER>/esp32/esp-idf/components/xtensa/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/xtensa/include -I/home/<USER>/esp32/esp-idf/components/xtensa/deprecated_include -I/home/<USER>/esp32/esp-idf/components/soc/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/register -I/home/<USER>/esp32/esp-idf/components/efuse/include -I/home/<USER>/esp32/esp-idf/components/efuse/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/spi_flash/include -I/home/<USER>/esp32/esp-idf/components/hal/platform_port/include -I/home/<USER>/esp32/esp-idf/components/hal/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/hal/include -I/home/<USER>/esp32/esp-idf/components/bootloader_support/include -I/home/<USER>/esp32/esp-idf/components/bootloader_support/bootloader_flash/include -I/home/<USER>/esp32/esp-idf/components/bootloader_support/private_include -I/home/<USER>/esp32/esp-idf/components/esp_security/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -O2 -fmacro-prefix-map=/home/<USER>/esp32/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=/home/<USER>/esp32/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_clk.c.obj -c /home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/rtc_clk.c", "file": "/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/rtc_clk.c"}, {"directory": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader", "command": "/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.1-dirty\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/home/<USER>/bysx/xiaozhi-esp32/build/bootloader/config -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/dma/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/ldo/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/debug_probe/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/esp_private -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/log/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_common/include -I/home/<USER>/esp32/esp-idf/components/newlib/platform_include -I/home/<USER>/esp32/esp-idf/components/xtensa/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/xtensa/include -I/home/<USER>/esp32/esp-idf/components/xtensa/deprecated_include -I/home/<USER>/esp32/esp-idf/components/soc/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/register -I/home/<USER>/esp32/esp-idf/components/efuse/include -I/home/<USER>/esp32/esp-idf/components/efuse/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/spi_flash/include -I/home/<USER>/esp32/esp-idf/components/hal/platform_port/include -I/home/<USER>/esp32/esp-idf/components/hal/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/hal/include -I/home/<USER>/esp32/esp-idf/components/bootloader_support/include -I/home/<USER>/esp32/esp-idf/components/bootloader_support/bootloader_flash/include -I/home/<USER>/esp32/esp-idf/components/bootloader_support/private_include -I/home/<USER>/esp32/esp-idf/components/esp_security/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -O2 -fmacro-prefix-map=/home/<USER>/esp32/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=/home/<USER>/esp32/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_clk_init.c.obj -c /home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/rtc_clk_init.c", "file": "/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/rtc_clk_init.c"}, {"directory": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader", "command": "/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.1-dirty\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/home/<USER>/bysx/xiaozhi-esp32/build/bootloader/config -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/dma/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/ldo/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/debug_probe/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/esp_private -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/log/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_common/include -I/home/<USER>/esp32/esp-idf/components/newlib/platform_include -I/home/<USER>/esp32/esp-idf/components/xtensa/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/xtensa/include -I/home/<USER>/esp32/esp-idf/components/xtensa/deprecated_include -I/home/<USER>/esp32/esp-idf/components/soc/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/register -I/home/<USER>/esp32/esp-idf/components/efuse/include -I/home/<USER>/esp32/esp-idf/components/efuse/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/spi_flash/include -I/home/<USER>/esp32/esp-idf/components/hal/platform_port/include -I/home/<USER>/esp32/esp-idf/components/hal/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/hal/include -I/home/<USER>/esp32/esp-idf/components/bootloader_support/include -I/home/<USER>/esp32/esp-idf/components/bootloader_support/bootloader_flash/include -I/home/<USER>/esp32/esp-idf/components/bootloader_support/private_include -I/home/<USER>/esp32/esp-idf/components/esp_security/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -O2 -fmacro-prefix-map=/home/<USER>/esp32/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=/home/<USER>/esp32/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_init.c.obj -c /home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/rtc_init.c", "file": "/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/rtc_init.c"}, {"directory": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader", "command": "/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.1-dirty\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/home/<USER>/bysx/xiaozhi-esp32/build/bootloader/config -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/dma/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/ldo/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/debug_probe/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/esp_private -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/log/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_common/include -I/home/<USER>/esp32/esp-idf/components/newlib/platform_include -I/home/<USER>/esp32/esp-idf/components/xtensa/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/xtensa/include -I/home/<USER>/esp32/esp-idf/components/xtensa/deprecated_include -I/home/<USER>/esp32/esp-idf/components/soc/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/register -I/home/<USER>/esp32/esp-idf/components/efuse/include -I/home/<USER>/esp32/esp-idf/components/efuse/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/spi_flash/include -I/home/<USER>/esp32/esp-idf/components/hal/platform_port/include -I/home/<USER>/esp32/esp-idf/components/hal/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/hal/include -I/home/<USER>/esp32/esp-idf/components/bootloader_support/include -I/home/<USER>/esp32/esp-idf/components/bootloader_support/bootloader_flash/include -I/home/<USER>/esp32/esp-idf/components/bootloader_support/private_include -I/home/<USER>/esp32/esp-idf/components/esp_security/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -O2 -fmacro-prefix-map=/home/<USER>/esp32/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=/home/<USER>/esp32/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_sleep.c.obj -c /home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/rtc_sleep.c", "file": "/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/rtc_sleep.c"}, {"directory": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader", "command": "/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.1-dirty\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/home/<USER>/bysx/xiaozhi-esp32/build/bootloader/config -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/dma/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/ldo/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/debug_probe/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/esp_private -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/log/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_common/include -I/home/<USER>/esp32/esp-idf/components/newlib/platform_include -I/home/<USER>/esp32/esp-idf/components/xtensa/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/xtensa/include -I/home/<USER>/esp32/esp-idf/components/xtensa/deprecated_include -I/home/<USER>/esp32/esp-idf/components/soc/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/register -I/home/<USER>/esp32/esp-idf/components/efuse/include -I/home/<USER>/esp32/esp-idf/components/efuse/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/spi_flash/include -I/home/<USER>/esp32/esp-idf/components/hal/platform_port/include -I/home/<USER>/esp32/esp-idf/components/hal/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/hal/include -I/home/<USER>/esp32/esp-idf/components/bootloader_support/include -I/home/<USER>/esp32/esp-idf/components/bootloader_support/bootloader_flash/include -I/home/<USER>/esp32/esp-idf/components/bootloader_support/private_include -I/home/<USER>/esp32/esp-idf/components/esp_security/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -O2 -fmacro-prefix-map=/home/<USER>/esp32/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=/home/<USER>/esp32/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_time.c.obj -c /home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/rtc_time.c", "file": "/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/rtc_time.c"}, {"directory": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader", "command": "/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.1-dirty\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/home/<USER>/bysx/xiaozhi-esp32/build/bootloader/config -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/dma/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/ldo/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/debug_probe/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/esp_private -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/log/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_common/include -I/home/<USER>/esp32/esp-idf/components/newlib/platform_include -I/home/<USER>/esp32/esp-idf/components/xtensa/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/xtensa/include -I/home/<USER>/esp32/esp-idf/components/xtensa/deprecated_include -I/home/<USER>/esp32/esp-idf/components/soc/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/register -I/home/<USER>/esp32/esp-idf/components/efuse/include -I/home/<USER>/esp32/esp-idf/components/efuse/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/spi_flash/include -I/home/<USER>/esp32/esp-idf/components/hal/platform_port/include -I/home/<USER>/esp32/esp-idf/components/hal/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/hal/include -I/home/<USER>/esp32/esp-idf/components/bootloader_support/include -I/home/<USER>/esp32/esp-idf/components/bootloader_support/bootloader_flash/include -I/home/<USER>/esp32/esp-idf/components/bootloader_support/private_include -I/home/<USER>/esp32/esp-idf/components/esp_security/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -O2 -fmacro-prefix-map=/home/<USER>/esp32/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=/home/<USER>/esp32/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/chip_info.c.obj -c /home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/chip_info.c", "file": "/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/chip_info.c"}, {"directory": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader", "command": "/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.1-dirty\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/home/<USER>/bysx/xiaozhi-esp32/build/bootloader/config -I/home/<USER>/esp32/esp-idf/components/esp_common/include -I/home/<USER>/esp32/esp-idf/components/log/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/dma/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/ldo/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/debug_probe/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/newlib/platform_include -I/home/<USER>/esp32/esp-idf/components/xtensa/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/xtensa/include -I/home/<USER>/esp32/esp-idf/components/xtensa/deprecated_include -I/home/<USER>/esp32/esp-idf/components/soc/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/register -I/home/<USER>/esp32/esp-idf/components/efuse/include -I/home/<USER>/esp32/esp-idf/components/efuse/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/bootloader_support/include -I/home/<USER>/esp32/esp-idf/components/bootloader_support/bootloader_flash/include -I/home/<USER>/esp32/esp-idf/components/bootloader_support/private_include -I/home/<USER>/esp32/esp-idf/components/spi_flash/include -I/home/<USER>/esp32/esp-idf/components/hal/platform_port/include -I/home/<USER>/esp32/esp-idf/components/hal/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/hal/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -O2 -fmacro-prefix-map=/home/<USER>/esp32/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=/home/<USER>/esp32/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/esp_common/CMakeFiles/__idf_esp_common.dir/src/esp_err_to_name.c.obj -c /home/<USER>/esp32/esp-idf/components/esp_common/src/esp_err_to_name.c", "file": "/home/<USER>/esp32/esp-idf/components/esp_common/src/esp_err_to_name.c"}, {"directory": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader", "command": "/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.1-dirty\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/home/<USER>/bysx/xiaozhi-esp32/build/bootloader/config -I/home/<USER>/esp32/esp-idf/components/esp_rom/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3 -I/home/<USER>/esp32/esp-idf/components/log/include -I/home/<USER>/esp32/esp-idf/components/esp_common/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/dma/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/ldo/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/debug_probe/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/newlib/platform_include -I/home/<USER>/esp32/esp-idf/components/xtensa/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/xtensa/include -I/home/<USER>/esp32/esp-idf/components/xtensa/deprecated_include -I/home/<USER>/esp32/esp-idf/components/soc/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/register -I/home/<USER>/esp32/esp-idf/components/hal/platform_port/include -I/home/<USER>/esp32/esp-idf/components/hal/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/hal/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -O2 -fmacro-prefix-map=/home/<USER>/esp32/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=/home/<USER>/esp32/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_sys.c.obj -c /home/<USER>/esp32/esp-idf/components/esp_rom/patches/esp_rom_sys.c", "file": "/home/<USER>/esp32/esp-idf/components/esp_rom/patches/esp_rom_sys.c"}, {"directory": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader", "command": "/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.1-dirty\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/home/<USER>/bysx/xiaozhi-esp32/build/bootloader/config -I/home/<USER>/esp32/esp-idf/components/esp_rom/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3 -I/home/<USER>/esp32/esp-idf/components/log/include -I/home/<USER>/esp32/esp-idf/components/esp_common/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/dma/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/ldo/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/debug_probe/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/newlib/platform_include -I/home/<USER>/esp32/esp-idf/components/xtensa/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/xtensa/include -I/home/<USER>/esp32/esp-idf/components/xtensa/deprecated_include -I/home/<USER>/esp32/esp-idf/components/soc/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/register -I/home/<USER>/esp32/esp-idf/components/hal/platform_port/include -I/home/<USER>/esp32/esp-idf/components/hal/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/hal/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -O2 -fmacro-prefix-map=/home/<USER>/esp32/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=/home/<USER>/esp32/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_print.c.obj -c /home/<USER>/esp32/esp-idf/components/esp_rom/patches/esp_rom_print.c", "file": "/home/<USER>/esp32/esp-idf/components/esp_rom/patches/esp_rom_print.c"}, {"directory": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader", "command": "/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.1-dirty\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/home/<USER>/bysx/xiaozhi-esp32/build/bootloader/config -I/home/<USER>/esp32/esp-idf/components/esp_rom/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3 -I/home/<USER>/esp32/esp-idf/components/log/include -I/home/<USER>/esp32/esp-idf/components/esp_common/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/dma/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/ldo/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/debug_probe/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/newlib/platform_include -I/home/<USER>/esp32/esp-idf/components/xtensa/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/xtensa/include -I/home/<USER>/esp32/esp-idf/components/xtensa/deprecated_include -I/home/<USER>/esp32/esp-idf/components/soc/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/register -I/home/<USER>/esp32/esp-idf/components/hal/platform_port/include -I/home/<USER>/esp32/esp-idf/components/hal/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/hal/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -O2 -fmacro-prefix-map=/home/<USER>/esp32/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=/home/<USER>/esp32/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_crc.c.obj -c /home/<USER>/esp32/esp-idf/components/esp_rom/patches/esp_rom_crc.c", "file": "/home/<USER>/esp32/esp-idf/components/esp_rom/patches/esp_rom_crc.c"}, {"directory": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader", "command": "/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.1-dirty\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/home/<USER>/bysx/xiaozhi-esp32/build/bootloader/config -I/home/<USER>/esp32/esp-idf/components/esp_rom/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3 -I/home/<USER>/esp32/esp-idf/components/log/include -I/home/<USER>/esp32/esp-idf/components/esp_common/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/dma/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/ldo/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/debug_probe/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/newlib/platform_include -I/home/<USER>/esp32/esp-idf/components/xtensa/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/xtensa/include -I/home/<USER>/esp32/esp-idf/components/xtensa/deprecated_include -I/home/<USER>/esp32/esp-idf/components/soc/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/register -I/home/<USER>/esp32/esp-idf/components/hal/platform_port/include -I/home/<USER>/esp32/esp-idf/components/hal/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/hal/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -O2 -fmacro-prefix-map=/home/<USER>/esp32/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=/home/<USER>/esp32/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_uart.c.obj -c /home/<USER>/esp32/esp-idf/components/esp_rom/patches/esp_rom_uart.c", "file": "/home/<USER>/esp32/esp-idf/components/esp_rom/patches/esp_rom_uart.c"}, {"directory": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader", "command": "/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.1-dirty\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/home/<USER>/bysx/xiaozhi-esp32/build/bootloader/config -I/home/<USER>/esp32/esp-idf/components/esp_rom/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3 -I/home/<USER>/esp32/esp-idf/components/log/include -I/home/<USER>/esp32/esp-idf/components/esp_common/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/dma/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/ldo/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/debug_probe/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/newlib/platform_include -I/home/<USER>/esp32/esp-idf/components/xtensa/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/xtensa/include -I/home/<USER>/esp32/esp-idf/components/xtensa/deprecated_include -I/home/<USER>/esp32/esp-idf/components/soc/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/register -I/home/<USER>/esp32/esp-idf/components/hal/platform_port/include -I/home/<USER>/esp32/esp-idf/components/hal/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/hal/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -O2 -fmacro-prefix-map=/home/<USER>/esp32/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=/home/<USER>/esp32/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_spiflash.c.obj -c /home/<USER>/esp32/esp-idf/components/esp_rom/patches/esp_rom_spiflash.c", "file": "/home/<USER>/esp32/esp-idf/components/esp_rom/patches/esp_rom_spiflash.c"}, {"directory": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader", "command": "/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.1-dirty\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/home/<USER>/bysx/xiaozhi-esp32/build/bootloader/config -I/home/<USER>/esp32/esp-idf/components/esp_rom/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3 -I/home/<USER>/esp32/esp-idf/components/log/include -I/home/<USER>/esp32/esp-idf/components/esp_common/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/dma/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/ldo/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/debug_probe/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/newlib/platform_include -I/home/<USER>/esp32/esp-idf/components/xtensa/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/xtensa/include -I/home/<USER>/esp32/esp-idf/components/xtensa/deprecated_include -I/home/<USER>/esp32/esp-idf/components/soc/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/register -I/home/<USER>/esp32/esp-idf/components/hal/platform_port/include -I/home/<USER>/esp32/esp-idf/components/hal/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/hal/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -O2 -fmacro-prefix-map=/home/<USER>/esp32/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=/home/<USER>/esp32/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_efuse.c.obj -c /home/<USER>/esp32/esp-idf/components/esp_rom/patches/esp_rom_efuse.c", "file": "/home/<USER>/esp32/esp-idf/components/esp_rom/patches/esp_rom_efuse.c"}, {"directory": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader", "command": "/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.1-dirty\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/home/<USER>/bysx/xiaozhi-esp32/build/bootloader/config -I/home/<USER>/esp32/esp-idf/components/esp_rom/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3 -I/home/<USER>/esp32/esp-idf/components/log/include -I/home/<USER>/esp32/esp-idf/components/esp_common/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/dma/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/ldo/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/debug_probe/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/newlib/platform_include -I/home/<USER>/esp32/esp-idf/components/xtensa/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/xtensa/include -I/home/<USER>/esp32/esp-idf/components/xtensa/deprecated_include -I/home/<USER>/esp32/esp-idf/components/soc/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/register -I/home/<USER>/esp32/esp-idf/components/hal/platform_port/include -I/home/<USER>/esp32/esp-idf/components/hal/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/hal/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -O2 -fmacro-prefix-map=/home/<USER>/esp32/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=/home/<USER>/esp32/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_gpio.c.obj -c /home/<USER>/esp32/esp-idf/components/esp_rom/patches/esp_rom_gpio.c", "file": "/home/<USER>/esp32/esp-idf/components/esp_rom/patches/esp_rom_gpio.c"}, {"directory": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader", "command": "/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.1-dirty\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/home/<USER>/bysx/xiaozhi-esp32/build/bootloader/config -I/home/<USER>/esp32/esp-idf/components/esp_rom/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3 -I/home/<USER>/esp32/esp-idf/components/log/include -I/home/<USER>/esp32/esp-idf/components/esp_common/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/dma/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/ldo/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/debug_probe/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/newlib/platform_include -I/home/<USER>/esp32/esp-idf/components/xtensa/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/xtensa/include -I/home/<USER>/esp32/esp-idf/components/xtensa/deprecated_include -I/home/<USER>/esp32/esp-idf/components/soc/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/register -I/home/<USER>/esp32/esp-idf/components/hal/platform_port/include -I/home/<USER>/esp32/esp-idf/components/hal/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/hal/include -mlongcalls  -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -O2 -fmacro-prefix-map=/home/<USER>/esp32/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=/home/<USER>/esp32/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -o esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_longjmp.S.obj -c /home/<USER>/esp32/esp-idf/components/esp_rom/patches/esp_rom_longjmp.S", "file": "/home/<USER>/esp32/esp-idf/components/esp_rom/patches/esp_rom_longjmp.S"}, {"directory": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader", "command": "/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.1-dirty\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/home/<USER>/bysx/xiaozhi-esp32/build/bootloader/config -I/home/<USER>/esp32/esp-idf/components/esp_rom/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3 -I/home/<USER>/esp32/esp-idf/components/log/include -I/home/<USER>/esp32/esp-idf/components/esp_common/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/dma/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/ldo/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/debug_probe/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/newlib/platform_include -I/home/<USER>/esp32/esp-idf/components/xtensa/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/xtensa/include -I/home/<USER>/esp32/esp-idf/components/xtensa/deprecated_include -I/home/<USER>/esp32/esp-idf/components/soc/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/register -I/home/<USER>/esp32/esp-idf/components/hal/platform_port/include -I/home/<USER>/esp32/esp-idf/components/hal/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/hal/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -O2 -fmacro-prefix-map=/home/<USER>/esp32/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=/home/<USER>/esp32/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_systimer.c.obj -c /home/<USER>/esp32/esp-idf/components/esp_rom/patches/esp_rom_systimer.c", "file": "/home/<USER>/esp32/esp-idf/components/esp_rom/patches/esp_rom_systimer.c"}, {"directory": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader", "command": "/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.1-dirty\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/home/<USER>/bysx/xiaozhi-esp32/build/bootloader/config -I/home/<USER>/esp32/esp-idf/components/esp_rom/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3 -I/home/<USER>/esp32/esp-idf/components/log/include -I/home/<USER>/esp32/esp-idf/components/esp_common/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/dma/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/ldo/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/debug_probe/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/newlib/platform_include -I/home/<USER>/esp32/esp-idf/components/xtensa/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/xtensa/include -I/home/<USER>/esp32/esp-idf/components/xtensa/deprecated_include -I/home/<USER>/esp32/esp-idf/components/soc/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/register -I/home/<USER>/esp32/esp-idf/components/hal/platform_port/include -I/home/<USER>/esp32/esp-idf/components/hal/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/hal/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -O2 -fmacro-prefix-map=/home/<USER>/esp32/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=/home/<USER>/esp32/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_wdt.c.obj -c /home/<USER>/esp32/esp-idf/components/esp_rom/patches/esp_rom_wdt.c", "file": "/home/<USER>/esp32/esp-idf/components/esp_rom/patches/esp_rom_wdt.c"}, {"directory": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader", "command": "/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.1-dirty\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/home/<USER>/bysx/xiaozhi-esp32/build/bootloader/config -I/home/<USER>/esp32/esp-idf/components/esp_rom/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3 -I/home/<USER>/esp32/esp-idf/components/log/include -I/home/<USER>/esp32/esp-idf/components/esp_common/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/dma/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/ldo/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/debug_probe/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/newlib/platform_include -I/home/<USER>/esp32/esp-idf/components/xtensa/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/xtensa/include -I/home/<USER>/esp32/esp-idf/components/xtensa/deprecated_include -I/home/<USER>/esp32/esp-idf/components/soc/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/register -I/home/<USER>/esp32/esp-idf/components/hal/platform_port/include -I/home/<USER>/esp32/esp-idf/components/hal/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/hal/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -O2 -fmacro-prefix-map=/home/<USER>/esp32/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=/home/<USER>/esp32/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_cache_esp32s2_esp32s3.c.obj -c /home/<USER>/esp32/esp-idf/components/esp_rom/patches/esp_rom_cache_esp32s2_esp32s3.c", "file": "/home/<USER>/esp32/esp-idf/components/esp_rom/patches/esp_rom_cache_esp32s2_esp32s3.c"}, {"directory": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader", "command": "/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.1-dirty\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/home/<USER>/bysx/xiaozhi-esp32/build/bootloader/config -I/home/<USER>/esp32/esp-idf/components/esp_rom/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3 -I/home/<USER>/esp32/esp-idf/components/log/include -I/home/<USER>/esp32/esp-idf/components/esp_common/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/dma/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/ldo/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/debug_probe/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/newlib/platform_include -I/home/<USER>/esp32/esp-idf/components/xtensa/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/xtensa/include -I/home/<USER>/esp32/esp-idf/components/xtensa/deprecated_include -I/home/<USER>/esp32/esp-idf/components/soc/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/register -I/home/<USER>/esp32/esp-idf/components/hal/platform_port/include -I/home/<USER>/esp32/esp-idf/components/hal/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/hal/include -mlongcalls  -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -O2 -fmacro-prefix-map=/home/<USER>/esp32/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=/home/<USER>/esp32/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -o esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_cache_writeback_esp32s3.S.obj -c /home/<USER>/esp32/esp-idf/components/esp_rom/patches/esp_rom_cache_writeback_esp32s3.S", "file": "/home/<USER>/esp32/esp-idf/components/esp_rom/patches/esp_rom_cache_writeback_esp32s3.S"}, {"directory": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader", "command": "/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.1-dirty\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/home/<USER>/bysx/xiaozhi-esp32/build/bootloader/config -I/home/<USER>/esp32/esp-idf/components/log/include -I/home/<USER>/esp32/esp-idf/components/log/include/esp_private -I/home/<USER>/esp32/esp-idf/components/esp_rom/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_common/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/dma/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/ldo/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/debug_probe/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/newlib/platform_include -I/home/<USER>/esp32/esp-idf/components/xtensa/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/xtensa/include -I/home/<USER>/esp32/esp-idf/components/xtensa/deprecated_include -I/home/<USER>/esp32/esp-idf/components/soc/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/register -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -O2 -fmacro-prefix-map=/home/<USER>/esp32/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=/home/<USER>/esp32/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/log/CMakeFiles/__idf_log.dir/src/noos/log_timestamp.c.obj -c /home/<USER>/esp32/esp-idf/components/log/src/noos/log_timestamp.c", "file": "/home/<USER>/esp32/esp-idf/components/log/src/noos/log_timestamp.c"}, {"directory": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader", "command": "/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.1-dirty\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/home/<USER>/bysx/xiaozhi-esp32/build/bootloader/config -I/home/<USER>/esp32/esp-idf/components/log/include -I/home/<USER>/esp32/esp-idf/components/log/include/esp_private -I/home/<USER>/esp32/esp-idf/components/esp_rom/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_common/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/dma/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/ldo/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/debug_probe/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/newlib/platform_include -I/home/<USER>/esp32/esp-idf/components/xtensa/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/xtensa/include -I/home/<USER>/esp32/esp-idf/components/xtensa/deprecated_include -I/home/<USER>/esp32/esp-idf/components/soc/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/register -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -O2 -fmacro-prefix-map=/home/<USER>/esp32/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=/home/<USER>/esp32/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/log/CMakeFiles/__idf_log.dir/src/log_timestamp_common.c.obj -c /home/<USER>/esp32/esp-idf/components/log/src/log_timestamp_common.c", "file": "/home/<USER>/esp32/esp-idf/components/log/src/log_timestamp_common.c"}, {"directory": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader", "command": "/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.1-dirty\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/home/<USER>/bysx/xiaozhi-esp32/build/bootloader/config -I/home/<USER>/esp32/esp-idf/components/log/include -I/home/<USER>/esp32/esp-idf/components/log/include/esp_private -I/home/<USER>/esp32/esp-idf/components/esp_rom/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_common/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/dma/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/ldo/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/debug_probe/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/newlib/platform_include -I/home/<USER>/esp32/esp-idf/components/xtensa/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/xtensa/include -I/home/<USER>/esp32/esp-idf/components/xtensa/deprecated_include -I/home/<USER>/esp32/esp-idf/components/soc/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/register -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -O2 -fmacro-prefix-map=/home/<USER>/esp32/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=/home/<USER>/esp32/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/log/CMakeFiles/__idf_log.dir/src/noos/log_lock.c.obj -c /home/<USER>/esp32/esp-idf/components/log/src/noos/log_lock.c", "file": "/home/<USER>/esp32/esp-idf/components/log/src/noos/log_lock.c"}, {"directory": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader", "command": "/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.1-dirty\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/home/<USER>/bysx/xiaozhi-esp32/build/bootloader/config -I/home/<USER>/esp32/esp-idf/components/log/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/include/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_common/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/dma/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/ldo/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/debug_probe/include -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/newlib/platform_include -I/home/<USER>/esp32/esp-idf/components/xtensa/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/xtensa/include -I/home/<USER>/esp32/esp-idf/components/xtensa/deprecated_include -I/home/<USER>/esp32/esp-idf/components/soc/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3 -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/include -I/home/<USER>/esp32/esp-idf/components/soc/esp32s3/register -I/home/<USER>/esp32/esp-idf/components/bootloader_support/include -I/home/<USER>/esp32/esp-idf/components/bootloader_support/bootloader_flash/include -I/home/<USER>/esp32/esp-idf/components/bootloader_support/private_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -O2 -fmacro-prefix-map=/home/<USER>/esp32/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=/home/<USER>/esp32/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/main/CMakeFiles/__idf_main.dir/bootloader_start.c.obj -c /home/<USER>/esp32/esp-idf/components/bootloader/subproject/main/bootloader_start.c", "file": "/home/<USER>/esp32/esp-idf/components/bootloader/subproject/main/bootloader_start.c"}]