/* * Data converted from /home/<USER>/bysx/xiaozhi-esp32/main/assets/zh-CN/4.p3
 */
.data
#if !defined (__APPLE__) && !defined (__linux__)
.section .rodata.embedded
#endif

.global _4_p3
_4_p3:

.global _binary_4_p3_start
_binary_4_p3_start: /* for objcopy compatibility */
.byte 0x00, 0x00, 0x00, 0x55, 0x58, 0x00, 0x8b, 0xfe, 0x24, 0x3a, 0xad, 0xe2, 0x07, 0xb8, 0x5a, 0xdd
.byte 0x39, 0xaa, 0xce, 0x7b, 0x77, 0x80, 0xdf, 0x30, 0x56, 0x0c, 0x3f, 0xa9, 0x73, 0x0b, 0x21, 0x41
.byte 0x0f, 0xa3, 0x2a, 0xb1, 0xcd, 0x7a, 0xd7, 0xe1, 0x53, 0xdc, 0x77, 0x6f, 0x63, 0xdd, 0x06, 0x1d
.byte 0x7e, 0xd0, 0xea, 0xb9, 0x74, 0xa1, 0x40, 0xa4, 0xff, 0x40, 0x03, 0xb3, 0xba, 0x6e, 0xff, 0xb8
.byte 0x59, 0x50, 0xba, 0xee, 0x34, 0x6d, 0xf9, 0x9e, 0xbb, 0x80, 0xb6, 0x62, 0xbc, 0x00, 0xff, 0x43
.byte 0x20, 0x45, 0x6e, 0x37, 0x51, 0x32, 0x9a, 0x55, 0x10, 0x00, 0x00, 0x00, 0x66, 0x58, 0x0a, 0xd8
.byte 0x79, 0xd6, 0xdf, 0x01, 0x8a, 0x12, 0x63, 0x34, 0x2b, 0x52, 0xf2, 0x23, 0xfc, 0xca, 0x46, 0xb8
.byte 0xed, 0xd8, 0x19, 0xd3, 0x97, 0xd5, 0x49, 0x31, 0xc3, 0xa1, 0x42, 0x0c, 0x5d, 0x4c, 0x6d, 0x80
.byte 0x94, 0xf8, 0x6c, 0xce, 0xd0, 0xa5, 0x3e, 0x0d, 0x20, 0x09, 0x49, 0x33, 0xe9, 0xee, 0xc9, 0x1b
.byte 0x6c, 0xb1, 0x45, 0x8a, 0x91, 0xeb, 0x46, 0x05, 0x82, 0x7f, 0x3e, 0x8e, 0x82, 0x5c, 0xd8, 0x15
.byte 0xc0, 0xf4, 0xe8, 0xc2, 0xd9, 0x16, 0x46, 0x3c, 0x3e, 0xae, 0xa5, 0x28, 0x95, 0xb0, 0x22, 0x79
.byte 0xc7, 0xa8, 0xdb, 0x46, 0x24, 0x6f, 0x8e, 0xab, 0x1a, 0x54, 0x0c, 0x49, 0x35, 0xa9, 0x8a, 0x0e
.byte 0x65, 0x5e, 0x9d, 0x00, 0x00, 0x00, 0x70, 0x58, 0xe3, 0xd4, 0x44, 0x1b, 0xbf, 0x8a, 0x74, 0x5d
.byte 0x0b, 0xb3, 0x48, 0xe0, 0x48, 0x44, 0x14, 0xa7, 0xa6, 0x99, 0x1c, 0x36, 0x29, 0x9e, 0xfe, 0x07
.byte 0x72, 0x37, 0xa4, 0x47, 0x2f, 0x6a, 0x33, 0x9e, 0xea, 0x9e, 0x86, 0x63, 0x1d, 0x61, 0x12, 0x8d
.byte 0x3e, 0x9b, 0x93, 0xfd, 0xe1, 0x96, 0x76, 0x2f, 0xd9, 0x01, 0x92, 0x21, 0x00, 0x1c, 0x3f, 0xc8
.byte 0x5e, 0xd8, 0x00, 0x7f, 0xea, 0xcf, 0x81, 0xdb, 0xf1, 0xf0, 0xff, 0x1e, 0x78, 0x89, 0x4c, 0x0f
.byte 0xe0, 0xf8, 0x76, 0x19, 0xf0, 0x70, 0xeb, 0x23, 0x1c, 0xb8, 0xbe, 0xab, 0x1f, 0xfb, 0xf6, 0x5c
.byte 0x20, 0x59, 0xb5, 0xa6, 0x50, 0x0b, 0xd0, 0x6a, 0x73, 0xf3, 0xa6, 0xd5, 0x52, 0xf1, 0x7b, 0x65
.byte 0x06, 0x5f, 0x45, 0xaf, 0x43, 0x51, 0x90, 0x00, 0x00, 0x00, 0x75, 0x58, 0xe1, 0x2c, 0xd5, 0x45
.byte 0x05, 0x0f, 0xef, 0x67, 0x9d, 0x93, 0xdc, 0x11, 0x97, 0x6e, 0x09, 0x7b, 0x75, 0x8d, 0x94, 0x9d
.byte 0xce, 0x21, 0xc4, 0xe6, 0xd2, 0x1e, 0xd0, 0x62, 0x78, 0xa8, 0xfa, 0xa4, 0x03, 0x7d, 0xbf, 0xe1
.byte 0x0b, 0xf0, 0xff, 0x8d, 0x61, 0xa0, 0x5b, 0x71, 0xc7, 0x07, 0xa9, 0xd3, 0xe7, 0x71, 0x3c, 0x55
.byte 0x11, 0xe9, 0x51, 0xed, 0x86, 0x52, 0xe3, 0x96, 0xc0, 0xe1, 0x87, 0x1d, 0x5d, 0xb8, 0xad, 0x76
.byte 0x39, 0xac, 0xc4, 0x0e, 0x3d, 0x47, 0xaf, 0xa7, 0xe8, 0xcd, 0x24, 0x0b, 0xf9, 0x76, 0xd5, 0x95
.byte 0x47, 0x60, 0x69, 0xdf, 0xf4, 0x4e, 0x0b, 0x47, 0xd1, 0xfd, 0x16, 0xd9, 0xc8, 0x67, 0x6a, 0x1c
.byte 0xd7, 0x5d, 0xf8, 0xb1, 0x9b, 0xf2, 0xb1, 0xbe, 0xaa, 0x2c, 0x10, 0x55, 0x7c, 0x48, 0xd0, 0x40
.byte 0x00, 0x00, 0x00, 0x73, 0x58, 0xe5, 0x27, 0xf3, 0x8f, 0x75, 0xf1, 0x17, 0x2a, 0x0f, 0x13, 0x73
.byte 0x2f, 0x6f, 0x94, 0x5f, 0xcd, 0x1c, 0x93, 0x2a, 0x69, 0xa4, 0xdd, 0xb3, 0x9a, 0xfd, 0x19, 0x15
.byte 0x50, 0xb8, 0x2c, 0xf5, 0x65, 0xd9, 0xc0, 0xc7, 0x9e, 0xa0, 0x90, 0x65, 0xa7, 0xd4, 0xbb, 0x56
.byte 0x31, 0xb6, 0xc1, 0xff, 0x2e, 0x85, 0x3a, 0xb1, 0xc4, 0x94, 0xc5, 0x28, 0x1f, 0x48, 0xcb, 0xf2
.byte 0x22, 0x6a, 0x74, 0x31, 0xc0, 0xb3, 0x65, 0xa0, 0x7e, 0xff, 0x74, 0xd3, 0xc4, 0xdf, 0xb2, 0x5a
.byte 0xf2, 0x7c, 0x6d, 0x48, 0x90, 0x08, 0x64, 0x84, 0xcb, 0x85, 0xe8, 0xf2, 0x25, 0x19, 0x80, 0xf6
.byte 0x6a, 0x37, 0x5d, 0x73, 0x3a, 0xa6, 0x46, 0x7f, 0xa8, 0x8d, 0x5f, 0x90, 0x56, 0x02, 0x8b, 0x30
.byte 0xe0, 0x6b, 0x3d, 0x7c, 0xd7, 0x79, 0x80, 0x00, 0x00, 0x00, 0x7c, 0x58, 0xee, 0x3d, 0x75, 0xae
.byte 0xad, 0x34, 0xda, 0x7e, 0x00, 0xc2, 0x67, 0xce, 0x74, 0x37, 0x9d, 0xd6, 0x0d, 0xa5, 0x48, 0xbf
.byte 0xf3, 0x02, 0xfa, 0x46, 0x44, 0x18, 0xfa, 0xd6, 0x73, 0x97, 0x4d, 0x09, 0x89, 0x2d, 0x28, 0x4a
.byte 0x13, 0x09, 0x68, 0x13, 0x8f, 0xfe, 0x13, 0x0c, 0x78, 0x98, 0x4f, 0xd8, 0xee, 0xe1, 0x8b, 0x91
.byte 0x98, 0xbb, 0xca, 0x9a, 0xca, 0xac, 0x29, 0x98, 0x4a, 0x6b, 0x89, 0x14, 0x84, 0xca, 0xda, 0xe1
.byte 0x97, 0x07, 0x79, 0x0f, 0xbb, 0xd8, 0x4e, 0x0d, 0x7d, 0x0e, 0x18, 0x14, 0x09, 0xd3, 0x33, 0x92
.byte 0x1f, 0xc8, 0x2b, 0x36, 0x92, 0xe0, 0x0d, 0x46, 0xa5, 0xa9, 0xca, 0x9d, 0x14, 0x28, 0xcb, 0xf7
.byte 0x8c, 0x5b, 0x67, 0x53, 0xaf, 0x63, 0xc5, 0xd0, 0xad, 0xe6, 0xe1, 0x32, 0x48, 0x3e, 0x1e, 0xee
.byte 0x93, 0x50, 0x0f, 0xc7, 0x83, 0xea, 0x40, 0x00, 0x00, 0x00, 0x86, 0x58, 0xee, 0x5c, 0x94, 0xec
.byte 0x9d, 0x50, 0xc2, 0x33, 0x0e, 0x86, 0xc7, 0xba, 0x9d, 0x7a, 0x9f, 0x3c, 0xf4, 0x3c, 0xc3, 0x69
.byte 0x53, 0x19, 0x01, 0x90, 0xe5, 0x23, 0x08, 0x0c, 0x8e, 0x95, 0x1c, 0x0e, 0x8b, 0x1e, 0x49, 0x1a
.byte 0x79, 0x9d, 0xaa, 0x9d, 0x1f, 0xb4, 0xba, 0xc4, 0xd5, 0x09, 0xec, 0xf0, 0xf0, 0x64, 0xef, 0x7e
.byte 0xff, 0xde, 0xc8, 0xa4, 0x9d, 0xea, 0x9c, 0x54, 0xa1, 0xa6, 0x76, 0x22, 0x81, 0xf7, 0x15, 0xaa
.byte 0xac, 0xa0, 0x3a, 0xa3, 0xe7, 0xad, 0x1d, 0xc7, 0xb3, 0x42, 0xb8, 0x4c, 0xb5, 0x75, 0xbb, 0x63
.byte 0x71, 0x86, 0x73, 0x19, 0xd3, 0x46, 0x55, 0xd1, 0x5d, 0x88, 0xa7, 0x5a, 0x01, 0x9e, 0xd1, 0x1c
.byte 0xf1, 0xc0, 0xa7, 0x9b, 0xbe, 0x73, 0xab, 0xfa, 0x57, 0x35, 0x9f, 0xe8, 0x1f, 0x8d, 0xf5, 0xef
.byte 0x04, 0xe8, 0xff, 0x9e, 0x29, 0x4a, 0x38, 0xbb, 0x06, 0x6e, 0xd0, 0x2d, 0xd2, 0x2d, 0x39, 0xcb
.byte 0xe0, 0x00, 0x00, 0x00, 0x9c, 0x58, 0xee, 0x3e, 0x20, 0xe8, 0x42, 0x66, 0x00, 0xbf, 0xcf, 0xc3
.byte 0xf0, 0xc4, 0xd7, 0x52, 0x25, 0xf5, 0x30, 0xc9, 0x95, 0x4b, 0x08, 0x0d, 0x1a, 0x9e, 0x3c, 0xff
.byte 0x88, 0x11, 0xd5, 0x62, 0x19, 0x1d, 0x8f, 0x55, 0xd8, 0x01, 0x36, 0x1b, 0xba, 0xea, 0xda, 0xbc
.byte 0x02, 0xbc, 0xda, 0xdb, 0x4c, 0xa6, 0x9a, 0xea, 0x65, 0x39, 0x56, 0xcf, 0x3a, 0xeb, 0xb0, 0x55
.byte 0x41, 0x21, 0x16, 0xfa, 0x41, 0x2b, 0x95, 0x98, 0x2b, 0xaf, 0x94, 0xb8, 0x5d, 0x68, 0x42, 0x02
.byte 0x8f, 0x91, 0x82, 0xef, 0x37, 0x72, 0x92, 0x38, 0xb8, 0x03, 0x3a, 0x2e, 0x56, 0x90, 0x5d, 0x43
.byte 0xf3, 0x8c, 0xa5, 0xfd, 0xbe, 0x74, 0xa8, 0xd3, 0xcd, 0x41, 0xc5, 0x66, 0xf9, 0xfd, 0x8d, 0x3a
.byte 0x9c, 0xb6, 0xbf, 0xbb, 0x07, 0x1f, 0xb2, 0x47, 0xfa, 0x3a, 0x93, 0xb7, 0x5b, 0x48, 0xbe, 0x23
.byte 0x12, 0x04, 0xf9, 0xa6, 0xb3, 0x44, 0xde, 0x52, 0xb7, 0x53, 0xab, 0x50, 0xc0, 0x06, 0xf1, 0x15
.byte 0x43, 0x03, 0x29, 0x13, 0xcc, 0x32, 0x97, 0x11, 0x43, 0x15, 0x48, 0x6a, 0x7d, 0xd1, 0x92, 0x98
.byte 0x60, 0x00, 0x00, 0x00, 0x70, 0x58, 0xca, 0x9f, 0xad, 0x81, 0x61, 0xc0, 0xb1, 0x2c, 0x74, 0xe3
.byte 0x34, 0xe1, 0x2d, 0x26, 0xc9, 0xa1, 0x17, 0x1f, 0xe5, 0x86, 0x50, 0xa2, 0x24, 0x32, 0x78, 0x70
.byte 0xe7, 0x56, 0xc0, 0xd4, 0xb4, 0xc7, 0xb9, 0x48, 0x78, 0x3f, 0xe1, 0xa1, 0x98, 0x84, 0x57, 0x64
.byte 0xbc, 0xd5, 0x8c, 0x51, 0x0b, 0x4f, 0x20, 0x57, 0x76, 0x4c, 0x56, 0x19, 0xd0, 0xce, 0x84, 0x64
.byte 0x37, 0xce, 0xcd, 0xab, 0xa5, 0x31, 0xd7, 0x1d, 0x04, 0x95, 0xb7, 0xe0, 0xa6, 0xcc, 0x1e, 0x91
.byte 0x41, 0xa5, 0x81, 0x10, 0xce, 0x2b, 0x13, 0x85, 0x84, 0x96, 0x84, 0xd3, 0x8a, 0x2d, 0x2d, 0x18
.byte 0xef, 0xff, 0x93, 0xce, 0x18, 0xe3, 0x95, 0x69, 0xac, 0x08, 0xf2, 0x32, 0x58, 0xe2, 0xc8, 0x78
.byte 0xe3, 0x10, 0x4b, 0xcc, 0xa4, 0x00, 0x00, 0x00, 0x53, 0x58, 0x00, 0xf0, 0xb8, 0xae, 0x8f, 0x36
.byte 0xee, 0xce, 0x73, 0xfd, 0x2d, 0xc2, 0x10, 0x67, 0x0f, 0x9a, 0x6b, 0x51, 0x1f, 0xc8, 0x2d, 0x13
.byte 0x1b, 0xc7, 0xbf, 0x73, 0x06, 0x84, 0xb7, 0x93, 0xb6, 0xdc, 0xe6, 0x0d, 0x73, 0x9e, 0xe7, 0x1a
.byte 0xf5, 0x0d, 0xf7, 0xef, 0xd4, 0x49, 0x20, 0xee, 0x2b, 0x82, 0xe6, 0x63, 0x85, 0xb4, 0x15, 0x35
.byte 0xe9, 0xdb, 0x80, 0x58, 0xac, 0x2d, 0x1c, 0x97, 0x68, 0x37, 0x79, 0xb7, 0xde, 0x91, 0x66, 0xaf
.byte 0x71, 0x35, 0xb0, 0xba, 0x00, 0xb8, 0x83, 0xae, 0x83, 0xc1, 0x85, 0x2c, 0x00, 0x00, 0x00, 0x5b
.byte 0x58, 0x01, 0x02, 0xa2, 0x76, 0x1f, 0x57, 0x64, 0x92, 0x32, 0x04, 0xeb, 0xa1, 0xdb, 0xa9, 0x54
.byte 0x2e, 0x68, 0x9d, 0x3e, 0x4e, 0x04, 0x43, 0xe8, 0x09, 0x6d, 0xac, 0xa9, 0xd6, 0xed, 0xb8, 0x7b
.byte 0x08, 0xfd, 0x4a, 0xff, 0xa0, 0x05, 0x79, 0x5e, 0x75, 0x13, 0x22, 0x90, 0x2d, 0xa1, 0xfe, 0xc7
.byte 0xb0, 0x5c, 0x64, 0x91, 0xb1, 0x75, 0x4a, 0x1e, 0xfb, 0x7e, 0x95, 0x8b, 0xd4, 0xd0, 0x06, 0xd4
.byte 0x9b, 0xc7, 0x4e, 0x0d, 0x93, 0x47, 0xdf, 0x11, 0x03, 0xc9, 0x04, 0xbd, 0x72, 0xdc, 0x63, 0x97
.byte 0x9c, 0xfd, 0x08, 0x1c, 0xc1, 0x6c, 0x63, 0x3a, 0x26, 0xd8, 0x4a, 0x00, 0x00, 0x00, 0x3e, 0x58
.byte 0x01, 0x0e, 0x71, 0x72, 0x00, 0x20, 0x4f, 0x9c, 0x26, 0xbd, 0x2e, 0x8a, 0xe3, 0x2e, 0x2d, 0xfc
.byte 0x6e, 0x0f, 0x56, 0x23, 0x4c, 0xad, 0x8c, 0x5a, 0xfe, 0xa8, 0x03, 0x73, 0x51, 0x2c, 0x5b, 0x02
.byte 0x41, 0xba, 0x05, 0x91, 0x68, 0x63, 0x63, 0xbe, 0x66, 0x08, 0xb3, 0x0d, 0x52, 0xaf, 0xfa, 0x1c
.byte 0x0f, 0xf2, 0xbc, 0x60, 0xc6, 0x74, 0x80, 0x7c, 0xdd, 0x64, 0x0b, 0x8d, 0x10, 0x00, 0x00, 0x00
.byte 0x20, 0x58, 0x02, 0xe9, 0x43, 0xa4, 0x50, 0x86, 0x80, 0x35, 0x1d, 0xfc, 0x35, 0x62, 0xb7, 0xd1
.byte 0x08, 0xae, 0xd2, 0x88, 0x3c, 0xfc, 0xdd, 0x97, 0x5c, 0xa2, 0xf3, 0x40, 0xb4, 0x71, 0xa1, 0x6a
.byte 0x70, 0x00, 0x00, 0x00, 0x15, 0x58, 0x01, 0xf2, 0x5e, 0x72, 0x36, 0x92, 0x1f, 0x05, 0xfa, 0x47
.byte 0x8d, 0x69, 0x2c, 0x8f, 0x20, 0x0a, 0xfb, 0xfb, 0x2b, 0x20, 0x00, 0x00, 0x00, 0x15, 0x58, 0x01
.byte 0xf2, 0x5e, 0x72, 0x36, 0x92, 0x1f, 0x05, 0xfa, 0x47, 0x8d, 0x69, 0x2c, 0x8f, 0x20, 0x0a, 0xfb
.byte 0xfb, 0x2b, 0x20, 0x00, 0x00, 0x00, 0x15, 0x58, 0x01, 0xf2, 0x5e, 0x72, 0x36, 0x92, 0x1f, 0x05
.byte 0xfa, 0x47, 0x8d, 0x69, 0x2c, 0x8f, 0x20, 0x0a, 0xfb, 0xfb, 0x2b, 0x20, 0x00, 0x00, 0x00, 0x15
.byte 0x58, 0x01, 0xf2, 0x5e, 0x72, 0x36, 0x92, 0x1f, 0x05, 0xfa, 0x47, 0x8d, 0x69, 0x2c, 0x8f, 0x20
.byte 0x0a, 0xfb, 0xfb, 0x2b, 0x20

.global _binary_4_p3_end
_binary_4_p3_end: /* for objcopy compatibility */


.global _4_p3_length
_4_p3_length:
.long 1477
