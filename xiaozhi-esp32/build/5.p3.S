/* * Data converted from /home/<USER>/bysx/xiaozhi-esp32/main/assets/zh-CN/5.p3
 */
.data
#if !defined (__APPLE__) && !defined (__linux__)
.section .rodata.embedded
#endif

.global _5_p3
_5_p3:

.global _binary_5_p3_start
_binary_5_p3_start: /* for objcopy compatibility */
.byte 0x00, 0x00, 0x00, 0x56, 0x58, 0x00, 0x8c, 0x00, 0x0e, 0x26, 0x8a, 0x99, 0xb8, 0xf1, 0xa1, 0x39
.byte 0x95, 0x53, 0x09, 0x86, 0x70, 0x41, 0xdb, 0x6a, 0x51, 0x63, 0xb2, 0x9a, 0xf7, 0x46, 0x9b, 0x13
.byte 0x34, 0x58, 0x8b, 0xf8, 0x5d, 0x88, 0x71, 0x2c, 0x5b, 0x69, 0x48, 0x5f, 0x5a, 0x22, 0xa1, 0x13
.byte 0x5a, 0xa1, 0x4c, 0x11, 0x45, 0xc4, 0xc0, 0x9f, 0xd5, 0xb9, 0x00, 0x4f, 0xd5, 0x19, 0x6f, 0x35
.byte 0x0c, 0x06, 0x7d, 0xac, 0xa7, 0xdc, 0x94, 0x76, 0x77, 0x8b, 0x05, 0x64, 0x79, 0x0a, 0x95, 0x63
.byte 0x78, 0x2d, 0x7b, 0xfd, 0x68, 0x66, 0x16, 0xf3, 0xe3, 0xac, 0x00, 0x00, 0x00, 0x5f, 0x58, 0x0b
.byte 0x05, 0xd8, 0x0e, 0x21, 0x52, 0xfa, 0x44, 0xcb, 0xe7, 0xc5, 0xc4, 0x73, 0x0d, 0x87, 0xd6, 0x43
.byte 0xe7, 0x4b, 0x40, 0xd4, 0x1c, 0x1f, 0xf3, 0x95, 0x2a, 0x67, 0xe1, 0x9a, 0xe2, 0x83, 0xa1, 0x63
.byte 0x92, 0x92, 0x13, 0x80, 0x9b, 0x88, 0xac, 0xd4, 0x72, 0xdc, 0xb0, 0x9c, 0x37, 0x91, 0xde, 0x75
.byte 0xa6, 0x76, 0xcb, 0xc2, 0x78, 0x86, 0xfc, 0x50, 0x60, 0x5b, 0x6e, 0x83, 0x57, 0x46, 0xc2, 0x45
.byte 0x24, 0x75, 0x76, 0x74, 0x1e, 0x19, 0x95, 0xc3, 0x8a, 0x4d, 0x2a, 0x38, 0x34, 0x1f, 0x16, 0xb0
.byte 0xce, 0x9d, 0x60, 0xae, 0x6f, 0x61, 0x78, 0x45, 0xde, 0x16, 0xe2, 0x68, 0x08, 0x00, 0x00, 0x00
.byte 0x87, 0x58, 0xe2, 0x32, 0x84, 0x1e, 0xfa, 0x5d, 0xb8, 0x05, 0xf0, 0xf7, 0x32, 0x8a, 0x01, 0x95
.byte 0x3f, 0x38, 0xdf, 0x1b, 0xbb, 0x30, 0x9a, 0x85, 0x44, 0xd3, 0xfd, 0x76, 0xf0, 0xff, 0x09, 0xec
.byte 0x51, 0x27, 0x6d, 0x24, 0x9e, 0x63, 0x02, 0x24, 0x05, 0x2c, 0x7c, 0x96, 0xd2, 0x54, 0xdf, 0xbf
.byte 0x21, 0xaa, 0x3e, 0x7f, 0x83, 0xf8, 0x3d, 0x03, 0x0c, 0xfb, 0x99, 0x9f, 0xd9, 0xde, 0x78, 0xc6
.byte 0xe6, 0xa4, 0x9f, 0x04, 0x97, 0xc7, 0x5c, 0xc6, 0x69, 0xc2, 0x69, 0x47, 0x7f, 0x06, 0xdf, 0x8b
.byte 0x6d, 0x18, 0x1c, 0xb6, 0x8c, 0x95, 0x1b, 0x9d, 0x8a, 0xd0, 0xd1, 0x1c, 0x05, 0x2e, 0xf1, 0x94
.byte 0x88, 0xcc, 0x01, 0xe3, 0xd3, 0x37, 0xdb, 0xdc, 0x99, 0xd5, 0x58, 0xa0, 0x60, 0x87, 0x21, 0x4d
.byte 0xdf, 0x76, 0x40, 0xd1, 0xae, 0x13, 0x38, 0xb5, 0x18, 0x56, 0x1d, 0x88, 0x81, 0xe2, 0x2f, 0x17
.byte 0x87, 0xbc, 0x97, 0x5f, 0xa5, 0x64, 0xd7, 0xb4, 0x00, 0x00, 0x00, 0x6e, 0x58, 0xe9, 0x47, 0xd9
.byte 0x6e, 0xab, 0x8a, 0x69, 0x6a, 0x11, 0xbd, 0xe8, 0xa2, 0x58, 0x16, 0xc2, 0x57, 0xfb, 0xfe, 0xc5
.byte 0x66, 0xc1, 0xaf, 0x42, 0x2a, 0x9f, 0xbf, 0x64, 0xfa, 0x6f, 0x2e, 0x62, 0x86, 0xc9, 0xbe, 0x5c
.byte 0x3c, 0x42, 0x0b, 0xf7, 0xfd, 0x16, 0x04, 0x42, 0xaa, 0x6a, 0xfa, 0x9f, 0xef, 0x72, 0xe9, 0x64
.byte 0xb1, 0xb7, 0x92, 0x61, 0x96, 0xec, 0xca, 0x56, 0x45, 0x45, 0x30, 0x51, 0xb0, 0x17, 0x14, 0x70
.byte 0x1b, 0x85, 0xb1, 0x64, 0x95, 0x03, 0x5a, 0x87, 0xc5, 0xc9, 0xb3, 0x82, 0xd8, 0xe7, 0x72, 0xf8
.byte 0xc7, 0xc0, 0x7c, 0x9d, 0x56, 0x2a, 0xa4, 0x78, 0x5b, 0x5d, 0x1c, 0x79, 0x73, 0x59, 0x98, 0x7f
.byte 0xde, 0x88, 0xf2, 0x2f, 0x33, 0x75, 0xf5, 0x3f, 0x28, 0x9b, 0x00, 0x00, 0x00, 0x70, 0x58, 0xe9
.byte 0xee, 0xf6, 0xac, 0x25, 0x18, 0x71, 0xe7, 0x59, 0xc4, 0x1b, 0x70, 0xfc, 0xa8, 0x47, 0x65, 0x82
.byte 0x0b, 0x00, 0xf0, 0x29, 0xa0, 0x1c, 0x1e, 0x73, 0x8b, 0xa0, 0xb7, 0x32, 0xdb, 0x5b, 0x80, 0x48
.byte 0xfd, 0x1d, 0xaa, 0xc2, 0x66, 0xb4, 0xd0, 0x04, 0x7d, 0x43, 0x19, 0xc1, 0x31, 0x0a, 0x39, 0x72
.byte 0x79, 0xdc, 0x2d, 0x0a, 0x1b, 0xd6, 0x98, 0xe4, 0xb5, 0x55, 0x99, 0xa8, 0x3f, 0x5f, 0xf1, 0xc3
.byte 0x63, 0x2d, 0x69, 0x4a, 0x8c, 0x53, 0xbf, 0x8a, 0xd6, 0x53, 0xc4, 0xeb, 0x94, 0x6b, 0xf0, 0xce
.byte 0xde, 0x8b, 0x10, 0xf4, 0x6d, 0x80, 0x19, 0x2a, 0xa1, 0x08, 0x05, 0xa9, 0xed, 0xa4, 0x65, 0x98
.byte 0xf5, 0x8f, 0x8f, 0xdb, 0x8d, 0x7b, 0x32, 0x7d, 0xfb, 0xea, 0x73, 0x5d, 0x50, 0x8d, 0x00, 0x00
.byte 0x00, 0x83, 0x58, 0xe9, 0x92, 0xe8, 0x71, 0xc1, 0x09, 0x05, 0xaa, 0x54, 0xaa, 0x14, 0x6b, 0xb4
.byte 0xea, 0xf6, 0xfb, 0x8a, 0xfc, 0x04, 0xcf, 0xd9, 0xea, 0x54, 0x55, 0xbf, 0xd5, 0x83, 0xac, 0x7f
.byte 0xa8, 0x46, 0xae, 0x39, 0xdf, 0x41, 0x19, 0x18, 0x2d, 0x65, 0x51, 0x53, 0x75, 0xfa, 0x0a, 0x33
.byte 0xd3, 0xf5, 0xa7, 0xbb, 0xd2, 0x2f, 0xb4, 0xeb, 0xb2, 0xe3, 0x25, 0xdc, 0x0b, 0xe9, 0x4c, 0xde
.byte 0x46, 0x01, 0x68, 0x5e, 0x6f, 0x9c, 0x95, 0xcc, 0xe7, 0x15, 0x8c, 0x8e, 0xcd, 0xc5, 0x6f, 0x49
.byte 0x10, 0x91, 0x58, 0x6f, 0xa8, 0x5c, 0x69, 0x25, 0x99, 0x21, 0xbc, 0xfb, 0xfe, 0x2f, 0x8c, 0x89
.byte 0xd3, 0xe5, 0xc5, 0x07, 0x1f, 0x66, 0x9c, 0xa7, 0x76, 0x1e, 0x65, 0xa5, 0x85, 0x71, 0x02, 0x1c
.byte 0xab, 0x1a, 0x96, 0xc0, 0x24, 0x68, 0x78, 0xf1, 0x76, 0x30, 0xac, 0x47, 0x9b, 0x9a, 0xd6, 0x76
.byte 0x30, 0x25, 0x08, 0x96, 0xf4, 0x00, 0x00, 0x00, 0x7e, 0x58, 0xe9, 0xf5, 0x8e, 0x53, 0x0a, 0xd1
.byte 0x7c, 0x96, 0x76, 0x36, 0x22, 0x5f, 0xb9, 0x17, 0x63, 0x9d, 0x51, 0x0d, 0xac, 0xcd, 0x5d, 0xa5
.byte 0x59, 0x4a, 0x00, 0xb2, 0x43, 0x04, 0x17, 0x13, 0x10, 0x8e, 0xe4, 0x61, 0x0b, 0x87, 0x17, 0x25
.byte 0x42, 0x16, 0x12, 0x0b, 0x03, 0x30, 0x78, 0x98, 0x44, 0x29, 0xf7, 0x7b, 0xe8, 0x0c, 0x71, 0x23
.byte 0x95, 0x91, 0xa7, 0x69, 0xaa, 0x94, 0x31, 0x93, 0xd9, 0x70, 0x0f, 0x12, 0xe5, 0x68, 0x1b, 0xc8
.byte 0xb1, 0x15, 0x3d, 0x93, 0xf9, 0xb3, 0xd1, 0x9f, 0x09, 0x73, 0xf0, 0xfb, 0x9c, 0xa9, 0x23, 0x87
.byte 0x01, 0xab, 0x28, 0x28, 0x50, 0x59, 0xd4, 0x41, 0x6a, 0x40, 0x45, 0xf9, 0x7f, 0x4c, 0xdd, 0x60
.byte 0xe2, 0xe5, 0x5a, 0x58, 0x3a, 0xac, 0xb9, 0x57, 0x82, 0xb1, 0x87, 0xd3, 0xf4, 0x93, 0x23, 0x24
.byte 0x58, 0xd8, 0x58, 0x30, 0x22, 0x39, 0x40, 0x00, 0x00, 0x00, 0x80, 0x58, 0xea, 0x62, 0x3e, 0x5f
.byte 0xca, 0xb9, 0x46, 0x56, 0x0e, 0x37, 0xfc, 0xe4, 0x17, 0xeb, 0x2f, 0xff, 0xdd, 0x48, 0x0b, 0x3b
.byte 0x4a, 0xc1, 0xb8, 0x13, 0x0f, 0xd3, 0xba, 0x23, 0x9a, 0xdf, 0x42, 0x02, 0x04, 0xd0, 0xf3, 0x91
.byte 0xf4, 0xd7, 0x86, 0x2c, 0x96, 0xca, 0xfa, 0x64, 0xa8, 0x12, 0x99, 0x0b, 0x49, 0xef, 0x00, 0x7f
.byte 0xff, 0xe6, 0x59, 0x47, 0xa5, 0x1a, 0x5c, 0x55, 0xaa, 0xde, 0x16, 0x0f, 0x0a, 0xc4, 0xa8, 0xf1
.byte 0xff, 0x22, 0xc9, 0xc9, 0x2e, 0x2b, 0x02, 0x18, 0x18, 0x18, 0x51, 0x37, 0xe4, 0xa4, 0x11, 0x9e
.byte 0x61, 0x2a, 0x46, 0x95, 0x27, 0xc7, 0xe6, 0xcc, 0xb7, 0xf6, 0x35, 0xac, 0xcc, 0x54, 0xb5, 0xe4
.byte 0xa0, 0xbc, 0xb5, 0xbb, 0x11, 0xcf, 0xde, 0x83, 0x9f, 0x2e, 0x3a, 0x01, 0x22, 0x27, 0xe2, 0x8f
.byte 0x34, 0x56, 0x34, 0x1b, 0x8d, 0x4a, 0x8b, 0x87, 0xb3, 0xa8, 0xe6, 0x00, 0x00, 0x00, 0x69, 0x58
.byte 0x87, 0x90, 0x32, 0x9c, 0x5e, 0x6d, 0x1d, 0x81, 0x2e, 0xb4, 0xcb, 0xe8, 0xf4, 0xaf, 0xa4, 0x32
.byte 0xe1, 0x66, 0x5f, 0x6f, 0xad, 0xed, 0xa6, 0x3d, 0x16, 0x66, 0x4c, 0x38, 0x67, 0xbd, 0x02, 0x90
.byte 0x61, 0x38, 0x42, 0x54, 0x49, 0xca, 0x5f, 0xde, 0xd4, 0x02, 0xda, 0x8c, 0x38, 0xd8, 0x3d, 0xe6
.byte 0x50, 0x62, 0xa4, 0xce, 0x9b, 0xa1, 0x0c, 0xba, 0x05, 0x25, 0x5c, 0x6c, 0xb0, 0xa8, 0x8c, 0xb5
.byte 0x73, 0xba, 0xc1, 0xd2, 0x03, 0x8d, 0xf0, 0x57, 0x70, 0xc4, 0x55, 0x5d, 0xf2, 0x21, 0x87, 0x98
.byte 0x32, 0x33, 0x42, 0xea, 0xa1, 0x39, 0x4c, 0x71, 0xf4, 0xc6, 0x4f, 0xa8, 0xb5, 0x69, 0xb5, 0x91
.byte 0x01, 0xbb, 0x3b, 0x10, 0x8e, 0x06, 0xf2, 0x70, 0x00, 0x00, 0x00, 0x54, 0x58, 0x01, 0x1a, 0x20
.byte 0x00, 0xce, 0xdd, 0x3b, 0x3a, 0xcc, 0x2f, 0x91, 0xfe, 0x20, 0xde, 0x85, 0xf5, 0x52, 0x81, 0x66
.byte 0xa2, 0x84, 0xa2, 0xf5, 0x39, 0x9d, 0x46, 0x99, 0xc9, 0xa6, 0xb4, 0xd4, 0xab, 0xec, 0x47, 0xb5
.byte 0xb1, 0x96, 0x55, 0x81, 0x5b, 0x0a, 0x72, 0xa3, 0x47, 0x09, 0xf9, 0xd7, 0xcf, 0xf3, 0x5f, 0x25
.byte 0x27, 0xdc, 0x3c, 0x16, 0x87, 0x90, 0x78, 0x42, 0x28, 0xe2, 0xd2, 0x1d, 0xe5, 0xe6, 0x7b, 0x4e
.byte 0x86, 0xcf, 0x5f, 0xd0, 0x4c, 0xed, 0xaa, 0x95, 0x63, 0xa6, 0x28, 0x9b, 0x04, 0x6a, 0x79, 0xf8
.byte 0x00, 0x00, 0x00, 0x66, 0x58, 0x0b, 0x51, 0xc6, 0x5e, 0x29, 0xb4, 0xd4, 0xc5, 0xb4, 0x1b, 0xcd
.byte 0x48, 0x47, 0xa0, 0x25, 0x25, 0x54, 0xcd, 0xd7, 0x27, 0x92, 0x85, 0x34, 0x19, 0xec, 0x7f, 0xc6
.byte 0x6e, 0x4a, 0x33, 0x35, 0xe1, 0xd1, 0x44, 0x86, 0xb0, 0xd4, 0x06, 0x1d, 0x7c, 0x99, 0x24, 0x46
.byte 0x86, 0xe5, 0xff, 0x95, 0xcc, 0x73, 0x57, 0x3c, 0x95, 0x10, 0xf8, 0xae, 0xc3, 0x64, 0x0c, 0xa1
.byte 0x21, 0x40, 0x4f, 0x87, 0x11, 0xcd, 0x57, 0xa6, 0xb9, 0xb8, 0xf0, 0x8e, 0x9e, 0x6c, 0x80, 0xdb
.byte 0xe6, 0x40, 0x95, 0xd9, 0xab, 0x44, 0xe4, 0x0a, 0xe0, 0xe7, 0xbd, 0x15, 0xb9, 0xd3, 0x4d, 0xba
.byte 0x9a, 0xac, 0x86, 0xa1, 0x28, 0x82, 0x96, 0x59, 0x01, 0x38, 0x00, 0x00, 0x00, 0x41, 0x58, 0x01
.byte 0x29, 0x71, 0x52, 0x30, 0x2b, 0x7d, 0x02, 0x28, 0x4d, 0xc6, 0xf2, 0xe3, 0x21, 0xcd, 0xfc, 0x74
.byte 0xfb, 0xaf, 0x26, 0x42, 0xa8, 0x60, 0xe2, 0xa0, 0xae, 0x04, 0x93, 0xae, 0x16, 0xb7, 0xc8, 0x10
.byte 0x9f, 0x29, 0x63, 0xe1, 0x4f, 0xac, 0x14, 0xdc, 0x09, 0xb3, 0x7d, 0x22, 0xfe, 0xad, 0x3d, 0x4b
.byte 0x29, 0x89, 0xe3, 0x53, 0x4c, 0x5c, 0xcb, 0x0e, 0xe0, 0x86, 0xf9, 0x35, 0xcd, 0x6d, 0xf0, 0x00
.byte 0x00, 0x00, 0x2f, 0x58, 0x03, 0x22, 0xf5, 0x54, 0x6a, 0x9a, 0x07, 0x15, 0x41, 0xa1, 0xe2, 0x12
.byte 0x8b, 0x57, 0x35, 0x59, 0x19, 0x7b, 0xf2, 0xdb, 0xb5, 0x8b, 0x1c, 0x1d, 0xd0, 0xf9, 0xa7, 0xc4
.byte 0x59, 0x28, 0xea, 0x12, 0x70, 0x0c, 0xae, 0xb5, 0x48, 0xdb, 0xf7, 0x1e, 0x17, 0x00, 0x6e, 0xa0
.byte 0xe5, 0x30, 0x00, 0x00, 0x00, 0x15, 0x58, 0x01, 0xf2, 0x5e, 0x72, 0x36, 0x92, 0x1f, 0x05, 0xfa
.byte 0x47, 0x8d, 0x69, 0x2c, 0x8f, 0x20, 0x0a, 0xfb, 0xfb, 0x2b, 0x20, 0x00, 0x00, 0x00, 0x15, 0x58
.byte 0x01, 0xf2, 0x5e, 0x72, 0x36, 0x92, 0x1f, 0x05, 0xfa, 0x47, 0x8d, 0x69, 0x2c, 0x8f, 0x20, 0x0a
.byte 0xfb, 0xfb, 0x2b, 0x20, 0x00, 0x00, 0x00, 0x15, 0x58, 0x01, 0xf2, 0x5e, 0x72, 0x36, 0x92, 0x1f
.byte 0x05, 0xfa, 0x47, 0x8d, 0x69, 0x2c, 0x8f, 0x20, 0x0a, 0xfb, 0xfb, 0x2b, 0x20, 0x00, 0x00, 0x00
.byte 0x15, 0x58, 0x01, 0xf2, 0x5e, 0x72, 0x36, 0x92, 0x1f, 0x05, 0xfa, 0x47, 0x8d, 0x69, 0x2c, 0x8f
.byte 0x20, 0x0a, 0xfb, 0xfb, 0x2b, 0x20

.global _binary_5_p3_end
_binary_5_p3_end: /* for objcopy compatibility */


.global _5_p3_length
_5_p3_length:
.long 1478
