/* * Data converted from /home/<USER>/bysx/xiaozhi-esp32/main/assets/zh-CN/9.p3
 */
.data
#if !defined (__APPLE__) && !defined (__linux__)
.section .rodata.embedded
#endif

.global _9_p3
_9_p3:

.global _binary_9_p3_start
_binary_9_p3_start: /* for objcopy compatibility */
.byte 0x00, 0x00, 0x00, 0x4d, 0x58, 0x00, 0x5e, 0x52, 0x05, 0x3d, 0x90, 0x2a, 0x3d, 0x28, 0x67, 0x10
.byte 0xbc, 0x1b, 0xab, 0xd7, 0x75, 0x1d, 0x3c, 0xcc, 0x05, 0x2d, 0xe2, 0x83, 0x49, 0xde, 0x17, 0xcf
.byte 0x13, 0xb9, 0x66, 0xdd, 0xd4, 0x8f, 0x71, 0x03, 0x52, 0xab, 0x08, 0x8f, 0xdd, 0xcc, 0xf4, 0xff
.byte 0x16, 0x4f, 0x16, 0xe2, 0xe4, 0x5d, 0x12, 0x2c, 0x97, 0x52, 0x95, 0xac, 0xfa, 0xe9, 0xec, 0x0a
.byte 0xab, 0x85, 0xd5, 0x33, 0xb8, 0x24, 0x18, 0x34, 0x17, 0x09, 0xa4, 0x76, 0x3f, 0x91, 0xd6, 0x3b
.byte 0x08, 0x00, 0x00, 0x00, 0x54, 0x58, 0x09, 0x21, 0xfd, 0x1f, 0x18, 0xc0, 0x47, 0x9d, 0x4d, 0xa8
.byte 0x51, 0xd5, 0xa9, 0xa6, 0x00, 0x63, 0x27, 0x20, 0xd0, 0xc7, 0x10, 0x4f, 0xd6, 0xbb, 0x88, 0xd5
.byte 0x72, 0x3d, 0x34, 0x6a, 0x68, 0xe6, 0x6e, 0x3e, 0x1b, 0x7f, 0xac, 0x24, 0x4d, 0xba, 0x9e, 0xe9
.byte 0x1c, 0x73, 0x55, 0xdc, 0x4b, 0x19, 0xf0, 0xda, 0x29, 0x28, 0x4a, 0x1d, 0xc1, 0x21, 0x52, 0x97
.byte 0x00, 0xa1, 0x7e, 0xc2, 0xa6, 0x56, 0x1a, 0xc7, 0x6c, 0x1c, 0x5d, 0x11, 0x4a, 0xd6, 0x09, 0x2e
.byte 0xee, 0xa6, 0x56, 0x16, 0x7c, 0x14, 0xb4, 0x2b, 0xf8, 0x00, 0x00, 0x00, 0x78, 0x58, 0xe0, 0x23
.byte 0x3e, 0xf3, 0x3e, 0x4f, 0x77, 0xf0, 0x5c, 0x90, 0xeb, 0x24, 0x9a, 0x67, 0x59, 0x25, 0xfb, 0x3f
.byte 0xd9, 0xa5, 0x55, 0xee, 0x95, 0x1b, 0x8a, 0xa2, 0x30, 0x3f, 0xea, 0x09, 0x53, 0xdb, 0x20, 0x4c
.byte 0xcb, 0xb4, 0xcf, 0x52, 0x61, 0xfd, 0x28, 0x37, 0x18, 0x73, 0x7f, 0xe1, 0x22, 0xd8, 0xd5, 0x88
.byte 0xbc, 0xa2, 0x22, 0xe9, 0xbf, 0x1b, 0x1a, 0xef, 0x89, 0x31, 0x08, 0x62, 0x3d, 0x4a, 0x1a, 0xa6
.byte 0x7f, 0x46, 0x00, 0xfe, 0xd5, 0xf8, 0x1f, 0xd3, 0x24, 0xb4, 0x30, 0x99, 0xd0, 0xf5, 0x26, 0x4f
.byte 0x72, 0x76, 0x6f, 0xb6, 0x96, 0x4f, 0xac, 0x22, 0x01, 0x67, 0x70, 0xb2, 0xbd, 0x4d, 0x7d, 0x58
.byte 0x73, 0x06, 0xa1, 0xf8, 0x2f, 0x11, 0xbf, 0xc7, 0x51, 0x3b, 0xe3, 0x65, 0xae, 0x4f, 0x43, 0xcf
.byte 0xcf, 0x04, 0x93, 0xcf, 0xf0, 0x00, 0x00, 0x00, 0x7a, 0x58, 0xe5, 0x95, 0x66, 0x52, 0x18, 0x73
.byte 0x55, 0x07, 0x8d, 0xe4, 0x1d, 0xaa, 0x95, 0x98, 0x5b, 0xec, 0x5c, 0xc2, 0x8c, 0x4e, 0x7d, 0x95
.byte 0x30, 0x9d, 0xba, 0xcb, 0x0d, 0x74, 0xe2, 0x19, 0xc3, 0xa4, 0x21, 0x7e, 0x4d, 0x57, 0xc6, 0x94
.byte 0x21, 0xe6, 0x23, 0xe7, 0x80, 0x03, 0x6f, 0x5c, 0xfb, 0x21, 0x04, 0xf8, 0x71, 0x24, 0xca, 0x0a
.byte 0x21, 0xae, 0x73, 0xe3, 0x92, 0xc7, 0x62, 0xcd, 0x83, 0x85, 0x2a, 0x4b, 0x18, 0xf5, 0x46, 0xd8
.byte 0xe6, 0x48, 0xa5, 0xa3, 0x4a, 0x16, 0xd9, 0x88, 0x31, 0x36, 0x75, 0x29, 0x87, 0x62, 0x12, 0x2a
.byte 0x8c, 0xda, 0x36, 0xf9, 0xb9, 0xa2, 0x2c, 0x86, 0x15, 0xd6, 0x24, 0x06, 0x60, 0x43, 0x7c, 0xb4
.byte 0xac, 0x0c, 0x35, 0x9f, 0xf5, 0x3e, 0x0f, 0x90, 0x51, 0x13, 0x71, 0x6e, 0xff, 0xf1, 0x0b, 0x4f
.byte 0x3b, 0x09, 0xb3, 0x00, 0x00, 0x00, 0x7e, 0x58, 0xed, 0xcf, 0x5d, 0x0f, 0x91, 0x2c, 0xb5, 0xd2
.byte 0x1c, 0xbb, 0x93, 0x41, 0x4a, 0xc3, 0xd8, 0xc3, 0x6d, 0x35, 0xe5, 0x96, 0xcc, 0x1c, 0xf1, 0xcb
.byte 0x56, 0xb1, 0x14, 0x7b, 0x66, 0x0d, 0x13, 0x87, 0x8b, 0xef, 0x7d, 0xfd, 0xea, 0x80, 0xe7, 0x16
.byte 0x76, 0x9b, 0x18, 0xce, 0xc9, 0xce, 0x2f, 0x1d, 0x50, 0x4c, 0xbe, 0x1b, 0xbb, 0xc5, 0x2d, 0x04
.byte 0xb3, 0x6e, 0x27, 0xd0, 0x9e, 0x64, 0x93, 0x3b, 0xdf, 0xc2, 0x14, 0x57, 0xcb, 0x5f, 0x06, 0x28
.byte 0xba, 0x71, 0xc8, 0xa1, 0x52, 0x79, 0x45, 0x58, 0x52, 0x50, 0x0e, 0x20, 0x10, 0xab, 0x5f, 0xce
.byte 0x6d, 0x9c, 0x46, 0xf8, 0x0e, 0xb5, 0xb8, 0xcc, 0x9c, 0x0d, 0x64, 0x85, 0x98, 0xdc, 0x88, 0x42
.byte 0x65, 0x94, 0xa3, 0xe8, 0x03, 0x4f, 0x2d, 0x4f, 0xfc, 0xfb, 0xd0, 0x2f, 0xb4, 0xad, 0x56, 0xbe
.byte 0x19, 0x46, 0x63, 0x40, 0x40, 0x00, 0x00, 0x00, 0x76, 0x58, 0xee, 0x1a, 0x1f, 0x2b, 0xe6, 0xf6
.byte 0x98, 0x5c, 0xab, 0xca, 0xbf, 0x64, 0x1d, 0xf7, 0xe4, 0xab, 0xb6, 0x81, 0xa3, 0xc0, 0x44, 0xd0
.byte 0x40, 0xdc, 0x41, 0x5e, 0xde, 0x27, 0xd8, 0xe8, 0x98, 0x7c, 0x27, 0xf3, 0x07, 0x73, 0x36, 0x01
.byte 0xea, 0xd0, 0x3d, 0x96, 0x8a, 0xfa, 0xb3, 0xf9, 0xe3, 0x46, 0xc0, 0x1a, 0x26, 0x22, 0x30, 0xbc
.byte 0xb7, 0x2c, 0x49, 0x7e, 0xd8, 0x62, 0xa6, 0x57, 0xf3, 0xef, 0xc3, 0x7e, 0xd8, 0xf9, 0x1b, 0x2a
.byte 0xf5, 0x13, 0x18, 0xc0, 0xbf, 0x62, 0x5d, 0x73, 0x27, 0x1d, 0x8a, 0x5a, 0xb5, 0x58, 0x8c, 0x5e
.byte 0x2c, 0x8a, 0x11, 0x13, 0x9a, 0x6e, 0xf7, 0x62, 0x8c, 0x64, 0x2d, 0xce, 0x13, 0xb8, 0xd1, 0x2c
.byte 0x4f, 0xe5, 0xe8, 0x9a, 0x20, 0x9f, 0xfa, 0x4e, 0x79, 0xc5, 0xed, 0x72, 0xd3, 0x58, 0x9a, 0x00
.byte 0x00, 0x00, 0x84, 0x58, 0xec, 0xd9, 0x51, 0xfa, 0x53, 0x96, 0x4b, 0x3b, 0xb6, 0x66, 0x7d, 0x4d
.byte 0xa3, 0x63, 0x91, 0x7f, 0xb0, 0x31, 0x31, 0xec, 0x3e, 0x4a, 0x26, 0x71, 0x66, 0xc7, 0xd1, 0x39
.byte 0x84, 0xd7, 0x97, 0x34, 0x42, 0xfe, 0xeb, 0xfd, 0x27, 0xf8, 0xc3, 0x42, 0xe2, 0x8b, 0xd8, 0x77
.byte 0x5e, 0x87, 0xae, 0xeb, 0xb8, 0xcf, 0xc5, 0x79, 0x9a, 0x37, 0x48, 0x2e, 0xbe, 0x93, 0x0d, 0x7d
.byte 0xc9, 0xd3, 0x57, 0x0a, 0x38, 0xd3, 0x16, 0x20, 0xf0, 0x5f, 0x08, 0x4c, 0xff, 0xa2, 0x97, 0xa7
.byte 0x0d, 0x25, 0xd1, 0x8a, 0xb6, 0x65, 0x76, 0xff, 0xfc, 0x56, 0x83, 0x14, 0x51, 0x5f, 0xaf, 0xb9
.byte 0x2a, 0xcc, 0x6a, 0x97, 0x29, 0xba, 0x4b, 0xb4, 0xe7, 0x38, 0x28, 0xb2, 0xdc, 0x69, 0xcd, 0xc8
.byte 0xd0, 0xd4, 0x98, 0x5d, 0x89, 0x8a, 0x63, 0xc2, 0x5e, 0x27, 0x3b, 0x2d, 0xee, 0xec, 0x81, 0xd0
.byte 0x0e, 0x48, 0x7c, 0x01, 0xd5, 0xa0, 0xc0, 0x00, 0x00, 0x00, 0x58, 0x58, 0x80, 0x98, 0x40, 0x06
.byte 0xf2, 0x93, 0x85, 0x45, 0x5a, 0xfa, 0x13, 0xa1, 0x9a, 0xd8, 0xc8, 0x22, 0x27, 0x34, 0xa4, 0x91
.byte 0xa9, 0xd9, 0x71, 0x71, 0xd5, 0xe5, 0x37, 0x51, 0x58, 0x61, 0x73, 0x15, 0x29, 0x09, 0x2c, 0xe1
.byte 0xeb, 0xe4, 0x32, 0x2a, 0x7e, 0xca, 0x56, 0x84, 0x7a, 0x62, 0xdc, 0x12, 0xcf, 0x7e, 0x87, 0x96
.byte 0xdd, 0x70, 0xfa, 0xff, 0x45, 0x8a, 0x69, 0x7e, 0xff, 0x8a, 0xa8, 0xbc, 0x28, 0x9c, 0x0f, 0x5b
.byte 0xb9, 0xec, 0x89, 0x7b, 0xe8, 0x2e, 0xb2, 0x94, 0xa5, 0xeb, 0x16, 0xa9, 0xea, 0x5a, 0x94, 0x7d
.byte 0xd0, 0x7d, 0xef, 0x00, 0x00, 0x00, 0x59, 0x58, 0x08, 0x6f, 0x0e, 0x0d, 0xd4, 0xa6, 0x27, 0xd2
.byte 0xfc, 0x57, 0xdf, 0xa8, 0x7d, 0xba, 0x84, 0xcc, 0x72, 0x4c, 0x58, 0x4c, 0x28, 0x9b, 0xe3, 0x27
.byte 0xbc, 0x90, 0xc4, 0xd9, 0xc8, 0xa3, 0x22, 0x51, 0x92, 0xe1, 0x37, 0x25, 0x31, 0xd6, 0xc5, 0x22
.byte 0xc7, 0x13, 0x0f, 0x2d, 0xf3, 0xd5, 0xb3, 0x00, 0x46, 0x8b, 0xc5, 0x1e, 0xee, 0xf7, 0xfb, 0x9f
.byte 0x2d, 0x43, 0x2d, 0x35, 0x01, 0x80, 0x2e, 0xf4, 0xbb, 0x58, 0x58, 0xcb, 0x17, 0x16, 0xc1, 0x33
.byte 0x2f, 0xd9, 0xa9, 0x23, 0x9c, 0x0d, 0x37, 0xed, 0x75, 0x27, 0x28, 0xfc, 0x5d, 0x88, 0x5b, 0xd8
.byte 0x00, 0x00, 0x00, 0x4d, 0x58, 0x09, 0x52, 0x9a, 0xeb, 0x97, 0xc6, 0xa3, 0xdf, 0xd2, 0xc2, 0x98
.byte 0xf7, 0xa2, 0x43, 0xbe, 0x5b, 0xbc, 0x76, 0x94, 0x3b, 0x75, 0x1d, 0x18, 0x25, 0x4b, 0xf4, 0x8a
.byte 0x4c, 0x3b, 0x58, 0xda, 0x42, 0xd0, 0xc6, 0xd8, 0xbd, 0xe7, 0xc0, 0x34, 0x71, 0x1e, 0xf3, 0x2a
.byte 0xca, 0x22, 0x3b, 0x5e, 0x99, 0x74, 0x77, 0x50, 0x8a, 0xa4, 0x9f, 0x15, 0x05, 0x42, 0xfe, 0xe5
.byte 0x9c, 0x5a, 0x65, 0x7a, 0xe9, 0xe0, 0x26, 0xe1, 0x9d, 0x2c, 0x37, 0x27, 0x85, 0x62, 0x65, 0xe2
.byte 0x10, 0x00, 0x00, 0x00, 0x2d, 0x58, 0x02, 0x8d, 0x41, 0x20, 0x93, 0x88, 0x96, 0x49, 0x00, 0x25
.byte 0xb2, 0x8d, 0xbb, 0x9c, 0x8a, 0xaf, 0xe6, 0x69, 0xf2, 0x1e, 0x3d, 0x6c, 0x8e, 0x23, 0x48, 0xac
.byte 0xdb, 0xf8, 0x60, 0xbc, 0x4b, 0xa5, 0xad, 0xe7, 0x88, 0x22, 0xb5, 0x68, 0xbd, 0x1b, 0x88, 0xeb
.byte 0x29, 0x60, 0x00, 0x00, 0x00, 0x1e, 0x58, 0x02, 0x23, 0xbc, 0xe4, 0x1c, 0xbe, 0xbd, 0x2f, 0x9b
.byte 0x42, 0xf7, 0x3d, 0x3c, 0xb3, 0x16, 0x38, 0x80, 0x65, 0x4f, 0x97, 0xb5, 0xec, 0xa9, 0x93, 0xfd
.byte 0xe3, 0x05, 0xfc, 0xe5, 0x00, 0x00, 0x00, 0x15, 0x58, 0x01, 0xf2, 0x5e, 0x72, 0x36, 0x92, 0x1f
.byte 0x05, 0xfa, 0x47, 0x8d, 0x69, 0x2c, 0x8f, 0x20, 0x0a, 0xfb, 0xfb, 0x2b, 0x20, 0x00, 0x00, 0x00
.byte 0x15, 0x58, 0x01, 0xf2, 0x5e, 0x72, 0x36, 0x92, 0x1f, 0x05, 0xfa, 0x47, 0x8d, 0x69, 0x2c, 0x8f
.byte 0x20, 0x0a, 0xfb, 0xfb, 0x2b, 0x20, 0x00, 0x00, 0x00, 0x15, 0x58, 0x01, 0xf2, 0x5e, 0x72, 0x36
.byte 0x92, 0x1f, 0x05, 0xfa, 0x47, 0x8d, 0x69, 0x2c, 0x8f, 0x20, 0x0a, 0xfb, 0xfb, 0x2b, 0x20

.global _binary_9_p3_end
_binary_9_p3_end: /* for objcopy compatibility */


.global _9_p3_length
_9_p3_length:
.long 1231
