/* * Data converted from /home/<USER>/bysx/xiaozhi-esp32/main/assets/zh-CN/3.p3
 */
.data
#if !defined (__APPLE__) && !defined (__linux__)
.section .rodata.embedded
#endif

.global _3_p3
_3_p3:

.global _binary_3_p3_start
_binary_3_p3_start: /* for objcopy compatibility */
.byte 0x00, 0x00, 0x00, 0x54, 0x58, 0x00, 0x75, 0x12, 0x05, 0x3d, 0x90, 0x3a, 0x69, 0xe5, 0x03, 0xfb
.byte 0x57, 0x70, 0x66, 0xbc, 0xd4, 0x8a, 0xbf, 0x0d, 0x8e, 0x3d, 0xb3, 0x10, 0x7b, 0xdc, 0x0a, 0xbb
.byte 0x5b, 0xd0, 0x11, 0xa0, 0x83, 0xf2, 0x51, 0x90, 0x75, 0x86, 0x4f, 0xd4, 0xda, 0x7c, 0xfb, 0x41
.byte 0x70, 0x0b, 0x66, 0x4a, 0x03, 0xad, 0x26, 0xe7, 0x5f, 0x82, 0x65, 0xd4, 0x84, 0xa6, 0x92, 0xc3
.byte 0xa4, 0xd1, 0xfa, 0xf1, 0x9a, 0x51, 0x7f, 0xf0, 0xab, 0x0d, 0x21, 0xcc, 0x24, 0xd3, 0x65, 0x33
.byte 0x6f, 0x07, 0xe8, 0xee, 0xdc, 0x50, 0x22, 0x30, 0x00, 0x00, 0x00, 0x67, 0x58, 0x09, 0xe3, 0x36
.byte 0x70, 0x8f, 0x19, 0x55, 0xb2, 0xc8, 0xec, 0x7c, 0x4f, 0x3d, 0x53, 0xc2, 0x58, 0x94, 0xe6, 0x91
.byte 0x26, 0x46, 0x77, 0x9c, 0x32, 0x26, 0xdd, 0x3c, 0x51, 0xf3, 0x4c, 0xc6, 0x9b, 0x21, 0x66, 0x7b
.byte 0x86, 0xe9, 0x57, 0x78, 0x6b, 0x21, 0xd7, 0xfe, 0x51, 0x67, 0x17, 0xfc, 0x0a, 0xc7, 0x8a, 0xc4
.byte 0x73, 0x7e, 0x4d, 0x8b, 0xf7, 0x38, 0x14, 0x8e, 0x73, 0xa7, 0xb7, 0xe2, 0xe8, 0xe1, 0xe4, 0x10
.byte 0x84, 0x8d, 0xfd, 0x37, 0xda, 0xb5, 0xb8, 0x89, 0x8b, 0x8d, 0xc9, 0xdd, 0x07, 0x9a, 0x1b, 0x4b
.byte 0xe4, 0x1a, 0x7a, 0xbf, 0x93, 0x2a, 0x64, 0x13, 0xcc, 0x7e, 0x8f, 0x68, 0xc1, 0xdd, 0x40, 0x72
.byte 0xda, 0xb5, 0x80, 0x00, 0x00, 0x00, 0x72, 0x58, 0xe3, 0xd9, 0xb6, 0x06, 0x11, 0xe6, 0x41, 0x54
.byte 0x8e, 0x39, 0x71, 0x66, 0x97, 0x9c, 0x3e, 0xdc, 0xa3, 0x99, 0x33, 0xbb, 0x7b, 0x60, 0x45, 0x99
.byte 0x9f, 0x17, 0xd7, 0x25, 0x76, 0x49, 0xe4, 0xce, 0xb3, 0x1d, 0x3a, 0x12, 0x05, 0x26, 0xe2, 0xa6
.byte 0x26, 0x19, 0xa5, 0x10, 0x5c, 0x65, 0x52, 0x0f, 0xc3, 0xce, 0x29, 0x2c, 0x6f, 0x0d, 0x02, 0x0a
.byte 0xe8, 0x70, 0x72, 0x91, 0x62, 0x0c, 0x86, 0x6e, 0x32, 0x15, 0x02, 0x55, 0x95, 0xdf, 0x75, 0xc6
.byte 0x7e, 0x46, 0x88, 0x39, 0x4e, 0x6d, 0x7c, 0xd1, 0xbc, 0x22, 0x30, 0xb5, 0x81, 0xdb, 0x33, 0x0e
.byte 0xbf, 0x30, 0xb0, 0x12, 0xa6, 0x88, 0x82, 0xf7, 0x77, 0x01, 0x09, 0x58, 0x92, 0x4f, 0x65, 0x13
.byte 0xc8, 0xf9, 0x11, 0x12, 0x41, 0x54, 0xad, 0xd0, 0x80, 0x00, 0x00, 0x00, 0x6b, 0x58, 0x0f, 0x5c
.byte 0xcd, 0x15, 0x9a, 0xaf, 0x28, 0xa8, 0xb9, 0xe8, 0xef, 0xfb, 0xe2, 0x54, 0x3e, 0xa8, 0xec, 0x64
.byte 0x53, 0xc0, 0x3f, 0x4a, 0xa6, 0x7f, 0x5b, 0x66, 0x16, 0x48, 0xe9, 0xec, 0x14, 0xdb, 0x8b, 0xeb
.byte 0x96, 0x16, 0x71, 0xc1, 0x09, 0x31, 0x8b, 0x3c, 0x91, 0xf8, 0xc7, 0xe5, 0xc9, 0xde, 0x86, 0xd5
.byte 0x02, 0x6f, 0x86, 0x68, 0xc8, 0x53, 0xeb, 0xf3, 0x85, 0x1c, 0x48, 0xdf, 0x42, 0x06, 0x32, 0x9c
.byte 0x8b, 0x44, 0xe1, 0x47, 0xcb, 0x4d, 0x19, 0xfb, 0xe9, 0x82, 0xd0, 0xb1, 0x06, 0x14, 0x02, 0x5c
.byte 0x12, 0x6f, 0xe0, 0x31, 0x8c, 0x6b, 0xe5, 0xcd, 0x72, 0x47, 0xa2, 0x52, 0x4e, 0x9f, 0xc0, 0xa0
.byte 0x59, 0x13, 0xa7, 0x67, 0xcf, 0xfb, 0xce, 0xd0, 0x00, 0x00, 0x00, 0x79, 0x58, 0xe0, 0xfe, 0x00
.byte 0x31, 0x19, 0x8b, 0x0d, 0xfa, 0xa5, 0x1a, 0xa7, 0xd5, 0xd0, 0xba, 0x6a, 0xf1, 0xe3, 0x65, 0xdc
.byte 0x75, 0xc5, 0x71, 0x4f, 0x28, 0xb3, 0x7e, 0x63, 0x09, 0x61, 0x09, 0xa5, 0x4e, 0xab, 0x6a, 0x14
.byte 0x18, 0x9a, 0xf9, 0xc5, 0x60, 0x41, 0x39, 0xf8, 0xa3, 0x3d, 0xcc, 0x62, 0xd0, 0xb9, 0x8c, 0xdd
.byte 0xcb, 0x67, 0xb9, 0x5d, 0x0c, 0x40, 0x9e, 0xf5, 0x57, 0x79, 0xc0, 0x9d, 0xd9, 0xc0, 0x94, 0x48
.byte 0x0f, 0xdc, 0x16, 0xd6, 0x07, 0x61, 0x5f, 0xa2, 0xe6, 0xe0, 0x1f, 0x39, 0x5b, 0x71, 0xb6, 0xee
.byte 0xd5, 0x1f, 0x32, 0xee, 0x75, 0xa5, 0xdd, 0x02, 0x1c, 0xaf, 0x48, 0x6c, 0xab, 0x29, 0xe4, 0xcd
.byte 0x2a, 0x99, 0xc5, 0x82, 0x2c, 0x8d, 0x87, 0xfa, 0x88, 0x8c, 0x6e, 0xeb, 0x2f, 0x35, 0x8f, 0x0a
.byte 0x64, 0x27, 0xf1, 0xb9, 0xa5, 0x00, 0x00, 0x00, 0x57, 0x58, 0xee, 0x78, 0xb4, 0x7c, 0x78, 0x53
.byte 0xd7, 0x08, 0x04, 0xa5, 0xd3, 0x6f, 0xaa, 0x2d, 0xff, 0x63, 0xea, 0x67, 0xb0, 0x9d, 0x1b, 0x7d
.byte 0xa2, 0xf8, 0xbd, 0x72, 0xb2, 0x21, 0xc4, 0xd1, 0x3f, 0xa8, 0x38, 0xf8, 0xa3, 0x55, 0xb8, 0x61
.byte 0x0c, 0xa7, 0x3a, 0x0c, 0xab, 0x39, 0x36, 0x3b, 0x31, 0x3a, 0x5b, 0x8e, 0x85, 0x02, 0x7a, 0x18
.byte 0xf4, 0xf5, 0x4a, 0xe7, 0x43, 0x84, 0x34, 0xd3, 0xcc, 0xf6, 0x1b, 0xf0, 0x8e, 0xf3, 0x09, 0xe8
.byte 0x79, 0x69, 0x16, 0x10, 0x9d, 0x06, 0x15, 0x97, 0xac, 0x67, 0x29, 0x87, 0xf7, 0xad, 0x29, 0xa0
.byte 0x00, 0x00, 0x00, 0x66, 0x58, 0xee, 0x27, 0x57, 0x4e, 0x4b, 0x7a, 0x11, 0xe9, 0xcf, 0xc1, 0xcc
.byte 0x9f, 0x58, 0x2b, 0xef, 0x4a, 0xa4, 0x97, 0x60, 0x30, 0x88, 0xa7, 0xe9, 0xc7, 0xae, 0x91, 0xef
.byte 0x1e, 0x43, 0xa3, 0xfd, 0x96, 0xf2, 0xf1, 0x16, 0xda, 0x36, 0xa2, 0xad, 0xd6, 0x61, 0xf8, 0xa9
.byte 0x7b, 0x7f, 0x74, 0x6d, 0x79, 0xc8, 0x6e, 0x6d, 0xad, 0x9a, 0x6f, 0xae, 0x11, 0x9e, 0x78, 0x03
.byte 0xfa, 0xf8, 0x67, 0x30, 0x78, 0x25, 0xa9, 0x40, 0x80, 0xb6, 0x19, 0x1b, 0xf9, 0x17, 0x1f, 0xec
.byte 0x20, 0xb0, 0x83, 0xfa, 0xd1, 0xa4, 0x99, 0xe6, 0x56, 0xa1, 0x96, 0xc5, 0x1b, 0xe6, 0x69, 0xfc
.byte 0xd6, 0x2d, 0xf1, 0x58, 0x82, 0xc6, 0x45, 0xb8, 0x85, 0x28, 0x00, 0x00, 0x00, 0x74, 0x58, 0xed
.byte 0x3a, 0xf0, 0xe1, 0xc2, 0x67, 0x8a, 0xf9, 0x69, 0x6d, 0x2b, 0x72, 0xec, 0xc2, 0xe1, 0xdf, 0x73
.byte 0x6b, 0x5f, 0x4d, 0xc8, 0x99, 0x9c, 0x55, 0x9b, 0xb0, 0x32, 0x21, 0x42, 0x2b, 0x97, 0x65, 0x3d
.byte 0xb7, 0x64, 0x71, 0xc7, 0xed, 0xb4, 0xc9, 0x2d, 0x4c, 0xd7, 0x92, 0xd2, 0x81, 0x0d, 0x0d, 0xf9
.byte 0x50, 0xd6, 0x9e, 0x5a, 0x5e, 0x55, 0xd2, 0x1e, 0x8e, 0x2b, 0x3d, 0x25, 0xa9, 0x44, 0xef, 0xa0
.byte 0x8a, 0x96, 0xb0, 0xba, 0x62, 0xb4, 0xbe, 0x74, 0xa0, 0x4e, 0xfa, 0x4e, 0x08, 0xb6, 0xa9, 0x27
.byte 0x9d, 0x35, 0x59, 0x45, 0x51, 0x70, 0x9d, 0x44, 0x1e, 0x95, 0x2d, 0x97, 0x64, 0x55, 0x95, 0x34
.byte 0x02, 0x51, 0x44, 0x2e, 0xcf, 0xeb, 0x10, 0x1f, 0x69, 0x35, 0x01, 0xcc, 0xee, 0xd4, 0xd0, 0x79
.byte 0x0a, 0x50, 0x00, 0x00, 0x00, 0x75, 0x58, 0xc8, 0x33, 0xc1, 0xe5, 0xcf, 0x31, 0xa9, 0x72, 0x5c
.byte 0x63, 0x53, 0xe5, 0x92, 0xc7, 0x54, 0x83, 0x86, 0xf8, 0x6e, 0x79, 0x64, 0x63, 0x5c, 0x4a, 0x74
.byte 0xd6, 0x29, 0x3f, 0x54, 0x3c, 0x62, 0x27, 0x99, 0xfd, 0x1e, 0x1d, 0x31, 0x76, 0xab, 0xba, 0x1e
.byte 0xb0, 0x8e, 0x79, 0x87, 0x5d, 0x93, 0x7e, 0x59, 0xd1, 0x37, 0xe3, 0x47, 0x92, 0x27, 0xba, 0x4d
.byte 0x48, 0xff, 0x80, 0x74, 0x5c, 0x49, 0x98, 0x55, 0xd7, 0x90, 0xae, 0xa7, 0xc6, 0x00, 0x7d, 0x61
.byte 0xc1, 0x33, 0x8d, 0xe2, 0x3a, 0x15, 0xeb, 0x01, 0xe7, 0x48, 0xf5, 0x2c, 0x9c, 0x6f, 0xa4, 0x29
.byte 0x30, 0xdd, 0x4a, 0x14, 0x0e, 0xb4, 0x38, 0x62, 0xcd, 0x11, 0x6e, 0x71, 0x15, 0x65, 0xa2, 0xa7
.byte 0xfc, 0xf0, 0x40, 0x38, 0x7b, 0xaa, 0x52, 0x07, 0x57, 0xf4, 0xbc, 0x00, 0x00, 0x00, 0x53, 0x58
.byte 0x0a, 0x69, 0x10, 0xba, 0x47, 0x05, 0xa0, 0x54, 0x9b, 0x88, 0xcd, 0x5f, 0x5a, 0x3d, 0xf7, 0xc5
.byte 0xc1, 0x76, 0x68, 0xe4, 0x6e, 0xb9, 0x87, 0x3c, 0xa6, 0x8f, 0x5b, 0x14, 0x23, 0x1d, 0x3d, 0x38
.byte 0x4c, 0x79, 0xe4, 0x41, 0x2e, 0x79, 0x40, 0x6b, 0x0e, 0x7b, 0x52, 0xa7, 0xfd, 0x66, 0x0f, 0xa6
.byte 0x82, 0xb9, 0xed, 0x3a, 0xae, 0xee, 0x61, 0x29, 0x74, 0xcf, 0xb2, 0x64, 0xa2, 0x0c, 0x9c, 0x46
.byte 0x06, 0x18, 0xf1, 0x12, 0x32, 0x5b, 0x23, 0x68, 0x46, 0x15, 0x1f, 0x92, 0xd6, 0x17, 0xf4, 0xc1
.byte 0xff, 0xdc, 0x00, 0x00, 0x00, 0x62, 0x58, 0x09, 0xe3, 0x8e, 0xb3, 0x29, 0x71, 0x54, 0xab, 0xde
.byte 0x24, 0xd9, 0x68, 0x78, 0x9e, 0x29, 0xac, 0x4e, 0x4c, 0x0d, 0x0e, 0xe6, 0x23, 0xf2, 0xf2, 0x21
.byte 0xc4, 0xea, 0xdd, 0xe1, 0xef, 0x23, 0x41, 0x1f, 0x8e, 0xff, 0x85, 0x95, 0x67, 0x1b, 0x59, 0xbd
.byte 0x85, 0x80, 0x14, 0xe4, 0x77, 0x56, 0x55, 0x39, 0xdc, 0xd3, 0xb4, 0xb0, 0x77, 0xd1, 0xd1, 0xc6
.byte 0xa8, 0x34, 0x6e, 0x78, 0xfa, 0xfe, 0x78, 0xde, 0x72, 0x7d, 0xba, 0xf6, 0x1b, 0x52, 0x2a, 0xa1
.byte 0xe7, 0xfd, 0x9b, 0xe0, 0x1a, 0x86, 0x8a, 0x2e, 0x49, 0x5c, 0xc7, 0xc3, 0xf4, 0xdc, 0x04, 0x3f
.byte 0x4d, 0xc7, 0x5d, 0x5d, 0x12, 0x8e, 0x4e, 0xe0, 0x00, 0x00, 0x00, 0x4e, 0x58, 0x01, 0x0f, 0xf6
.byte 0xed, 0xb2, 0x0b, 0x6f, 0xa4, 0x49, 0x5d, 0x6b, 0xfd, 0x8f, 0xbc, 0xe5, 0xe9, 0xdf, 0xd1, 0xc0
.byte 0xd2, 0xc9, 0x6a, 0x46, 0x07, 0xf2, 0xc5, 0xa8, 0xa9, 0xf2, 0x9d, 0x34, 0xe7, 0xc8, 0xc7, 0xb5
.byte 0x4c, 0xf9, 0xee, 0xf1, 0xc5, 0x37, 0x24, 0x72, 0xf2, 0x90, 0xe1, 0x82, 0x92, 0x17, 0x12, 0xa9
.byte 0x53, 0x89, 0x17, 0x2a, 0x43, 0xb1, 0x73, 0x78, 0x75, 0x0d, 0x46, 0x54, 0x49, 0xa1, 0x5d, 0x8f
.byte 0x47, 0x6c, 0x2b, 0x58, 0x76, 0xcc, 0x04, 0x8e, 0x91, 0x62, 0x00, 0x00, 0x00, 0x1b, 0x58, 0x02
.byte 0xfa, 0x78, 0x36, 0x44, 0xec, 0x96, 0xe7, 0x1f, 0x52, 0xf8, 0x05, 0x68, 0x3f, 0x06, 0x43, 0x30
.byte 0x12, 0xbc, 0x00, 0x60, 0xe5, 0x81, 0xb2, 0xb8, 0x80, 0x00, 0x00, 0x00, 0x15, 0x58, 0x01, 0xf2
.byte 0x5e, 0x72, 0x36, 0x92, 0x1f, 0x05, 0xfa, 0x47, 0x8d, 0x69, 0x2c, 0x8f, 0x20, 0x0a, 0xfb, 0xfb
.byte 0x2b, 0x20, 0x00, 0x00, 0x00, 0x15, 0x58, 0x01, 0xf2, 0x5e, 0x72, 0x36, 0x92, 0x1f, 0x05, 0xfa
.byte 0x47, 0x8d, 0x69, 0x2c, 0x8f, 0x20, 0x0a, 0xfb, 0xfb, 0x2b, 0x20, 0x00, 0x00, 0x00, 0x15, 0x58
.byte 0x01, 0xf2, 0x5e, 0x72, 0x36, 0x92, 0x1f, 0x05, 0xfa, 0x47, 0x8d, 0x69, 0x2c, 0x8f, 0x20, 0x0a
.byte 0xfb, 0xfb, 0x2b, 0x20

.global _binary_3_p3_end
_binary_3_p3_end: /* for objcopy compatibility */


.global _3_p3_length
_3_p3_length:
.long 1364
