[0/1] cd /home/<USER>/bysx/xiaozhi-esp32/build && /usr/bin/cmake -D "IDF_SIZE_TOOL=/home/<USER>/.espressif/python_env/idf5.4_py3.10_env/bin/python;-m;esp_idf_size" -D MAP_FILE=/home/<USER>/bysx/xiaozhi-esp32/build/xiaozhi.map -D OUTPUT_JSON= -P /home/<USER>/esp32/esp-idf/tools/cmake/run_size_tool.cmake
                            Memory Type Usage Summary                             
┏━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━┳━━━━━━━━━━┳━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━┓
┃ Memory Type/Section ┃ Used [bytes] ┃ Used [%] ┃ Remain [bytes] ┃ Total [bytes] ┃
┡━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━╇━━━━━━━━━━╇━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━┩
│ Flash Code          │      1592824 │    18.99 │        6795752 │       8388576 │
│    .text            │      1592824 │    18.99 │                │               │
│ Flash Data          │       825532 │     2.46 │       32728868 │      33554400 │
│    .rodata          │       825276 │     2.46 │                │               │
│    .appdesc         │          256 │      0.0 │                │               │
│ DIRAM               │       141123 │    41.29 │         200637 │        341760 │
│    .text            │        80079 │    23.43 │                │               │
│    .data            │        32892 │     9.62 │                │               │
│    .bss             │        27080 │     7.92 │                │               │
│    .vectors         │         1027 │      0.3 │                │               │
│ RTC FAST            │          324 │     3.96 │           7868 │          8192 │
│    .rtc_reserved    │           40 │     0.49 │                │               │
│    .force_fast      │           28 │     0.34 │                │               │
└─────────────────────┴──────────────┴──────────┴────────────────┴───────────────┘
