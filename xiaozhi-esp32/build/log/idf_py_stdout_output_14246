-- IDF_TARGET is not set, guessed 'esp32s3' from sdkconfig '/home/<USER>/bysx/xiaozhi-esp32/sdkconfig'
-- Found Git: /usr/bin/git (found version "2.34.1") 
-- The C compiler identification is GNU 14.2.0
-- The CXX compiler identification is GNU 14.2.0
-- The ASM compiler identification is GNU
-- Found assembler: /home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc
-- Detecting C compiler ABI info
-- Detecting C compiler ABI info - done
-- Check for working C compiler: /home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc - skipped
-- Detecting C compile features
-- Detecting C compile features - done
-- Detecting CXX compiler ABI info
-- Detecting CXX compiler ABI info - done
-- Check for working CXX compiler: /home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-g++ - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
-- Building ESP-IDF components for target esp32s3
NOTICE: Skipping optional dependency: waveshare/esp_lcd_jd9365_10_1
NOTICE: Skipping optional dependency: waveshare/esp_lcd_st7703
NOTICE: Skipping optional dependency: espressif/esp_lcd_ili9881c
NOTICE: Skipping optional dependency: espressif/esp_wifi_remote
NOTICE: Skipping optional dependency: espfriends/servo_dog_ctrl
NOTICE: Skipping optional dependency: waveshare/esp_lcd_jd9365_10_1
NOTICE: Skipping optional dependency: waveshare/esp_lcd_st7703
NOTICE: Skipping optional dependency: espressif/esp_lcd_ili9881c
NOTICE: Skipping optional dependency: espressif/esp_wifi_remote
NOTICE: Skipping optional dependency: espfriends/servo_dog_ctrl
NOTICE: Skipping optional dependency: waveshare/esp_lcd_jd9365_10_1
NOTICE: Skipping optional dependency: waveshare/esp_lcd_st7703
NOTICE: Skipping optional dependency: espressif/esp_lcd_ili9881c
NOTICE: Skipping optional dependency: espressif/esp_wifi_remote
NOTICE: Skipping optional dependency: espfriends/servo_dog_ctrl
NOTICE: Skipping optional dependency: waveshare/esp_lcd_jd9365_10_1
NOTICE: Skipping optional dependency: waveshare/esp_lcd_st7703
NOTICE: Skipping optional dependency: espressif/esp_lcd_ili9881c
NOTICE: Skipping optional dependency: espressif/esp_wifi_remote
NOTICE: Skipping optional dependency: espfriends/servo_dog_ctrl
NOTICE: Processing 43 dependencies:
NOTICE: [1/43] 78/esp-ml307 (3.2.4)
NOTICE: [2/43] 78/esp-opus (1.0.5)
NOTICE: [3/43] 78/esp-opus-encoder (2.4.0)
NOTICE: [4/43] 78/esp-wifi-connect (2.4.3)
NOTICE: [5/43] 78/esp_lcd_nv3023 (1.0.0)
NOTICE: [6/43] 78/xiaozhi-fonts (1.3.2)
NOTICE: [7/43] espressif/adc_battery_estimation (0.2.0)
NOTICE: [8/43] espressif/adc_mic (0.2.0)
NOTICE: [9/43] espressif/button (4.1.3)
NOTICE: [10/43] espressif/cmake_utilities (0.5.3)
NOTICE: [11/43] espressif/dl_fft (0.2.0)
NOTICE: [12/43] espressif/esp-dsp (1.6.0)
NOTICE: [13/43] espressif/esp-sr (2.1.4)
NOTICE: [14/43] espressif/esp32-camera (2.1.0)
NOTICE: [15/43] espressif/esp_codec_dev (1.3.6)
NOTICE: [16/43] espressif/esp_io_expander (1.0.1)
NOTICE: [17/43] espressif/esp_io_expander_tca9554 (2.0.0)
NOTICE: [18/43] espressif/esp_io_expander_tca95xx_16bit (2.0.0)
NOTICE: [19/43] espressif/esp_jpeg (1.3.1)
NOTICE: [20/43] espressif/esp_lcd_axs15231b (1.0.1)
NOTICE: [21/43] espressif/esp_lcd_gc9a01 (2.0.1)
NOTICE: [22/43] espressif/esp_lcd_ili9341 (1.2.0)
NOTICE: [23/43] espressif/esp_lcd_panel_io_additions (1.0.1)
NOTICE: [24/43] espressif/esp_lcd_spd2010 (1.0.2)
NOTICE: [25/43] espressif/esp_lcd_st77916 (1.0.1)
NOTICE: [26/43] espressif/esp_lcd_st7796 (1.3.2)
NOTICE: [27/43] espressif/esp_lcd_touch (1.1.2)
NOTICE: [28/43] espressif/esp_lcd_touch_cst816s (1.1.0)
NOTICE: [29/43] espressif/esp_lcd_touch_ft5x06 (1.0.7)
NOTICE: [30/43] espressif/esp_lcd_touch_gt911 (1.1.3)
NOTICE: [31/43] espressif/esp_lvgl_port (2.6.0)
NOTICE: [32/43] espressif/esp_mmap_assets (1.3.1~1)
NOTICE: [33/43] espressif/knob (1.0.0)
NOTICE: [34/43] espressif/led_strip (2.5.5)
NOTICE: [35/43] espressif2022/image_player (1.1.0~1)
NOTICE: [36/43] lvgl/lvgl (9.2.2)
NOTICE: [37/43] tny-robotics/sh1106-esp-idf (1.0.0)
NOTICE: [38/43] txp666/otto-emoji-gif-component (1.0.2)
NOTICE: [39/43] waveshare/esp_lcd_sh8601 (1.0.2)
NOTICE: [40/43] waveshare/esp_lcd_touch_cst9217 (1.0.3)
NOTICE: [41/43] wvirgil123/esp_jpeg_simd (1.0.0)
NOTICE: [42/43] wvirgil123/sscma_client (1.0.2)
NOTICE: [43/43] idf (5.4.1)
NOTICE: Skipping optional dependency: waveshare/esp_lcd_jd9365_10_1
NOTICE: Skipping optional dependency: waveshare/esp_lcd_st7703
NOTICE: Skipping optional dependency: espressif/esp_lcd_ili9881c
NOTICE: Skipping optional dependency: espressif/esp_wifi_remote
NOTICE: Skipping optional dependency: espfriends/servo_dog_ctrl
-- Project sdkconfig file /home/<USER>/bysx/xiaozhi-esp32/sdkconfig
Loading defaults file /home/<USER>/bysx/xiaozhi-esp32/sdkconfig.defaults...
Loading defaults file /home/<USER>/bysx/xiaozhi-esp32/sdkconfig.defaults.esp32s3...
-- Compiler supported targets: xtensa-esp-elf
-- Found Python3: /home/<USER>/.espressif/python_env/idf5.4_py3.10_env/bin/python (found version "3.10.12") found components: Interpreter 
-- Looking for pthread.h
-- Looking for pthread.h - found
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
-- Found Threads: TRUE  
-- Performing Test C_COMPILER_SUPPORTS_WFORMAT_SIGNEDNESS
-- Performing Test C_COMPILER_SUPPORTS_WFORMAT_SIGNEDNESS - Success
-- App "xiaozhi" version: 1.8.4
-- Adding linker script /home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/esp_system/ld/memory.ld
-- Adding linker script /home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/esp_system/ld/sections.ld.in
-- Adding linker script /home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.ld
-- Adding linker script /home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.api.ld
-- Adding linker script /home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.bt_funcs.ld
-- Adding linker script /home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.libgcc.ld
-- Adding linker script /home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.wdt.ld
-- Adding linker script /home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.version.ld
-- Adding linker script /home/<USER>/esp32/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.newlib.ld
-- Adding linker script /home/<USER>/esp32/esp-idf/components/soc/esp32s3/ld/esp32s3.peripherals.ld
-- ESP_LCD_NV3023: 1.0.0
-- ADC_BATTERY_ESTIMATION: 0.2.0
-- ADC_MIC: 0.2.0
-- BUTTON: 4.1.3
-- ESP_LCD_AXS15231B: 1.0.1
-- ESP_LCD_GC9A01: 2.0.1
-- ESP_LCD_ILI9341: 1.2.0
-- ESP_LCD_PANEL_IO_ADDITIONS: 1.0.1
-- ESP_LCD_SPD2010: 1.0.2
-- ESP_LCD_ST77916: 1.0.1
-- ESP_LCD_ST7796: 1.3.2
-- LVGL version: 9.2.2
-- ESP_MMAP_ASSETS: 1.3.1
-- KNOB: 1.0.0
-- Otto Emoji GIF Component:
--   Found 7 GIF source files
--   Include directory: /home/<USER>/bysx/xiaozhi-esp32/managed_components/txp666__otto-emoji-gif-component/include
-- ESP_LCD_SH8601: 1.0.2
-- Component idf::main will be linked with -Wl,--whole-archive
-- Components: 78__esp-ml307 78__esp-opus 78__esp-opus-encoder 78__esp-wifi-connect 78__esp_lcd_nv3023 78__xiaozhi-fonts app_trace app_update bootloader bootloader_support bt cmock console cxx driver efuse esp-tls esp_adc esp_app_format esp_bootloader_format esp_coex esp_common esp_driver_ana_cmpr esp_driver_cam esp_driver_dac esp_driver_gpio esp_driver_gptimer esp_driver_i2c esp_driver_i2s esp_driver_isp esp_driver_jpeg esp_driver_ledc esp_driver_mcpwm esp_driver_parlio esp_driver_pcnt esp_driver_ppa esp_driver_rmt esp_driver_sdio esp_driver_sdm esp_driver_sdmmc esp_driver_sdspi esp_driver_spi esp_driver_touch_sens esp_driver_tsens esp_driver_uart esp_driver_usb_serial_jtag esp_eth esp_event esp_gdbstub esp_hid esp_http_client esp_http_server esp_https_ota esp_https_server esp_hw_support esp_lcd esp_local_ctrl esp_mm esp_netif esp_netif_stack esp_partition esp_phy esp_pm esp_psram esp_ringbuf esp_rom esp_security esp_system esp_timer esp_vfs_console esp_wifi espcoredump espressif2022__image_player espressif__adc_battery_estimation espressif__adc_mic espressif__button espressif__cmake_utilities espressif__dl_fft espressif__esp-dsp espressif__esp-sr espressif__esp32-camera espressif__esp_codec_dev espressif__esp_io_expander espressif__esp_io_expander_tca9554 espressif__esp_io_expander_tca95xx_16bit espressif__esp_jpeg espressif__esp_lcd_axs15231b espressif__esp_lcd_gc9a01 espressif__esp_lcd_ili9341 espressif__esp_lcd_panel_io_additions espressif__esp_lcd_spd2010 espressif__esp_lcd_st77916 espressif__esp_lcd_st7796 espressif__esp_lcd_touch espressif__esp_lcd_touch_cst816s espressif__esp_lcd_touch_ft5x06 espressif__esp_lcd_touch_gt911 espressif__esp_lvgl_port espressif__esp_mmap_assets espressif__knob espressif__led_strip esptool_py fatfs freertos hal heap http_parser idf_test ieee802154 json log lvgl__lvgl lwip main mbedtls mqtt newlib nvs_flash nvs_sec_provider openthread partition_table perfmon protobuf-c protocomm pthread rt sdmmc soc spi_flash spiffs tcp_transport tny-robotics__sh1106-esp-idf touch_element txp666__otto-emoji-gif-component ulp unity usb vfs waveshare__esp_lcd_sh8601 waveshare__esp_lcd_touch_cst9217 wear_levelling wifi_provisioning wpa_supplicant wvirgil123__esp_jpeg_simd wvirgil123__sscma_client xtensa
-- Component paths: /home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-ml307 /home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus /home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus-encoder /home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-wifi-connect /home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp_lcd_nv3023 /home/<USER>/bysx/xiaozhi-esp32/managed_components/78__xiaozhi-fonts /home/<USER>/esp32/esp-idf/components/app_trace /home/<USER>/esp32/esp-idf/components/app_update /home/<USER>/esp32/esp-idf/components/bootloader /home/<USER>/esp32/esp-idf/components/bootloader_support /home/<USER>/esp32/esp-idf/components/bt /home/<USER>/esp32/esp-idf/components/cmock /home/<USER>/esp32/esp-idf/components/console /home/<USER>/esp32/esp-idf/components/cxx /home/<USER>/esp32/esp-idf/components/driver /home/<USER>/esp32/esp-idf/components/efuse /home/<USER>/esp32/esp-idf/components/esp-tls /home/<USER>/esp32/esp-idf/components/esp_adc /home/<USER>/esp32/esp-idf/components/esp_app_format /home/<USER>/esp32/esp-idf/components/esp_bootloader_format /home/<USER>/esp32/esp-idf/components/esp_coex /home/<USER>/esp32/esp-idf/components/esp_common /home/<USER>/esp32/esp-idf/components/esp_driver_ana_cmpr /home/<USER>/esp32/esp-idf/components/esp_driver_cam /home/<USER>/esp32/esp-idf/components/esp_driver_dac /home/<USER>/esp32/esp-idf/components/esp_driver_gpio /home/<USER>/esp32/esp-idf/components/esp_driver_gptimer /home/<USER>/esp32/esp-idf/components/esp_driver_i2c /home/<USER>/esp32/esp-idf/components/esp_driver_i2s /home/<USER>/esp32/esp-idf/components/esp_driver_isp /home/<USER>/esp32/esp-idf/components/esp_driver_jpeg /home/<USER>/esp32/esp-idf/components/esp_driver_ledc /home/<USER>/esp32/esp-idf/components/esp_driver_mcpwm /home/<USER>/esp32/esp-idf/components/esp_driver_parlio /home/<USER>/esp32/esp-idf/components/esp_driver_pcnt /home/<USER>/esp32/esp-idf/components/esp_driver_ppa /home/<USER>/esp32/esp-idf/components/esp_driver_rmt /home/<USER>/esp32/esp-idf/components/esp_driver_sdio /home/<USER>/esp32/esp-idf/components/esp_driver_sdm /home/<USER>/esp32/esp-idf/components/esp_driver_sdmmc /home/<USER>/esp32/esp-idf/components/esp_driver_sdspi /home/<USER>/esp32/esp-idf/components/esp_driver_spi /home/<USER>/esp32/esp-idf/components/esp_driver_touch_sens /home/<USER>/esp32/esp-idf/components/esp_driver_tsens /home/<USER>/esp32/esp-idf/components/esp_driver_uart /home/<USER>/esp32/esp-idf/components/esp_driver_usb_serial_jtag /home/<USER>/esp32/esp-idf/components/esp_eth /home/<USER>/esp32/esp-idf/components/esp_event /home/<USER>/esp32/esp-idf/components/esp_gdbstub /home/<USER>/esp32/esp-idf/components/esp_hid /home/<USER>/esp32/esp-idf/components/esp_http_client /home/<USER>/esp32/esp-idf/components/esp_http_server /home/<USER>/esp32/esp-idf/components/esp_https_ota /home/<USER>/esp32/esp-idf/components/esp_https_server /home/<USER>/esp32/esp-idf/components/esp_hw_support /home/<USER>/esp32/esp-idf/components/esp_lcd /home/<USER>/esp32/esp-idf/components/esp_local_ctrl /home/<USER>/esp32/esp-idf/components/esp_mm /home/<USER>/esp32/esp-idf/components/esp_netif /home/<USER>/esp32/esp-idf/components/esp_netif_stack /home/<USER>/esp32/esp-idf/components/esp_partition /home/<USER>/esp32/esp-idf/components/esp_phy /home/<USER>/esp32/esp-idf/components/esp_pm /home/<USER>/esp32/esp-idf/components/esp_psram /home/<USER>/esp32/esp-idf/components/esp_ringbuf /home/<USER>/esp32/esp-idf/components/esp_rom /home/<USER>/esp32/esp-idf/components/esp_security /home/<USER>/esp32/esp-idf/components/esp_system /home/<USER>/esp32/esp-idf/components/esp_timer /home/<USER>/esp32/esp-idf/components/esp_vfs_console /home/<USER>/esp32/esp-idf/components/esp_wifi /home/<USER>/esp32/esp-idf/components/espcoredump /home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif2022__image_player /home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__adc_battery_estimation /home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__adc_mic /home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__button /home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__cmake_utilities /home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__dl_fft /home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp /home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-sr /home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp32-camera /home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_codec_dev /home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_io_expander /home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_io_expander_tca9554 /home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_io_expander_tca95xx_16bit /home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_jpeg /home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_lcd_axs15231b /home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_lcd_gc9a01 /home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_lcd_ili9341 /home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_lcd_panel_io_additions /home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_lcd_spd2010 /home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_lcd_st77916 /home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_lcd_st7796 /home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_lcd_touch /home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_lcd_touch_cst816s /home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_lcd_touch_ft5x06 /home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_lcd_touch_gt911 /home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_lvgl_port /home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_mmap_assets /home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__knob /home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__led_strip /home/<USER>/esp32/esp-idf/components/esptool_py /home/<USER>/esp32/esp-idf/components/fatfs /home/<USER>/esp32/esp-idf/components/freertos /home/<USER>/esp32/esp-idf/components/hal /home/<USER>/esp32/esp-idf/components/heap /home/<USER>/esp32/esp-idf/components/http_parser /home/<USER>/esp32/esp-idf/components/idf_test /home/<USER>/esp32/esp-idf/components/ieee802154 /home/<USER>/esp32/esp-idf/components/json /home/<USER>/esp32/esp-idf/components/log /home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl /home/<USER>/esp32/esp-idf/components/lwip /home/<USER>/bysx/xiaozhi-esp32/main /home/<USER>/esp32/esp-idf/components/mbedtls /home/<USER>/esp32/esp-idf/components/mqtt /home/<USER>/esp32/esp-idf/components/newlib /home/<USER>/esp32/esp-idf/components/nvs_flash /home/<USER>/esp32/esp-idf/components/nvs_sec_provider /home/<USER>/esp32/esp-idf/components/openthread /home/<USER>/esp32/esp-idf/components/partition_table /home/<USER>/esp32/esp-idf/components/perfmon /home/<USER>/esp32/esp-idf/components/protobuf-c /home/<USER>/esp32/esp-idf/components/protocomm /home/<USER>/esp32/esp-idf/components/pthread /home/<USER>/esp32/esp-idf/components/rt /home/<USER>/esp32/esp-idf/components/sdmmc /home/<USER>/esp32/esp-idf/components/soc /home/<USER>/esp32/esp-idf/components/spi_flash /home/<USER>/esp32/esp-idf/components/spiffs /home/<USER>/esp32/esp-idf/components/tcp_transport /home/<USER>/bysx/xiaozhi-esp32/managed_components/tny-robotics__sh1106-esp-idf /home/<USER>/esp32/esp-idf/components/touch_element /home/<USER>/bysx/xiaozhi-esp32/managed_components/txp666__otto-emoji-gif-component /home/<USER>/esp32/esp-idf/components/ulp /home/<USER>/esp32/esp-idf/components/unity /home/<USER>/esp32/esp-idf/components/usb /home/<USER>/esp32/esp-idf/components/vfs /home/<USER>/bysx/xiaozhi-esp32/managed_components/waveshare__esp_lcd_sh8601 /home/<USER>/bysx/xiaozhi-esp32/managed_components/waveshare__esp_lcd_touch_cst9217 /home/<USER>/esp32/esp-idf/components/wear_levelling /home/<USER>/esp32/esp-idf/components/wifi_provisioning /home/<USER>/esp32/esp-idf/components/wpa_supplicant /home/<USER>/bysx/xiaozhi-esp32/managed_components/wvirgil123__esp_jpeg_simd /home/<USER>/bysx/xiaozhi-esp32/managed_components/wvirgil123__sscma_client /home/<USER>/esp32/esp-idf/components/xtensa
-- Configuring done
-- Generating done
-- Build files have been written to: /home/<USER>/bysx/xiaozhi-esp32/build
