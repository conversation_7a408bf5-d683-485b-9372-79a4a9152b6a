[1/1] cd /home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/partition_table && /usr/bin/cmake -E echo "Partition table binary generated. Contents:" && /usr/bin/cmake -E echo "*******************************************************************************" && /home/<USER>/.espressif/python_env/idf5.4_py3.10_env/bin/python /home/<USER>/esp32/esp-idf/components/partition_table/gen_esp32part.py -q --offset 0x8000 --flash-size 16MB -- /home/<USER>/bysx/xiaozhi-esp32/build/partition_table/partition-table.bin && /usr/bin/cmake -E echo "*******************************************************************************"
Partition table binary generated. Contents:
*******************************************************************************
# ESP-IDF Partition Table
# Name, Type, SubType, Offset, Size, Flags
nvs,data,nvs,0x9000,16K,
otadata,data,ota,0xd000,8K,
phy_init,data,phy,0xf000,4K,
model,data,spiffs,0x10000,960K,
ota_0,app,ota_0,0x100000,6M,
ota_1,app,ota_1,0x700000,6M,
*******************************************************************************
