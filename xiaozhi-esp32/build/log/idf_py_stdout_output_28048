[1/1] cd /home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/efuse && /home/<USER>/.espressif/python_env/idf5.4_py3.10_env/bin/python /home/<USER>/esp32/esp-idf/components/efuse/efuse_table_gen.py /home/<USER>/esp32/esp-idf/components/efuse/esp32s3/esp_efuse_table.csv -t esp32s3 --max_blk_len 256 --info
Parsing efuse CSV input file /home/<USER>/esp32/esp-idf/components/efuse/esp32s3/esp_efuse_table.csv ...
Verifying efuse table...
Max number of bits in BLK 256
Sorted efuse table:
# 	field_name                     	efuse_block 	bit_start 	bit_count
1 	WR_DIS                         	EFUSE_BLK0 	   0     	   32   
2 	WR_DIS.RD_DIS                  	EFUSE_BLK0 	   0     	   1    
3 	WR_DIS.DIS_ICACHE              	EFUSE_BLK0 	   2     	   1    
4 	WR_DIS.DIS_DCACHE              	EFUSE_BLK0 	   2     	   1    
5 	WR_DIS.DIS_DOWNLOAD_ICACHE     	EFUSE_BLK0 	   2     	   1    
6 	WR_DIS.DIS_DOWNLOAD_DCACHE     	EFUSE_BLK0 	   2     	   1    
7 	WR_DIS.DIS_FORCE_DOWNLOAD      	EFUSE_BLK0 	   2     	   1    
8 	WR_DIS.DIS_USB_OTG             	EFUSE_BLK0 	   2     	   1    
9 	WR_DIS.DIS_TWAI                	EFUSE_BLK0 	   2     	   1    
10 	WR_DIS.DIS_APP_CPU             	EFUSE_BLK0 	   2     	   1    
11 	WR_DIS.DIS_PAD_JTAG            	EFUSE_BLK0 	   2     	   1    
12 	WR_DIS.DIS_DOWNLOAD_MANUAL_ENCRYPT 	EFUSE_BLK0 	   2     	   1    
13 	WR_DIS.DIS_USB_JTAG            	EFUSE_BLK0 	   2     	   1    
14 	WR_DIS.DIS_USB_SERIAL_JTAG     	EFUSE_BLK0 	   2     	   1    
15 	WR_DIS.STRAP_JTAG_SEL          	EFUSE_BLK0 	   2     	   1    
16 	WR_DIS.USB_PHY_SEL             	EFUSE_BLK0 	   2     	   1    
17 	WR_DIS.VDD_SPI_XPD             	EFUSE_BLK0 	   3     	   1    
18 	WR_DIS.VDD_SPI_TIEH            	EFUSE_BLK0 	   3     	   1    
19 	WR_DIS.VDD_SPI_FORCE           	EFUSE_BLK0 	   3     	   1    
20 	WR_DIS.WDT_DELAY_SEL           	EFUSE_BLK0 	   3     	   1    
21 	WR_DIS.SPI_BOOT_CRYPT_CNT      	EFUSE_BLK0 	   4     	   1    
22 	WR_DIS.SECURE_BOOT_KEY_REVOKE0 	EFUSE_BLK0 	   5     	   1    
23 	WR_DIS.SECURE_BOOT_KEY_REVOKE1 	EFUSE_BLK0 	   6     	   1    
24 	WR_DIS.SECURE_BOOT_KEY_REVOKE2 	EFUSE_BLK0 	   7     	   1    
25 	WR_DIS.KEY_PURPOSE_0           	EFUSE_BLK0 	   8     	   1    
26 	WR_DIS.KEY_PURPOSE_1           	EFUSE_BLK0 	   9     	   1    
27 	WR_DIS.KEY_PURPOSE_2           	EFUSE_BLK0 	   10    	   1    
28 	WR_DIS.KEY_PURPOSE_3           	EFUSE_BLK0 	   11    	   1    
29 	WR_DIS.KEY_PURPOSE_4           	EFUSE_BLK0 	   12    	   1    
30 	WR_DIS.KEY_PURPOSE_5           	EFUSE_BLK0 	   13    	   1    
31 	WR_DIS.SECURE_BOOT_EN          	EFUSE_BLK0 	   15    	   1    
32 	WR_DIS.SECURE_BOOT_AGGRESSIVE_REVOKE 	EFUSE_BLK0 	   16    	   1    
33 	WR_DIS.FLASH_TPUW              	EFUSE_BLK0 	   18    	   1    
34 	WR_DIS.DIS_DOWNLOAD_MODE       	EFUSE_BLK0 	   18    	   1    
35 	WR_DIS.DIS_DIRECT_BOOT         	EFUSE_BLK0 	   18    	   1    
36 	WR_DIS.DIS_USB_SERIAL_JTAG_ROM_PRINT 	EFUSE_BLK0 	   18    	   1    
37 	WR_DIS.FLASH_ECC_MODE          	EFUSE_BLK0 	   18    	   1    
38 	WR_DIS.DIS_USB_SERIAL_JTAG_DOWNLOAD_MODE 	EFUSE_BLK0 	   18    	   1    
39 	WR_DIS.ENABLE_SECURITY_DOWNLOAD 	EFUSE_BLK0 	   18    	   1    
40 	WR_DIS.UART_PRINT_CONTROL      	EFUSE_BLK0 	   18    	   1    
41 	WR_DIS.PIN_POWER_SELECTION     	EFUSE_BLK0 	   18    	   1    
42 	WR_DIS.FLASH_TYPE              	EFUSE_BLK0 	   18    	   1    
43 	WR_DIS.FLASH_PAGE_SIZE         	EFUSE_BLK0 	   18    	   1    
44 	WR_DIS.FLASH_ECC_EN            	EFUSE_BLK0 	   18    	   1    
45 	WR_DIS.FORCE_SEND_RESUME       	EFUSE_BLK0 	   18    	   1    
46 	WR_DIS.SECURE_VERSION          	EFUSE_BLK0 	   18    	   1    
47 	WR_DIS.DIS_USB_OTG_DOWNLOAD_MODE 	EFUSE_BLK0 	   19    	   1    
48 	WR_DIS.DISABLE_WAFER_VERSION_MAJOR 	EFUSE_BLK0 	   19    	   1    
49 	WR_DIS.DISABLE_BLK_VERSION_MAJOR 	EFUSE_BLK0 	   19    	   1    
50 	WR_DIS.BLK1                    	EFUSE_BLK0 	   20    	   1    
51 	WR_DIS.MAC                     	EFUSE_BLK0 	   20    	   1    
52 	WR_DIS.SPI_PAD_CONFIG_CLK      	EFUSE_BLK0 	   20    	   1    
53 	WR_DIS.SPI_PAD_CONFIG_Q        	EFUSE_BLK0 	   20    	   1    
54 	WR_DIS.SPI_PAD_CONFIG_D        	EFUSE_BLK0 	   20    	   1    
55 	WR_DIS.SPI_PAD_CONFIG_CS       	EFUSE_BLK0 	   20    	   1    
56 	WR_DIS.SPI_PAD_CONFIG_HD       	EFUSE_BLK0 	   20    	   1    
57 	WR_DIS.SPI_PAD_CONFIG_WP       	EFUSE_BLK0 	   20    	   1    
58 	WR_DIS.SPI_PAD_CONFIG_DQS      	EFUSE_BLK0 	   20    	   1    
59 	WR_DIS.SPI_PAD_CONFIG_D4       	EFUSE_BLK0 	   20    	   1    
60 	WR_DIS.SPI_PAD_CONFIG_D5       	EFUSE_BLK0 	   20    	   1    
61 	WR_DIS.SPI_PAD_CONFIG_D6       	EFUSE_BLK0 	   20    	   1    
62 	WR_DIS.SPI_PAD_CONFIG_D7       	EFUSE_BLK0 	   20    	   1    
63 	WR_DIS.WAFER_VERSION_MINOR_LO  	EFUSE_BLK0 	   20    	   1    
64 	WR_DIS.PKG_VERSION             	EFUSE_BLK0 	   20    	   1    
65 	WR_DIS.BLK_VERSION_MINOR       	EFUSE_BLK0 	   20    	   1    
66 	WR_DIS.FLASH_CAP               	EFUSE_BLK0 	   20    	   1    
67 	WR_DIS.FLASH_TEMP              	EFUSE_BLK0 	   20    	   1    
68 	WR_DIS.FLASH_VENDOR            	EFUSE_BLK0 	   20    	   1    
69 	WR_DIS.PSRAM_CAP               	EFUSE_BLK0 	   20    	   1    
70 	WR_DIS.PSRAM_TEMP              	EFUSE_BLK0 	   20    	   1    
71 	WR_DIS.PSRAM_VENDOR            	EFUSE_BLK0 	   20    	   1    
72 	WR_DIS.K_RTC_LDO               	EFUSE_BLK0 	   20    	   1    
73 	WR_DIS.K_DIG_LDO               	EFUSE_BLK0 	   20    	   1    
74 	WR_DIS.V_RTC_DBIAS20           	EFUSE_BLK0 	   20    	   1    
75 	WR_DIS.V_DIG_DBIAS20           	EFUSE_BLK0 	   20    	   1    
76 	WR_DIS.DIG_DBIAS_HVT           	EFUSE_BLK0 	   20    	   1    
77 	WR_DIS.WAFER_VERSION_MINOR_HI  	EFUSE_BLK0 	   20    	   1    
78 	WR_DIS.WAFER_VERSION_MAJOR     	EFUSE_BLK0 	   20    	   1    
79 	WR_DIS.ADC2_CAL_VOL_ATTEN3     	EFUSE_BLK0 	   20    	   1    
80 	WR_DIS.SYS_DATA_PART1          	EFUSE_BLK0 	   21    	   1    
81 	WR_DIS.OPTIONAL_UNIQUE_ID      	EFUSE_BLK0 	   21    	   1    
82 	WR_DIS.BLK_VERSION_MAJOR       	EFUSE_BLK0 	   21    	   1    
83 	WR_DIS.TEMP_CALIB              	EFUSE_BLK0 	   21    	   1    
84 	WR_DIS.OCODE                   	EFUSE_BLK0 	   21    	   1    
85 	WR_DIS.ADC1_INIT_CODE_ATTEN0   	EFUSE_BLK0 	   21    	   1    
86 	WR_DIS.ADC1_INIT_CODE_ATTEN1   	EFUSE_BLK0 	   21    	   1    
87 	WR_DIS.ADC1_INIT_CODE_ATTEN2   	EFUSE_BLK0 	   21    	   1    
88 	WR_DIS.ADC1_INIT_CODE_ATTEN3   	EFUSE_BLK0 	   21    	   1    
89 	WR_DIS.ADC2_INIT_CODE_ATTEN0   	EFUSE_BLK0 	   21    	   1    
90 	WR_DIS.ADC2_INIT_CODE_ATTEN1   	EFUSE_BLK0 	   21    	   1    
91 	WR_DIS.ADC2_INIT_CODE_ATTEN2   	EFUSE_BLK0 	   21    	   1    
92 	WR_DIS.ADC2_INIT_CODE_ATTEN3   	EFUSE_BLK0 	   21    	   1    
93 	WR_DIS.ADC1_CAL_VOL_ATTEN0     	EFUSE_BLK0 	   21    	   1    
94 	WR_DIS.ADC1_CAL_VOL_ATTEN1     	EFUSE_BLK0 	   21    	   1    
95 	WR_DIS.ADC1_CAL_VOL_ATTEN2     	EFUSE_BLK0 	   21    	   1    
96 	WR_DIS.ADC1_CAL_VOL_ATTEN3     	EFUSE_BLK0 	   21    	   1    
97 	WR_DIS.ADC2_CAL_VOL_ATTEN0     	EFUSE_BLK0 	   21    	   1    
98 	WR_DIS.ADC2_CAL_VOL_ATTEN1     	EFUSE_BLK0 	   21    	   1    
99 	WR_DIS.ADC2_CAL_VOL_ATTEN2     	EFUSE_BLK0 	   21    	   1    
100 	WR_DIS.BLOCK_USR_DATA          	EFUSE_BLK0 	   22    	   1    
101 	WR_DIS.CUSTOM_MAC              	EFUSE_BLK0 	   22    	   1    
102 	WR_DIS.BLOCK_KEY0              	EFUSE_BLK0 	   23    	   1    
103 	WR_DIS.BLOCK_KEY1              	EFUSE_BLK0 	   24    	   1    
104 	WR_DIS.BLOCK_KEY2              	EFUSE_BLK0 	   25    	   1    
105 	WR_DIS.BLOCK_KEY3              	EFUSE_BLK0 	   26    	   1    
106 	WR_DIS.BLOCK_KEY4              	EFUSE_BLK0 	   27    	   1    
107 	WR_DIS.BLOCK_KEY5              	EFUSE_BLK0 	   28    	   1    
108 	WR_DIS.BLOCK_SYS_DATA2         	EFUSE_BLK0 	   29    	   1    
109 	WR_DIS.USB_EXCHG_PINS          	EFUSE_BLK0 	   30    	   1    
110 	WR_DIS.USB_EXT_PHY_ENABLE      	EFUSE_BLK0 	   30    	   1    
111 	WR_DIS.SOFT_DIS_JTAG           	EFUSE_BLK0 	   31    	   1    
112 	RD_DIS                         	EFUSE_BLK0 	   32    	   7    
113 	RD_DIS.BLOCK_KEY0              	EFUSE_BLK0 	   32    	   1    
114 	RD_DIS.BLOCK_KEY1              	EFUSE_BLK0 	   33    	   1    
115 	RD_DIS.BLOCK_KEY2              	EFUSE_BLK0 	   34    	   1    
116 	RD_DIS.BLOCK_KEY3              	EFUSE_BLK0 	   35    	   1    
117 	RD_DIS.BLOCK_KEY4              	EFUSE_BLK0 	   36    	   1    
118 	RD_DIS.BLOCK_KEY5              	EFUSE_BLK0 	   37    	   1    
119 	RD_DIS.BLOCK_SYS_DATA2         	EFUSE_BLK0 	   38    	   1    
120 	DIS_ICACHE                     	EFUSE_BLK0 	   40    	   1    
121 	DIS_DCACHE                     	EFUSE_BLK0 	   41    	   1    
122 	DIS_DOWNLOAD_ICACHE            	EFUSE_BLK0 	   42    	   1    
123 	DIS_DOWNLOAD_DCACHE            	EFUSE_BLK0 	   43    	   1    
124 	DIS_FORCE_DOWNLOAD             	EFUSE_BLK0 	   44    	   1    
125 	DIS_USB_OTG                    	EFUSE_BLK0 	   45    	   1    
126 	DIS_TWAI                       	EFUSE_BLK0 	   46    	   1    
127 	DIS_APP_CPU                    	EFUSE_BLK0 	   47    	   1    
128 	SOFT_DIS_JTAG                  	EFUSE_BLK0 	   48    	   3    
129 	DIS_PAD_JTAG                   	EFUSE_BLK0 	   51    	   1    
130 	DIS_DOWNLOAD_MANUAL_ENCRYPT    	EFUSE_BLK0 	   52    	   1    
131 	USB_EXCHG_PINS                 	EFUSE_BLK0 	   57    	   1    
132 	USB_EXT_PHY_ENABLE             	EFUSE_BLK0 	   58    	   1    
133 	VDD_SPI_XPD                    	EFUSE_BLK0 	   68    	   1    
134 	VDD_SPI_TIEH                   	EFUSE_BLK0 	   69    	   1    
135 	VDD_SPI_FORCE                  	EFUSE_BLK0 	   70    	   1    
136 	WDT_DELAY_SEL                  	EFUSE_BLK0 	   80    	   2    
137 	SPI_BOOT_CRYPT_CNT             	EFUSE_BLK0 	   82    	   3    
138 	SECURE_BOOT_KEY_REVOKE0        	EFUSE_BLK0 	   85    	   1    
139 	SECURE_BOOT_KEY_REVOKE1        	EFUSE_BLK0 	   86    	   1    
140 	SECURE_BOOT_KEY_REVOKE2        	EFUSE_BLK0 	   87    	   1    
141 	KEY_PURPOSE_0                  	EFUSE_BLK0 	   88    	   4    
142 	KEY_PURPOSE_1                  	EFUSE_BLK0 	   92    	   4    
143 	KEY_PURPOSE_2                  	EFUSE_BLK0 	   96    	   4    
144 	KEY_PURPOSE_3                  	EFUSE_BLK0 	  100    	   4    
145 	KEY_PURPOSE_4                  	EFUSE_BLK0 	  104    	   4    
146 	KEY_PURPOSE_5                  	EFUSE_BLK0 	  108    	   4    
147 	SECURE_BOOT_EN                 	EFUSE_BLK0 	  116    	   1    
148 	SECURE_BOOT_AGGRESSIVE_REVOKE  	EFUSE_BLK0 	  117    	   1    
149 	DIS_USB_JTAG                   	EFUSE_BLK0 	  118    	   1    
150 	DIS_USB_SERIAL_JTAG            	EFUSE_BLK0 	  119    	   1    
151 	STRAP_JTAG_SEL                 	EFUSE_BLK0 	  120    	   1    
152 	USB_PHY_SEL                    	EFUSE_BLK0 	  121    	   1    
153 	FLASH_TPUW                     	EFUSE_BLK0 	  124    	   4    
154 	DIS_DOWNLOAD_MODE              	EFUSE_BLK0 	  128    	   1    
155 	DIS_DIRECT_BOOT                	EFUSE_BLK0 	  129    	   1    
156 	DIS_USB_SERIAL_JTAG_ROM_PRINT  	EFUSE_BLK0 	  130    	   1    
157 	FLASH_ECC_MODE                 	EFUSE_BLK0 	  131    	   1    
158 	DIS_USB_SERIAL_JTAG_DOWNLOAD_MODE 	EFUSE_BLK0 	  132    	   1    
159 	ENABLE_SECURITY_DOWNLOAD       	EFUSE_BLK0 	  133    	   1    
160 	UART_PRINT_CONTROL             	EFUSE_BLK0 	  134    	   2    
161 	PIN_POWER_SELECTION            	EFUSE_BLK0 	  136    	   1    
162 	FLASH_TYPE                     	EFUSE_BLK0 	  137    	   1    
163 	FLASH_PAGE_SIZE                	EFUSE_BLK0 	  138    	   2    
164 	FLASH_ECC_EN                   	EFUSE_BLK0 	  140    	   1    
165 	FORCE_SEND_RESUME              	EFUSE_BLK0 	  141    	   1    
166 	SECURE_VERSION                 	EFUSE_BLK0 	  142    	   16   
167 	DIS_USB_OTG_DOWNLOAD_MODE      	EFUSE_BLK0 	  159    	   1    
168 	DISABLE_WAFER_VERSION_MAJOR    	EFUSE_BLK0 	  160    	   1    
169 	DISABLE_BLK_VERSION_MAJOR      	EFUSE_BLK0 	  161    	   1    
170 	MAC                            	EFUSE_BLK1 	   0     	   8    
171 	MAC                            	EFUSE_BLK1 	   8     	   8    
172 	MAC                            	EFUSE_BLK1 	   16    	   8    
173 	MAC                            	EFUSE_BLK1 	   24    	   8    
174 	MAC                            	EFUSE_BLK1 	   32    	   8    
175 	MAC                            	EFUSE_BLK1 	   40    	   8    
176 	SPI_PAD_CONFIG_CLK             	EFUSE_BLK1 	   48    	   6    
177 	SPI_PAD_CONFIG_Q               	EFUSE_BLK1 	   54    	   6    
178 	SPI_PAD_CONFIG_D               	EFUSE_BLK1 	   60    	   6    
179 	SPI_PAD_CONFIG_CS              	EFUSE_BLK1 	   66    	   6    
180 	SPI_PAD_CONFIG_HD              	EFUSE_BLK1 	   72    	   6    
181 	SPI_PAD_CONFIG_WP              	EFUSE_BLK1 	   78    	   6    
182 	SPI_PAD_CONFIG_DQS             	EFUSE_BLK1 	   84    	   6    
183 	SPI_PAD_CONFIG_D4              	EFUSE_BLK1 	   90    	   6    
184 	SPI_PAD_CONFIG_D5              	EFUSE_BLK1 	   96    	   6    
185 	SPI_PAD_CONFIG_D6              	EFUSE_BLK1 	  102    	   6    
186 	SPI_PAD_CONFIG_D7              	EFUSE_BLK1 	  108    	   6    
187 	WAFER_VERSION_MINOR_LO         	EFUSE_BLK1 	  114    	   3    
188 	PKG_VERSION                    	EFUSE_BLK1 	  117    	   3    
189 	BLK_VERSION_MINOR              	EFUSE_BLK1 	  120    	   3    
190 	FLASH_CAP                      	EFUSE_BLK1 	  123    	   3    
191 	FLASH_TEMP                     	EFUSE_BLK1 	  126    	   2    
192 	FLASH_VENDOR                   	EFUSE_BLK1 	  128    	   3    
193 	PSRAM_CAP                      	EFUSE_BLK1 	  131    	   2    
194 	PSRAM_TEMP                     	EFUSE_BLK1 	  133    	   2    
195 	PSRAM_VENDOR                   	EFUSE_BLK1 	  135    	   2    
196 	K_RTC_LDO                      	EFUSE_BLK1 	  141    	   7    
197 	K_DIG_LDO                      	EFUSE_BLK1 	  148    	   7    
198 	V_RTC_DBIAS20                  	EFUSE_BLK1 	  155    	   8    
199 	V_DIG_DBIAS20                  	EFUSE_BLK1 	  163    	   8    
200 	DIG_DBIAS_HVT                  	EFUSE_BLK1 	  171    	   5    
201 	PSRAM_CAP                      	EFUSE_BLK1 	  179    	   1    
202 	WAFER_VERSION_MINOR_HI         	EFUSE_BLK1 	  183    	   1    
203 	WAFER_VERSION_MAJOR            	EFUSE_BLK1 	  184    	   2    
204 	ADC2_CAL_VOL_ATTEN3            	EFUSE_BLK1 	  186    	   6    
205 	SYS_DATA_PART2                 	EFUSE_BLK10 	   0     	  256   
206 	OPTIONAL_UNIQUE_ID             	EFUSE_BLK2 	   0     	  128   
207 	BLK_VERSION_MAJOR              	EFUSE_BLK2 	  128    	   2    
208 	TEMP_CALIB                     	EFUSE_BLK2 	  132    	   9    
209 	OCODE                          	EFUSE_BLK2 	  141    	   8    
210 	ADC1_INIT_CODE_ATTEN0          	EFUSE_BLK2 	  149    	   8    
211 	ADC1_INIT_CODE_ATTEN1          	EFUSE_BLK2 	  157    	   6    
212 	ADC1_INIT_CODE_ATTEN2          	EFUSE_BLK2 	  163    	   6    
213 	ADC1_INIT_CODE_ATTEN3          	EFUSE_BLK2 	  169    	   6    
214 	ADC2_INIT_CODE_ATTEN0          	EFUSE_BLK2 	  175    	   8    
215 	ADC2_INIT_CODE_ATTEN1          	EFUSE_BLK2 	  183    	   6    
216 	ADC2_INIT_CODE_ATTEN2          	EFUSE_BLK2 	  189    	   6    
217 	ADC2_INIT_CODE_ATTEN3          	EFUSE_BLK2 	  195    	   6    
218 	ADC1_CAL_VOL_ATTEN0            	EFUSE_BLK2 	  201    	   8    
219 	ADC1_CAL_VOL_ATTEN1            	EFUSE_BLK2 	  209    	   8    
220 	ADC1_CAL_VOL_ATTEN2            	EFUSE_BLK2 	  217    	   8    
221 	ADC1_CAL_VOL_ATTEN3            	EFUSE_BLK2 	  225    	   8    
222 	ADC2_CAL_VOL_ATTEN0            	EFUSE_BLK2 	  233    	   8    
223 	ADC2_CAL_VOL_ATTEN1            	EFUSE_BLK2 	  241    	   7    
224 	ADC2_CAL_VOL_ATTEN2            	EFUSE_BLK2 	  248    	   7    
225 	USER_DATA                      	EFUSE_BLK3 	   0     	  256   
226 	USER_DATA.MAC_CUSTOM           	EFUSE_BLK3 	  200    	   48   
227 	KEY0                           	EFUSE_BLK4 	   0     	  256   
228 	KEY1                           	EFUSE_BLK5 	   0     	  256   
229 	KEY2                           	EFUSE_BLK6 	   0     	  256   
230 	KEY3                           	EFUSE_BLK7 	   0     	  256   
231 	KEY4                           	EFUSE_BLK8 	   0     	  256   
232 	KEY5                           	EFUSE_BLK9 	   0     	  256   

Used bits in efuse table:
EFUSE_BLK0 
[0 31] [0 0] [2 2] [2 2] [2 2] [2 2] [2 2] [2 2] [2 2] [2 2] [2 2] [2 2] [2 2] [2 2] [2 2] [2 3] [3 3] [3 3] [3 13] [15 16] [18 18] [18 18] [18 18] [18 18] [18 18] [18 18] [18 18] [18 18] [18 18] [18 18] [18 18] [18 18] [18 18] [18 19] [19 19] [19 20] [20 20] [20 20] [20 20] [20 20] [20 20] [20 20] [20 20] [20 20] [20 20] [20 20] [20 20] [20 20] [20 20] [20 20] [20 20] [20 20] [20 20] [20 20] [20 20] [20 20] [20 20] [20 20] [20 20] [20 20] [20 20] [20 20] [20 20] [20 20] [20 21] [21 21] [21 21] [21 21] [21 21] [21 21] [21 21] [21 21] [21 21] [21 21] [21 21] [21 21] [21 21] [21 21] [21 21] [21 21] [21 21] [21 21] [21 21] [21 22] [22 30] [30 38] [32 38] [40 52] [57 58] [68 70] [80 111] [116 121] [124 157] [159 161] 

EFUSE_BLK1 
[0 136] [141 175] [179 179] [183 191] 

EFUSE_BLK10 
[0 255] 

EFUSE_BLK2 
[0 129] [132 254] 

EFUSE_BLK3 
[0 255] [200 247] 

EFUSE_BLK4 
[0 255] 

EFUSE_BLK5 
[0 255] 

EFUSE_BLK6 
[0 255] 

EFUSE_BLK7 
[0 255] 

EFUSE_BLK8 
[0 255] 

EFUSE_BLK9 
[0 255] 

Note: Not printed ranges are free for using. (bits in EFUSE_BLK0 are reserved for Espressif)

