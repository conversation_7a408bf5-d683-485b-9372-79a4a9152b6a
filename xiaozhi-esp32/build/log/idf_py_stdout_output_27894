[1/4] cd /home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/esptool_py && /home/<USER>/.espressif/python_env/idf5.4_py3.10_env/bin/python /home/<USER>/esp32/esp-idf/components/partition_table/check_sizes.py --offset 0x8000 partition --type app /home/<USER>/bysx/xiaozhi-esp32/build/partition_table/partition-table.bin /home/<USER>/bysx/xiaozhi-esp32/build/xiaozhi.bin
xiaozhi.bin binary size 0x26a490 bytes. Smallest app partition is 0x600000 bytes. 0x395b70 bytes (60%) free.
[2/4] Performing build step for 'bootloader'
[1/1] cd /home/<USER>/bysx/xiaozhi-esp32/build/bootloader/esp-idf/esptool_py && /home/<USER>/.espressif/python_env/idf5.4_py3.10_env/bin/python /home/<USER>/esp32/esp-idf/components/partition_table/check_sizes.py --offset 0x8000 bootloader 0x0 /home/<USER>/bysx/xiaozhi-esp32/build/bootloader/bootloader.bin
Bootloader binary size 0x4050 bytes. 0x3fb0 bytes (50%) free.
