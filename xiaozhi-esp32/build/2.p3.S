/* * Data converted from /home/<USER>/bysx/xiaozhi-esp32/main/assets/zh-CN/2.p3
 */
.data
#if !defined (__APPLE__) && !defined (__linux__)
.section .rodata.embedded
#endif

.global _2_p3
_2_p3:

.global _binary_2_p3_start
_binary_2_p3_start: /* for objcopy compatibility */
.byte 0x00, 0x00, 0x00, 0x4f, 0x58, 0x00, 0x33, 0x33, 0x25, 0xef, 0xce, 0xf1, 0xe7, 0xbe, 0xf6, 0x67
.byte 0x75, 0x9b, 0x89, 0x48, 0x57, 0x6e, 0x2c, 0xc0, 0x28, 0xe0, 0x6f, 0x9f, 0x76, 0x13, 0x4d, 0x0d
.byte 0x12, 0x44, 0xa9, 0x4a, 0x56, 0x76, 0xcd, 0xc3, 0x88, 0xd5, 0x48, 0x6d, 0xcb, 0xcb, 0x22, 0x81
.byte 0x0e, 0xbb, 0x31, 0xe9, 0x35, 0xd5, 0x02, 0x14, 0x5d, 0xba, 0xc2, 0x19, 0x8a, 0xac, 0xec, 0xc3
.byte 0x17, 0x0e, 0xae, 0x33, 0xc5, 0xd3, 0x15, 0x84, 0x31, 0x54, 0xf7, 0x77, 0x64, 0xea, 0xe3, 0xbb
.byte 0x8e, 0x87, 0xfa, 0x00, 0x00, 0x00, 0x4e, 0x58, 0x08, 0x6f, 0x14, 0xce, 0x6d, 0xb0, 0x78, 0xa7
.byte 0x09, 0xe9, 0xde, 0xc3, 0xba, 0xce, 0x8e, 0xc5, 0x08, 0x9b, 0x6d, 0x78, 0x43, 0x76, 0x89, 0xce
.byte 0x26, 0x45, 0x76, 0x66, 0xa9, 0x6d, 0x13, 0x3e, 0x31, 0x05, 0x93, 0x61, 0xbc, 0xe8, 0xd9, 0xba
.byte 0x1c, 0xaa, 0x43, 0x8d, 0xb5, 0xe4, 0x89, 0x83, 0xc8, 0xe4, 0x0c, 0xdd, 0x2c, 0x57, 0xee, 0x1f
.byte 0x63, 0x7e, 0xc3, 0x16, 0x1d, 0x64, 0x25, 0x4b, 0x2b, 0x2f, 0x9c, 0x2f, 0xbb, 0x6a, 0x12, 0x2a
.byte 0x34, 0xa2, 0xd6, 0xe7, 0x28, 0x00, 0x00, 0x00, 0x70, 0x58, 0x69, 0x37, 0x37, 0xaf, 0x9b, 0x75
.byte 0x5f, 0x19, 0xe7, 0xee, 0x00, 0xa6, 0x9f, 0xc5, 0xb7, 0x09, 0x88, 0x88, 0xcf, 0x62, 0x13, 0x13
.byte 0x8d, 0x6c, 0x80, 0x68, 0xc7, 0x07, 0x73, 0x75, 0x74, 0xa7, 0xf1, 0xb9, 0x42, 0x9b, 0xf1, 0xd2
.byte 0xc8, 0x5f, 0xdd, 0x72, 0xbb, 0xc8, 0x68, 0x89, 0x51, 0xfb, 0xb1, 0x1c, 0x84, 0xd7, 0xfd, 0xae
.byte 0xcd, 0xdd, 0xd7, 0x13, 0xbc, 0x43, 0xe5, 0x77, 0xb3, 0x2c, 0x64, 0xf0, 0xaf, 0xc9, 0xb7, 0x82
.byte 0x38, 0xd7, 0x0d, 0x10, 0x13, 0x36, 0xd1, 0x60, 0xaa, 0x25, 0x9a, 0x75, 0xfe, 0x93, 0x8f, 0x44
.byte 0x59, 0xff, 0x73, 0x7e, 0x35, 0xa9, 0xae, 0x1d, 0x30, 0x1c, 0xb8, 0xb6, 0xb4, 0x05, 0xf9, 0xf2
.byte 0xcd, 0x72, 0x44, 0x28, 0x5e, 0x0f, 0x72, 0xf5, 0x70, 0x00, 0x00, 0x00, 0x6b, 0x58, 0xed, 0xef
.byte 0x0a, 0x25, 0xc8, 0x6e, 0x5c, 0xcb, 0x37, 0x68, 0x64, 0x77, 0x17, 0x8b, 0x2d, 0x09, 0x59, 0x08
.byte 0x47, 0xd4, 0x82, 0xf2, 0x81, 0x38, 0x81, 0x46, 0xc6, 0xc8, 0xa4, 0x66, 0xf4, 0xe5, 0xac, 0x16
.byte 0xc8, 0xee, 0xff, 0x4f, 0xe4, 0xf2, 0x34, 0x2b, 0xcc, 0x15, 0xb9, 0x16, 0xce, 0x94, 0xc0, 0x95
.byte 0xf0, 0xc0, 0x91, 0x7d, 0xb8, 0xa9, 0x3f, 0xb3, 0x06, 0xca, 0xa0, 0xa9, 0xce, 0xd0, 0xff, 0x0b
.byte 0x8d, 0x2c, 0xac, 0x5c, 0xdb, 0x84, 0xd7, 0x3d, 0xb5, 0xbe, 0x76, 0xea, 0xe6, 0x83, 0x57, 0xef
.byte 0x25, 0xc2, 0xe3, 0x77, 0xfd, 0x2b, 0xfd, 0x01, 0xa9, 0xfa, 0xf2, 0x66, 0xad, 0x25, 0x65, 0x9a
.byte 0x5d, 0x69, 0x31, 0x7f, 0x3a, 0x52, 0xfc, 0xe8, 0x00, 0x00, 0x00, 0x6e, 0x58, 0xed, 0xc2, 0x9d
.byte 0x6d, 0xd1, 0x28, 0xb4, 0x25, 0x04, 0x6e, 0x3d, 0x31, 0xc5, 0x99, 0xcc, 0xb9, 0xc5, 0x39, 0xdd
.byte 0x5c, 0x72, 0x55, 0x36, 0x7d, 0x53, 0xa1, 0x75, 0x1a, 0x1a, 0x2e, 0x69, 0x9e, 0x0c, 0x66, 0x0c
.byte 0x8e, 0x92, 0x7b, 0x9e, 0x42, 0xaf, 0xc6, 0x8b, 0xea, 0x32, 0x82, 0x1e, 0x30, 0xfd, 0x3a, 0x17
.byte 0xf0, 0x64, 0xd2, 0xd1, 0xa2, 0xf8, 0x22, 0x1d, 0x26, 0xae, 0x75, 0x13, 0xda, 0x33, 0xd9, 0x88
.byte 0xad, 0xa1, 0x24, 0xb4, 0xc8, 0x0e, 0x5d, 0x7b, 0x75, 0x72, 0x7d, 0xb6, 0xcb, 0x74, 0xda, 0xe2
.byte 0x18, 0x4a, 0xbd, 0xbd, 0x2c, 0x0d, 0x53, 0x39, 0x7b, 0xb8, 0x91, 0xac, 0xca, 0xec, 0x8f, 0x58
.byte 0xf6, 0xf7, 0x27, 0x67, 0xf2, 0xb3, 0xe6, 0xd1, 0xe7, 0x98, 0x00, 0x00, 0x00, 0x76, 0x58, 0xed
.byte 0xbb, 0xdf, 0x8a, 0x2f, 0xb6, 0x11, 0x49, 0xb3, 0x09, 0x28, 0x1f, 0x1d, 0x7d, 0xc3, 0x6c, 0x63
.byte 0x48, 0xf5, 0x47, 0x23, 0xc9, 0x86, 0xbc, 0x29, 0x78, 0xf7, 0xfd, 0xcf, 0xda, 0xa1, 0xc8, 0x10
.byte 0x66, 0x9e, 0xe6, 0x35, 0xde, 0x89, 0x2b, 0x62, 0x1f, 0x94, 0x87, 0x7e, 0x59, 0x15, 0x1c, 0x8a
.byte 0xa0, 0xcc, 0x86, 0xf4, 0x10, 0x38, 0x82, 0x22, 0x83, 0x53, 0x39, 0x4e, 0x6b, 0x58, 0x70, 0xbb
.byte 0x9f, 0xc5, 0xd6, 0x32, 0x1f, 0x97, 0x8e, 0x98, 0x0d, 0x80, 0x06, 0xa9, 0xeb, 0x8e, 0x14, 0x3c
.byte 0x8b, 0xb5, 0xe0, 0x30, 0xf6, 0xa8, 0x89, 0xfb, 0xc9, 0x1e, 0x35, 0xae, 0xe6, 0x67, 0xe5, 0x60
.byte 0xbc, 0x0e, 0x3b, 0xf9, 0xf1, 0xd7, 0xba, 0xe8, 0xdb, 0x6d, 0x55, 0x24, 0x3e, 0x3c, 0x8f, 0x00
.byte 0x8e, 0x04, 0x87, 0xc0, 0x00, 0x00, 0x00, 0x82, 0x58, 0xcc, 0x25, 0x13, 0x5f, 0x0c, 0xad, 0x8e
.byte 0x36, 0x20, 0x02, 0x59, 0xdd, 0xd1, 0xbf, 0x61, 0x13, 0xcb, 0x32, 0x85, 0xbb, 0xc7, 0x2b, 0x17
.byte 0x92, 0x84, 0x9f, 0x9c, 0x04, 0x8d, 0xaa, 0xb0, 0x37, 0x64, 0x9c, 0x0c, 0x4c, 0x63, 0xa5, 0x48
.byte 0xa9, 0xb0, 0x1b, 0x26, 0x02, 0xb7, 0x0d, 0xab, 0x67, 0xed, 0x65, 0xee, 0xff, 0xf7, 0x21, 0xd5
.byte 0xc4, 0x04, 0x0a, 0x61, 0x25, 0xa9, 0x2b, 0x4d, 0x46, 0x36, 0xc0, 0x8d, 0x8d, 0x6f, 0x72, 0x48
.byte 0x23, 0xdf, 0xd8, 0xb7, 0x3b, 0x8a, 0xe0, 0xc4, 0x94, 0xa8, 0x92, 0x73, 0x91, 0xac, 0xa0, 0xe2
.byte 0xef, 0x3a, 0x83, 0x62, 0x39, 0x74, 0x6a, 0x7c, 0x74, 0xb1, 0x38, 0x56, 0x35, 0x3c, 0x6a, 0x45
.byte 0x69, 0x17, 0xd3, 0x4c, 0xe1, 0x34, 0x50, 0x24, 0xe9, 0x05, 0x95, 0xb9, 0x81, 0x54, 0xc1, 0x5d
.byte 0xa5, 0xc8, 0xd7, 0x03, 0x22, 0x5a, 0x1b, 0x10, 0x24, 0x23, 0x00, 0x00, 0x00, 0x5e, 0x58, 0x0a
.byte 0xb6, 0x3a, 0x13, 0x29, 0xdc, 0xb7, 0x95, 0x7e, 0x50, 0x0f, 0x3f, 0x50, 0x93, 0x25, 0x4d, 0xf1
.byte 0x6c, 0xaa, 0xf5, 0x17, 0x5c, 0x90, 0xbd, 0xd1, 0xbf, 0x12, 0xdf, 0x8d, 0xc7, 0xc2, 0xfe, 0x12
.byte 0x0e, 0x2c, 0xed, 0xcd, 0x46, 0xa1, 0x3a, 0x81, 0x64, 0x0f, 0x71, 0x8e, 0x59, 0x0a, 0x33, 0x40
.byte 0x4d, 0x30, 0xa2, 0xa3, 0x1e, 0xb4, 0x13, 0xa3, 0x02, 0x6f, 0x46, 0x71, 0x36, 0x4a, 0x28, 0x78
.byte 0x51, 0xcd, 0x95, 0xbf, 0x10, 0x97, 0xad, 0x74, 0x67, 0x8e, 0xcf, 0xdb, 0xcf, 0x57, 0x81, 0x47
.byte 0x4f, 0xce, 0x6f, 0x49, 0x50, 0x72, 0x6a, 0x1f, 0x5c, 0x4f, 0x94, 0x4f, 0x00, 0x00, 0x00, 0x5c
.byte 0x58, 0x07, 0xef, 0xd7, 0xce, 0x0e, 0x20, 0x7f, 0x49, 0xed, 0x9b, 0x47, 0x83, 0x42, 0x83, 0x3f
.byte 0xaa, 0x6f, 0x5e, 0x6c, 0x18, 0xed, 0xe6, 0xe0, 0xca, 0x66, 0x61, 0x36, 0x62, 0x7b, 0x7a, 0x8d
.byte 0x34, 0x13, 0xd9, 0xa0, 0x70, 0x46, 0x12, 0xcc, 0x4b, 0xed, 0x5a, 0x6d, 0x9a, 0xe2, 0xa8, 0x0c
.byte 0x86, 0xca, 0x0c, 0x67, 0x74, 0xae, 0x92, 0x0d, 0x5b, 0x4a, 0x57, 0x59, 0xbe, 0xf7, 0x38, 0x4d
.byte 0x6c, 0x66, 0xff, 0x4a, 0xe4, 0xdc, 0x8f, 0x59, 0x0f, 0x25, 0x61, 0x79, 0x75, 0xce, 0xc8, 0xa6
.byte 0x31, 0x5f, 0x3d, 0x53, 0x41, 0xbf, 0xcf, 0xb3, 0x24, 0xba, 0x2d, 0x80, 0x00, 0x00, 0x00, 0x3b
.byte 0x58, 0x09, 0x2b, 0xe8, 0x89, 0x43, 0x0f, 0x92, 0xf5, 0xec, 0xd8, 0x11, 0x35, 0x86, 0x00, 0x13
.byte 0xaa, 0x89, 0x92, 0x5d, 0x75, 0x9b, 0x53, 0x8a, 0x1f, 0x8b, 0xda, 0x83, 0xaa, 0x8f, 0xf1, 0x37
.byte 0x99, 0x51, 0x22, 0xb5, 0x14, 0xef, 0x6a, 0x0e, 0xb9, 0xeb, 0x69, 0x28, 0xf1, 0xc3, 0x12, 0x68
.byte 0xa8, 0xfd, 0xb6, 0xc9, 0xf0, 0xd2, 0x66, 0x97, 0x98, 0x9a, 0x5e, 0x00, 0x00, 0x00, 0x27, 0x58
.byte 0x02, 0xa8, 0xe6, 0x8f, 0xe2, 0x52, 0xba, 0x6d, 0xef, 0x89, 0x59, 0xf5, 0x12, 0x87, 0xa0, 0xe8
.byte 0x6f, 0x8f, 0x0e, 0xdd, 0x68, 0x14, 0xcd, 0xd2, 0x58, 0x27, 0xaf, 0x9b, 0xb3, 0x96, 0x8f, 0x84
.byte 0xc9, 0x09, 0x50, 0x0a, 0x9c, 0x20, 0x00, 0x00, 0x00, 0x15, 0x58, 0x01, 0xf2, 0x5e, 0x72, 0x36
.byte 0x92, 0x1f, 0x05, 0xfa, 0x47, 0x8d, 0x69, 0x2c, 0x8f, 0x20, 0x0a, 0xfb, 0xfb, 0x2b, 0x20, 0x00
.byte 0x00, 0x00, 0x15, 0x58, 0x01, 0xf2, 0x5e, 0x72, 0x36, 0x92, 0x1f, 0x05, 0xfa, 0x47, 0x8d, 0x69
.byte 0x2c, 0x8f, 0x20, 0x0a, 0xfb, 0xfb, 0x2b, 0x20, 0x00, 0x00, 0x00, 0x15, 0x58, 0x01, 0xf2, 0x5e
.byte 0x72, 0x36, 0x92, 0x1f, 0x05, 0xfa, 0x47, 0x8d, 0x69, 0x2c, 0x8f, 0x20, 0x0a, 0xfb, 0xfb, 0x2b
.byte 0x20, 0x00, 0x00, 0x00, 0x15, 0x58, 0x01, 0xf2, 0x5e, 0x72, 0x36, 0x92, 0x1f, 0x05, 0xfa, 0x47
.byte 0x8d, 0x69, 0x2c, 0x8f, 0x20, 0x0a, 0xfb, 0xfb, 0x2b, 0x20

.global _binary_2_p3_end
_binary_2_p3_end: /* for objcopy compatibility */


.global _2_p3_length
_2_p3_length:
.long 1162
