/* * Data converted from /home/<USER>/bysx/xiaozhi-esp32/main/assets/common/popup.p3
 */
.data
#if !defined (__APPLE__) && !defined (__linux__)
.section .rodata.embedded
#endif

.global popup_p3
popup_p3:

.global _binary_popup_p3_start
_binary_popup_p3_start: /* for objcopy compatibility */
.byte 0x00, 0x00, 0x00, 0x42, 0x58, 0x00, 0x5e, 0x85, 0x70, 0x51, 0x44, 0xa5, 0xf0, 0x03, 0x4c, 0xdb
.byte 0xc8, 0x2f, 0xad, 0xbf, 0x55, 0x96, 0xfa, 0x24, 0xf1, 0xfb, 0x25, 0xb1, 0x48, 0x52, 0xbb, 0x12
.byte 0xf9, 0x91, 0xc1, 0x09, 0x4d, 0xe5, 0x39, 0x7f, 0xc3, 0xb0, 0xe0, 0x25, 0x7e, 0x2c, 0xf6, 0x65
.byte 0x66, 0x7a, 0x4b, 0xee, 0xd9, 0xe5, 0xb1, 0x0f, 0x01, 0xa3, 0x52, 0x65, 0xd9, 0xfc, 0xe0, 0xeb
.byte 0x22, 0x4b, 0x66, 0x58, 0x7d, 0x80, 0x00, 0x00, 0x00, 0x59, 0x58, 0x08, 0x79, 0x60, 0x5a, 0xca
.byte 0xbf, 0xc8, 0xe2, 0xec, 0x24, 0x0b, 0x5a, 0x24, 0x5d, 0x54, 0x24, 0x3f, 0x04, 0xc8, 0x61, 0xb6
.byte 0x44, 0x0e, 0xcf, 0x9b, 0x33, 0x87, 0x8a, 0x5e, 0xc4, 0x6b, 0x3a, 0xe4, 0xda, 0x92, 0x0a, 0xf9
.byte 0xa4, 0xba, 0x1d, 0xf6, 0x46, 0x76, 0x50, 0x68, 0xa8, 0xac, 0xe5, 0xc2, 0xdf, 0x0c, 0x63, 0xaf
.byte 0xfc, 0x94, 0xc9, 0x9a, 0xa6, 0x97, 0x47, 0xba, 0x8a, 0xf3, 0xe9, 0x4a, 0x9f, 0xf2, 0xf9, 0x52
.byte 0xf9, 0xcf, 0x22, 0x17, 0x73, 0x10, 0xb0, 0x3b, 0x6c, 0xa9, 0xc4, 0x63, 0x93, 0xad, 0xe9, 0xe4
.byte 0x14, 0xc2, 0xbf, 0x00, 0x00, 0x00, 0x5a, 0x58, 0x08, 0x6f, 0xe1, 0x55, 0x13, 0x0a, 0x06, 0x33
.byte 0x15, 0x89, 0x4f, 0xa1, 0xeb, 0xf3, 0x59, 0x9f, 0x45, 0xd8, 0x08, 0x73, 0xd1, 0x8f, 0xb6, 0xf6
.byte 0xba, 0x49, 0xc5, 0x17, 0xee, 0xa5, 0x91, 0xb2, 0x5f, 0xbf, 0x0b, 0x15, 0xe9, 0xc3, 0xba, 0x94
.byte 0x8e, 0x17, 0x22, 0x49, 0x57, 0x66, 0x19, 0x72, 0x35, 0xa1, 0x77, 0xb9, 0xd4, 0x20, 0x95, 0x4f
.byte 0x2f, 0xed, 0x59, 0x5e, 0xfe, 0xd9, 0x73, 0xfc, 0x34, 0x06, 0xc1, 0x26, 0x24, 0x0b, 0xd4, 0x2d
.byte 0xdd, 0xe5, 0x78, 0x9c, 0x85, 0x82, 0xe1, 0x96, 0x04, 0x57, 0x16, 0xc2, 0x6e, 0xf2, 0x1d, 0x24
.byte 0x10, 0x00, 0x00, 0x00, 0x95, 0x58, 0xe1, 0xeb, 0xfc, 0x23, 0x54, 0x7f, 0x57, 0x23, 0x9b, 0x04
.byte 0xaf, 0x92, 0x3f, 0x53, 0x00, 0xb7, 0x29, 0x6a, 0xdc, 0xa0, 0xf4, 0x72, 0x34, 0xe2, 0x98, 0xf2
.byte 0x36, 0xd0, 0x09, 0xfa, 0xcf, 0xd0, 0xda, 0x91, 0x0f, 0x4d, 0xbd, 0xc2, 0xb1, 0xeb, 0x4a, 0xc2
.byte 0x3c, 0x3a, 0x66, 0x9c, 0x1e, 0xe1, 0x10, 0x4b, 0x6d, 0xf1, 0x6a, 0x74, 0xae, 0x44, 0x00, 0xaf
.byte 0x79, 0xf4, 0x9d, 0xe1, 0x6c, 0x80, 0xcd, 0x14, 0x40, 0xb6, 0x54, 0x93, 0x24, 0x5e, 0x56, 0x6f
.byte 0x65, 0x5d, 0x7f, 0x27, 0x5b, 0xa8, 0x78, 0x84, 0x52, 0xc5, 0x5f, 0x35, 0x09, 0xef, 0x52, 0xb2
.byte 0x83, 0xef, 0x4d, 0xb2, 0x59, 0x54, 0xbc, 0xe2, 0x1d, 0x6e, 0x97, 0xa4, 0x34, 0xd1, 0xa5, 0xe6
.byte 0x92, 0xfb, 0x8d, 0xcc, 0x52, 0xad, 0xa8, 0x79, 0x34, 0x18, 0x97, 0x8b, 0x06, 0xf9, 0xa9, 0x37
.byte 0xde, 0xed, 0x08, 0xf7, 0x40, 0xb6, 0xfa, 0x67, 0xa4, 0x75, 0x50, 0xe3, 0x93, 0x34, 0x53, 0xf9
.byte 0x55, 0x00, 0x60, 0x75, 0x34, 0x02, 0xe9, 0x76, 0x76, 0xcc, 0x00, 0x00, 0x00, 0x97, 0x58, 0xe8
.byte 0xa2, 0xd4, 0xb7, 0x71, 0xe1, 0xbf, 0xf5, 0x35, 0xd2, 0x5d, 0xdb, 0x2d, 0x37, 0xc3, 0x23, 0xed
.byte 0x14, 0xe7, 0xad, 0x40, 0x08, 0x2f, 0xd7, 0x4f, 0xa6, 0x69, 0x2d, 0x62, 0xba, 0x98, 0x19, 0x2b
.byte 0x30, 0xa7, 0x7f, 0x3a, 0x8d, 0xcc, 0x9e, 0xd8, 0x62, 0xec, 0xf1, 0xfa, 0xb3, 0x08, 0x16, 0x22
.byte 0x7f, 0x8b, 0x21, 0xdb, 0xd5, 0xe3, 0x6e, 0x8b, 0xec, 0xf0, 0xdf, 0x5d, 0x32, 0x32, 0x9b, 0xae
.byte 0x04, 0x27, 0x09, 0xf8, 0x0a, 0x70, 0x0b, 0x79, 0x4f, 0x7e, 0xa0, 0xf7, 0x21, 0x3f, 0x5a, 0x8b
.byte 0x27, 0xce, 0x37, 0xa1, 0x04, 0x74, 0x27, 0xee, 0x5b, 0x3c, 0x8b, 0x18, 0xe2, 0x11, 0x2d, 0x41
.byte 0x15, 0x55, 0x24, 0xcc, 0xe9, 0x50, 0xba, 0xae, 0x6b, 0x21, 0xd4, 0xa4, 0x40, 0x98, 0xda, 0xc0
.byte 0x73, 0xa4, 0x36, 0x60, 0x01, 0x3d, 0x41, 0x95, 0x06, 0x42, 0x64, 0xc2, 0x58, 0x67, 0x92, 0xdc
.byte 0x52, 0x09, 0x93, 0xb5, 0xf4, 0xeb, 0x4e, 0xa6, 0x54, 0x75, 0x5c, 0xd5, 0x47, 0x20, 0xa7, 0xa3
.byte 0x99, 0xc0, 0x10, 0x80, 0xf0, 0x00, 0x00, 0x00, 0x9d, 0x58, 0xe7, 0xa7, 0x45, 0xd8, 0xc6, 0x6d
.byte 0x1f, 0x2d, 0x5e, 0xbe, 0x04, 0x98, 0xaa, 0xd7, 0xfe, 0xff, 0xa8, 0x5c, 0x6e, 0x07, 0x05, 0x0f
.byte 0x10, 0xe2, 0x07, 0x8d, 0x09, 0x4b, 0x9c, 0x86, 0x3a, 0x64, 0xcd, 0xc9, 0x9a, 0x52, 0x71, 0x86
.byte 0xb9, 0x3e, 0xe3, 0x29, 0xc1, 0x06, 0x3f, 0x21, 0x80, 0x6e, 0x1b, 0x82, 0xf9, 0xfe, 0xf0, 0xb9
.byte 0xb0, 0x52, 0x9a, 0xbe, 0x31, 0xee, 0x50, 0xe5, 0x19, 0x9d, 0x33, 0x69, 0x09, 0xa3, 0x76, 0xaa
.byte 0x04, 0xc9, 0xc2, 0xdc, 0xd9, 0x68, 0xff, 0x21, 0x54, 0xb5, 0xec, 0x8b, 0x6d, 0x3a, 0x90, 0x25
.byte 0x77, 0x73, 0x26, 0x86, 0xd9, 0xb3, 0xec, 0xb9, 0x57, 0xb5, 0x6b, 0x95, 0xa8, 0x6e, 0x1e, 0xc9
.byte 0x9b, 0x38, 0x1c, 0xa9, 0xba, 0x92, 0xeb, 0x59, 0x1b, 0x39, 0x67, 0x1c, 0x0f, 0xa9, 0xe8, 0xce
.byte 0xc7, 0xc9, 0xba, 0xf1, 0x6b, 0xb7, 0xc1, 0x08, 0x40, 0x25, 0xe4, 0xb2, 0xc8, 0x0b, 0xd2, 0xd9
.byte 0x65, 0x89, 0x07, 0x01, 0xbe, 0xd0, 0x0e, 0x2a, 0xd7, 0xbe, 0x7d, 0xb8, 0x46, 0x80, 0xcf, 0xd9
.byte 0x26, 0xe7, 0x4c, 0xd3, 0xb5, 0x74, 0x00, 0x00, 0x00, 0xa0, 0x58, 0xe9, 0x0c, 0xed, 0x16, 0xdf
.byte 0x23, 0xdc, 0x83, 0xab, 0xbf, 0x9e, 0xac, 0x43, 0xb0, 0x93, 0x03, 0x27, 0xe1, 0x50, 0xe7, 0x89
.byte 0x1e, 0xe5, 0xf1, 0x83, 0xaa, 0x94, 0x46, 0x5c, 0xd6, 0x2d, 0x5b, 0x94, 0x33, 0x33, 0x23, 0x75
.byte 0x59, 0x1d, 0x05, 0x1a, 0x61, 0x10, 0x03, 0xa4, 0x69, 0xf2, 0xd4, 0xa8, 0xaa, 0x07, 0xc4, 0xe5
.byte 0x66, 0x89, 0x36, 0x77, 0x0e, 0x52, 0x19, 0xda, 0x65, 0xb7, 0x40, 0xcf, 0x5b, 0xcc, 0xcc, 0x5f
.byte 0xb2, 0xee, 0x12, 0x4a, 0x61, 0xda, 0xc7, 0x6d, 0xb9, 0xbe, 0xb1, 0xa8, 0xaf, 0xd3, 0x13, 0xca
.byte 0x83, 0x7a, 0x1e, 0x15, 0x6a, 0x71, 0x26, 0xa9, 0xf5, 0x9d, 0xd2, 0x22, 0x30, 0x03, 0x00, 0x2b
.byte 0x31, 0xda, 0x0d, 0xab, 0x24, 0x12, 0x59, 0xf4, 0xee, 0xe7, 0x6d, 0xda, 0xdc, 0x94, 0xc6, 0xe4
.byte 0x8b, 0x64, 0x2d, 0x54, 0x3b, 0x4f, 0x21, 0x09, 0x6a, 0x15, 0xe4, 0xb3, 0xbc, 0x56, 0xa5, 0xac
.byte 0xdf, 0x9b, 0x68, 0xc6, 0x40, 0xe1, 0x3a, 0xd3, 0x74, 0xb2, 0x17, 0xb8, 0xd8, 0xac, 0x0e, 0x64
.byte 0xf4, 0xa0, 0x9a, 0x81, 0x67, 0xe2, 0x9d, 0xa2, 0xe8, 0x01, 0x00, 0x00, 0x00, 0x5b, 0x58, 0xe2
.byte 0x29, 0x9f, 0x5d, 0x9e, 0x2d, 0xa8, 0xf6, 0x6b, 0x27, 0x40, 0xb7, 0x28, 0xa2, 0x65, 0xc3, 0xb7
.byte 0x06, 0xfe, 0xec, 0x5b, 0x05, 0x0f, 0x26, 0xe2, 0x14, 0xeb, 0x46, 0xc6, 0x21, 0x95, 0x5c, 0x57
.byte 0xeb, 0xe8, 0x8b, 0xc6, 0xf7, 0xfa, 0x0e, 0xd3, 0xa0, 0x12, 0x13, 0x13, 0xc2, 0x24, 0x64, 0xe3
.byte 0x59, 0x0a, 0x71, 0xc1, 0x51, 0x92, 0xb9, 0x98, 0x4b, 0x69, 0x3b, 0x8d, 0x42, 0x1a, 0x8e, 0xec
.byte 0x4a, 0x87, 0xc4, 0x33, 0xcc, 0x79, 0xc8, 0x02, 0x2b, 0x43, 0xb2, 0x24, 0xcb, 0xf6, 0xce, 0xb6
.byte 0x2c, 0x26, 0x13, 0xc2, 0xed, 0x73, 0x58, 0x77, 0xe0

.global _binary_popup_p3_end
_binary_popup_p3_end: /* for objcopy compatibility */


.global popup_p3_length
popup_p3_length:
.long 985
