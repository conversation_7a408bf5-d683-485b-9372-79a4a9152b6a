/* * Data converted from /home/<USER>/bysx/xiaozhi-esp32/main/assets/zh-CN/8.p3
 */
.data
#if !defined (__APPLE__) && !defined (__linux__)
.section .rodata.embedded
#endif

.global _8_p3
_8_p3:

.global _binary_8_p3_start
_binary_8_p3_start: /* for objcopy compatibility */
.byte 0x00, 0x00, 0x00, 0x52, 0x58, 0x00, 0x2c, 0xa6, 0x1f, 0x72, 0xdf, 0xbc, 0xc1, 0xcb, 0xff, 0x93
.byte 0x50, 0x76, 0xe2, 0x6c, 0xe9, 0x91, 0x9e, 0x79, 0x5c, 0x59, 0x6d, 0x48, 0x14, 0x0b, 0x00, 0x0c
.byte 0x46, 0xca, 0x2b, 0x07, 0x54, 0x70, 0xb2, 0xf1, 0x4d, 0x64, 0xd9, 0x78, 0x54, 0x31, 0x6e, 0x5a
.byte 0x86, 0x80, 0x79, 0xa3, 0x31, 0x6e, 0x15, 0x31, 0xa1, 0xb9, 0x9b, 0x08, 0xa8, 0x45, 0x1d, 0xdc
.byte 0x4d, 0xbd, 0xb3, 0x5d, 0xc0, 0xea, 0x59, 0x3a, 0x30, 0x74, 0x32, 0xe9, 0x8f, 0x77, 0x7a, 0x0e
.byte 0x8e, 0x3f, 0xef, 0x12, 0x83, 0x40, 0x00, 0x00, 0x00, 0x59, 0x58, 0x07, 0xa4, 0xb5, 0x44, 0x22
.byte 0xfd, 0x91, 0x8c, 0x84, 0xf8, 0xfa, 0xde, 0x42, 0x3f, 0xa0, 0x6d, 0x91, 0x66, 0x90, 0x5a, 0xf4
.byte 0xaf, 0xd1, 0x0f, 0x63, 0x38, 0xbb, 0xfa, 0xf9, 0x52, 0xe5, 0x17, 0xfe, 0x75, 0xd9, 0xda, 0xae
.byte 0x81, 0xf8, 0x39, 0x32, 0x9b, 0xd5, 0x08, 0xfe, 0x51, 0xbf, 0xf1, 0x04, 0xfe, 0xaf, 0x1a, 0x60
.byte 0x4e, 0xa2, 0xf7, 0xa3, 0x3f, 0xb2, 0xe4, 0x4e, 0x60, 0x52, 0xf9, 0x53, 0xdd, 0xcc, 0x06, 0x20
.byte 0xc7, 0x3f, 0xe6, 0x19, 0xd4, 0xc6, 0xac, 0xf3, 0x88, 0x10, 0xf8, 0x34, 0x9f, 0xb1, 0x95, 0x42
.byte 0x17, 0x22, 0x30, 0x00, 0x00, 0x00, 0x6d, 0x58, 0xe0, 0x18, 0x46, 0x0b, 0x75, 0x76, 0x0d, 0x1b
.byte 0xbd, 0xdf, 0x5c, 0xc5, 0xbd, 0x17, 0xca, 0x07, 0xf7, 0xaa, 0x3c, 0x21, 0x4a, 0x3b, 0x81, 0xe8
.byte 0x96, 0x0b, 0x62, 0x92, 0x95, 0x4a, 0x58, 0xd0, 0xd0, 0xe2, 0x48, 0x48, 0x2d, 0x49, 0x9d, 0xcd
.byte 0xaa, 0x95, 0xef, 0x23, 0x41, 0x05, 0x6b, 0x0d, 0x95, 0xc8, 0x57, 0x35, 0xfc, 0x3a, 0x42, 0x03
.byte 0x20, 0x95, 0x0f, 0xe0, 0xb2, 0xca, 0x3d, 0xa6, 0x38, 0x5f, 0x90, 0xa4, 0xaa, 0x79, 0x8b, 0x72
.byte 0x2f, 0xd8, 0x2a, 0x79, 0xce, 0xd5, 0x3d, 0x44, 0xdc, 0xf6, 0x29, 0xe7, 0x2e, 0x05, 0xc8, 0x3a
.byte 0x4a, 0x2e, 0xc0, 0xc7, 0x41, 0x5f, 0x9a, 0x80, 0xfa, 0x35, 0xa4, 0x6c, 0xa0, 0x37, 0xdd, 0x7a
.byte 0xa0, 0x01, 0x8e, 0xb6, 0x00, 0x00, 0x00, 0x62, 0x58, 0xee, 0x28, 0xdc, 0x75, 0xb4, 0xee, 0x06
.byte 0x94, 0xd7, 0x47, 0x33, 0xaf, 0x8c, 0x8d, 0xbc, 0xc9, 0xc3, 0x13, 0x4c, 0xce, 0xd6, 0x24, 0xbf
.byte 0x7d, 0x93, 0x41, 0xa8, 0xbf, 0x35, 0x4b, 0xa5, 0xf3, 0xe8, 0x71, 0x50, 0x10, 0x4f, 0xb3, 0x62
.byte 0x6c, 0x32, 0x7b, 0xc0, 0xaa, 0xb5, 0xdc, 0xc7, 0x63, 0xf5, 0x89, 0xe7, 0x9d, 0x1e, 0x51, 0xf8
.byte 0x62, 0x38, 0x83, 0x8a, 0x84, 0x30, 0x21, 0xdc, 0xfd, 0x5d, 0x06, 0x0a, 0xbe, 0xcf, 0xcf, 0x30
.byte 0x48, 0x60, 0x34, 0xc2, 0xbe, 0x63, 0x1b, 0x06, 0xf6, 0x9c, 0x8c, 0x7a, 0x5c, 0x6e, 0x28, 0x39
.byte 0xe1, 0x64, 0x76, 0x73, 0x8f, 0x3c, 0xe0, 0x40, 0x1e, 0x17, 0x00, 0x00, 0x00, 0x61, 0x58, 0xee
.byte 0x2c, 0x14, 0x05, 0xf3, 0xd4, 0x48, 0x84, 0x7a, 0xac, 0x77, 0xee, 0xbd, 0xa3, 0x7a, 0x41, 0xc0
.byte 0x25, 0x00, 0xe2, 0xc3, 0x1e, 0x76, 0x59, 0xac, 0xe1, 0x46, 0x64, 0x54, 0x29, 0x1e, 0x1e, 0x95
.byte 0xc6, 0x94, 0xc2, 0xe4, 0x79, 0xfe, 0x92, 0x8c, 0xc3, 0x4c, 0x1a, 0x7e, 0x2c, 0x72, 0xc0, 0x12
.byte 0x2e, 0x73, 0x82, 0x5b, 0x79, 0x8e, 0x84, 0x3e, 0x83, 0xf4, 0x7d, 0xff, 0xbc, 0x40, 0x45, 0x4d
.byte 0x86, 0x5b, 0xdc, 0x6d, 0x59, 0x27, 0xc9, 0xe8, 0x2c, 0xa0, 0x2e, 0x11, 0x49, 0x43, 0x7e, 0x9f
.byte 0x51, 0x58, 0x65, 0x7d, 0x42, 0x68, 0x36, 0xc1, 0x89, 0xfa, 0x95, 0xed, 0xab, 0x51, 0xbc, 0x00
.byte 0x00, 0x00, 0x60, 0x58, 0xed, 0xe8, 0xc7, 0x75, 0x4c, 0x14, 0x0d, 0xef, 0x36, 0x48, 0xd9, 0x39
.byte 0x7a, 0xc4, 0x6e, 0x54, 0x00, 0x19, 0x7f, 0x95, 0x70, 0xca, 0x94, 0x5b, 0x87, 0x98, 0x3a, 0x98
.byte 0x64, 0x6a, 0xf9, 0xd6, 0xa5, 0x9e, 0xa4, 0xe6, 0xd2, 0x37, 0x97, 0x8e, 0xf3, 0x64, 0xab, 0xeb
.byte 0x11, 0x93, 0x3b, 0xdc, 0xbd, 0x47, 0xac, 0x37, 0xed, 0xd5, 0xc4, 0xca, 0xac, 0x71, 0x5e, 0xff
.byte 0x99, 0x61, 0x83, 0x51, 0x90, 0x93, 0x1a, 0x02, 0xc7, 0x7e, 0xb6, 0xc2, 0x5e, 0x5f, 0x29, 0x20
.byte 0xc1, 0xb2, 0x84, 0x3a, 0x39, 0x00, 0xae, 0xf7, 0x3a, 0x1c, 0x90, 0xc4, 0x87, 0x46, 0xf3, 0x77
.byte 0xe3, 0x70, 0x4c, 0x00, 0x00, 0x00, 0x6e, 0x58, 0xec, 0x61, 0x7f, 0x91, 0x00, 0x14, 0x19, 0xb1
.byte 0xc3, 0xea, 0xf1, 0x52, 0xf3, 0xf7, 0x43, 0xc8, 0x5d, 0xbb, 0xa3, 0x9f, 0x84, 0x2c, 0x69, 0x30
.byte 0x53, 0xb8, 0x8f, 0xe3, 0x2f, 0xcd, 0x69, 0xa4, 0xd6, 0x6d, 0xc0, 0x1f, 0x94, 0xc0, 0xee, 0x7a
.byte 0x88, 0x57, 0xb3, 0x36, 0xbc, 0x64, 0xb3, 0xc9, 0x9d, 0x8d, 0x38, 0x8f, 0x08, 0xd6, 0x5f, 0x72
.byte 0xe2, 0xe5, 0x18, 0x1d, 0x85, 0x6b, 0xb1, 0xca, 0xba, 0x1f, 0xac, 0x6a, 0xf8, 0x1b, 0x41, 0xf8
.byte 0x07, 0xf9, 0xca, 0x43, 0xa1, 0x14, 0x62, 0x78, 0xe1, 0x94, 0x09, 0x0a, 0xfd, 0xc8, 0xbb, 0x47
.byte 0xf6, 0x7b, 0xd7, 0x35, 0x20, 0x43, 0x42, 0xdd, 0x10, 0xb9, 0x86, 0xe4, 0x24, 0x0d, 0x87, 0x86
.byte 0xdc, 0xa1, 0xfd, 0x9d, 0x19, 0x00, 0x00, 0x00, 0x68, 0x58, 0xcf, 0x94, 0x48, 0x2d, 0x4c, 0x31
.byte 0x08, 0x85, 0xe0, 0x6a, 0xd7, 0x8e, 0x1d, 0x0a, 0x69, 0x0e, 0xc5, 0x49, 0xe3, 0x2d, 0xdd, 0xde
.byte 0x72, 0xf5, 0xa2, 0x8a, 0xda, 0xd5, 0x80, 0x22, 0x81, 0x5f, 0xa6, 0xd6, 0x1c, 0x8e, 0xca, 0x7c
.byte 0xa3, 0xd2, 0xfb, 0x97, 0xa5, 0xea, 0xcb, 0x1d, 0x58, 0x64, 0xe6, 0x6a, 0x31, 0x4a, 0xe4, 0xb8
.byte 0x88, 0xb6, 0x3b, 0x0a, 0x27, 0x68, 0x93, 0x7a, 0x3c, 0xf0, 0x70, 0x6b, 0xcc, 0x5e, 0xc6, 0x04
.byte 0x6e, 0x80, 0xa6, 0x1d, 0x30, 0xa4, 0x26, 0xd6, 0x4c, 0xc3, 0x0f, 0x0f, 0xd4, 0x3d, 0xe3, 0x67
.byte 0x45, 0x96, 0x24, 0xdb, 0x62, 0x64, 0x05, 0x54, 0xcb, 0x9f, 0x02, 0x5d, 0xd9, 0xb6, 0x79, 0x09
.byte 0x40, 0x00, 0x00, 0x00, 0x50, 0x58, 0x00, 0xbf, 0x53, 0x87, 0xf6, 0xca, 0x03, 0x61, 0xc7, 0x6e
.byte 0x3f, 0x89, 0x28, 0x46, 0x1b, 0xf5, 0x9e, 0xae, 0x49, 0xa9, 0xf1, 0xa4, 0xd2, 0x8f, 0x8b, 0xee
.byte 0xbe, 0x06, 0x85, 0x45, 0x39, 0xe1, 0x57, 0xf8, 0xb3, 0x9e, 0xef, 0xc6, 0x4d, 0x4d, 0x30, 0x03
.byte 0x77, 0x33, 0x81, 0x3e, 0x07, 0x3d, 0xb1, 0x56, 0xd5, 0x46, 0x54, 0xf1, 0xd8, 0x59, 0x22, 0xb8
.byte 0xfe, 0x62, 0x53, 0xf7, 0x3c, 0xd7, 0x18, 0x4d, 0xfa, 0xc7, 0xdc, 0xec, 0x39, 0xaa, 0x5d, 0x01
.byte 0x22, 0x0c, 0xad, 0x76, 0x9a, 0x00, 0x00, 0x00, 0x59, 0x58, 0x08, 0x6f, 0xaa, 0xf8, 0x49, 0xbb
.byte 0x52, 0x81, 0x76, 0x41, 0xc0, 0x65, 0xae, 0xaa, 0x8a, 0x15, 0xcc, 0xa8, 0x62, 0x03, 0xb2, 0x3b
.byte 0x24, 0xbe, 0xa6, 0x15, 0x7d, 0x21, 0xa4, 0x84, 0x61, 0x24, 0xce, 0xa0, 0xdd, 0x49, 0xe5, 0x34
.byte 0xff, 0x1f, 0x49, 0x04, 0x26, 0x8d, 0x01, 0x18, 0xa2, 0xe8, 0x9a, 0x71, 0x8d, 0x65, 0xde, 0xf2
.byte 0x4f, 0x63, 0xd4, 0x9a, 0x2e, 0x69, 0x49, 0x73, 0xdc, 0x55, 0x46, 0xad, 0xc6, 0x32, 0xda, 0xe0
.byte 0x2b, 0x9e, 0x6a, 0x3e, 0x8b, 0xb5, 0xfe, 0xbf, 0xb1, 0x0f, 0x79, 0x39, 0x87, 0xed, 0x3e, 0xf8
.byte 0x3e, 0x5c, 0x00, 0x00, 0x00, 0x41, 0x58, 0x06, 0x9a, 0x88, 0xf5, 0x16, 0xdf, 0x70, 0xf1, 0xc9
.byte 0x3a, 0x53, 0x34, 0xbe, 0xab, 0xb8, 0x78, 0x95, 0xd9, 0xf9, 0x05, 0xd2, 0x02, 0xc9, 0x98, 0xcc
.byte 0x4b, 0xd5, 0xdd, 0x61, 0xce, 0xdc, 0x46, 0x7a, 0xc6, 0x35, 0xb8, 0x41, 0x24, 0x9f, 0xd2, 0x72
.byte 0x22, 0xb8, 0x90, 0x14, 0xff, 0x30, 0x1f, 0x0f, 0x9b, 0x86, 0x5b, 0x04, 0x5f, 0xab, 0x05, 0xb4
.byte 0xbb, 0x65, 0x32, 0x1a, 0x9c, 0x9c, 0x50, 0x00, 0x00, 0x00, 0x1f, 0x58, 0x02, 0x23, 0xbc, 0xe4
.byte 0x3f, 0xed, 0x78, 0x1f, 0xe0, 0x12, 0x95, 0x52, 0xe3, 0x9a, 0xa2, 0x4b, 0x02, 0xbf, 0x4a, 0x66
.byte 0x85, 0xd2, 0xd1, 0x32, 0x99, 0x1f, 0x15, 0x4a, 0x0e, 0xbc, 0x00, 0x00, 0x00, 0x15, 0x58, 0x01
.byte 0xf2, 0x5e, 0x72, 0x36, 0x92, 0x1f, 0x05, 0xfa, 0x47, 0x8d, 0x69, 0x2c, 0x8f, 0x20, 0x0a, 0xfb
.byte 0xfb, 0x2b, 0x20, 0x00, 0x00, 0x00, 0x15, 0x58, 0x01, 0xf2, 0x5e, 0x72, 0x36, 0x92, 0x1f, 0x05
.byte 0xfa, 0x47, 0x8d, 0x69, 0x2c, 0x8f, 0x20, 0x0a, 0xfb, 0xfb, 0x2b, 0x20, 0x00, 0x00, 0x00, 0x15
.byte 0x58, 0x01, 0xf2, 0x5e, 0x72, 0x36, 0x92, 0x1f, 0x05, 0xfa, 0x47, 0x8d, 0x69, 0x2c, 0x8f, 0x20
.byte 0x0a, 0xfb, 0xfb, 0x2b, 0x20

.global _binary_8_p3_end
_binary_8_p3_end: /* for objcopy compatibility */


.global _8_p3_length
_8_p3_length:
.long 1173
