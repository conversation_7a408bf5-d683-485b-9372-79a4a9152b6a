/* * Data converted from /home/<USER>/bysx/xiaozhi-esp32/main/assets/zh-CN/0.p3
 */
.data
#if !defined (__APPLE__) && !defined (__linux__)
.section .rodata.embedded
#endif

.global _0_p3
_0_p3:

.global _binary_0_p3_start
_binary_0_p3_start: /* for objcopy compatibility */
.byte 0x00, 0x00, 0x00, 0x48, 0x58, 0x00, 0x5e, 0x52, 0x05, 0x3b, 0xb7, 0x75, 0x60, 0xd0, 0xec, 0x40
.byte 0x62, 0xe8, 0xfb, 0x2b, 0xa3, 0x62, 0x94, 0xa6, 0xaf, 0xb6, 0x0f, 0x37, 0x72, 0x04, 0x77, 0x97
.byte 0xa9, 0xab, 0xa0, 0xcd, 0x1b, 0x76, 0xa8, 0xa9, 0x8a, 0xda, 0x8c, 0xdc, 0xc4, 0x82, 0x20, 0xea
.byte 0xdb, 0x0a, 0xec, 0x13, 0xe2, 0x73, 0x04, 0x51, 0xf3, 0x15, 0x7c, 0xbc, 0x63, 0x7f, 0x47, 0xd3
.byte 0x46, 0x4e, 0x6c, 0x93, 0xc3, 0xd8, 0xc3, 0xa4, 0x21, 0xdc, 0x45, 0x17, 0x00, 0x00, 0x00, 0x59
.byte 0x58, 0x09, 0x37, 0x35, 0x89, 0x60, 0xf9, 0x58, 0xd1, 0x81, 0xe8, 0x37, 0xc7, 0x8b, 0x20, 0x7c
.byte 0x28, 0x7d, 0x1c, 0x54, 0x6f, 0xbe, 0xe9, 0x17, 0x63, 0x5b, 0x2e, 0xd2, 0x06, 0x4c, 0x20, 0x53
.byte 0xf3, 0x36, 0x16, 0x84, 0x68, 0xad, 0x21, 0x74, 0xc6, 0x34, 0xe8, 0xce, 0x7e, 0xb1, 0x6f, 0xbf
.byte 0x2f, 0xf2, 0xd3, 0xbe, 0xc8, 0xa9, 0x2a, 0x93, 0xe9, 0x61, 0x5b, 0x46, 0x49, 0x03, 0xc2, 0x24
.byte 0xdd, 0xe4, 0xf6, 0x3b, 0xd8, 0x08, 0x3c, 0x55, 0x47, 0x5d, 0x12, 0x27, 0x88, 0x36, 0x20, 0x56
.byte 0x3e, 0x2a, 0x38, 0x43, 0x0a, 0x04, 0x76, 0xb2, 0xb0, 0x00, 0x00, 0x00, 0x83, 0x58, 0xe0, 0x59
.byte 0xdf, 0x0c, 0xf0, 0xd0, 0x79, 0xd7, 0x4b, 0xb5, 0x4c, 0x4d, 0xca, 0x89, 0xf2, 0xef, 0x9a, 0xb7
.byte 0x32, 0x91, 0x49, 0x96, 0x17, 0x92, 0xfa, 0x4d, 0x8e, 0x50, 0xfb, 0x3d, 0xcf, 0xd9, 0x15, 0x02
.byte 0xf6, 0x3f, 0xe3, 0x22, 0x2f, 0x44, 0x14, 0x2e, 0x7f, 0xd8, 0xc7, 0xe1, 0x7a, 0x99, 0xeb, 0x5a
.byte 0xb3, 0x64, 0x94, 0xe4, 0x67, 0x13, 0xa2, 0x63, 0x0b, 0x64, 0x4f, 0xe6, 0x5c, 0x74, 0x5d, 0x06
.byte 0x79, 0xda, 0x72, 0xba, 0x7e, 0x8f, 0x24, 0x9f, 0x3b, 0xe2, 0xb1, 0x67, 0x0a, 0x1e, 0x15, 0xe4
.byte 0xf4, 0xc3, 0x09, 0x0b, 0x21, 0xd9, 0xe8, 0x78, 0x31, 0x4e, 0x01, 0x85, 0x18, 0x01, 0x10, 0x72
.byte 0x7e, 0xe2, 0x7a, 0x18, 0xbf, 0x0f, 0xd8, 0xdb, 0x5b, 0x6c, 0x62, 0x1f, 0xa0, 0x0c, 0xe5, 0xc8
.byte 0xe1, 0xad, 0x49, 0xf4, 0x2e, 0x6e, 0x6c, 0xc9, 0xb4, 0x78, 0x8c, 0xcd, 0x83, 0xfa, 0xa1, 0xf1
.byte 0x00, 0x00, 0x00, 0x89, 0x58, 0xed, 0x34, 0xd4, 0x86, 0x0d, 0x80, 0xc3, 0x3b, 0xaa, 0x18, 0xd4
.byte 0x61, 0x30, 0x1f, 0x8f, 0x0e, 0x81, 0xa5, 0x87, 0xed, 0xfe, 0x99, 0x9e, 0xf1, 0xec, 0x62, 0x1a
.byte 0x5f, 0xa3, 0x75, 0xd7, 0x42, 0xa2, 0x07, 0x56, 0xbc, 0x05, 0x62, 0x60, 0xe7, 0x75, 0x58, 0x8b
.byte 0xd9, 0x02, 0x16, 0x1e, 0x4d, 0x7b, 0xea, 0xd1, 0xb2, 0xe2, 0xb6, 0x2b, 0x5e, 0x24, 0xb0, 0x4d
.byte 0x70, 0xf0, 0xa6, 0xe9, 0xb6, 0x84, 0x2d, 0xfa, 0xdb, 0x0c, 0xf8, 0x7e, 0x84, 0xb3, 0xa8, 0x20
.byte 0x58, 0x0e, 0x26, 0x71, 0x1b, 0xb8, 0x37, 0x2b, 0x5e, 0xf9, 0xa5, 0x47, 0x8f, 0x21, 0xf8, 0x40
.byte 0x5d, 0x78, 0x95, 0xc4, 0x8a, 0x40, 0x37, 0x8b, 0x77, 0x3d, 0x5f, 0x2d, 0x96, 0xdd, 0x17, 0x3f
.byte 0xf0, 0x48, 0xf1, 0x03, 0x6e, 0x1c, 0x8b, 0xd2, 0x4c, 0x17, 0xaf, 0x18, 0xd5, 0xab, 0x1a, 0xea
.byte 0x08, 0x05, 0x58, 0x16, 0xe5, 0x57, 0x4c, 0x44, 0xca, 0xf8, 0x8d, 0xd8, 0x80, 0x00, 0x00, 0x00
.byte 0x65, 0x58, 0xed, 0xfb, 0x2f, 0x63, 0xd9, 0xa9, 0xdd, 0x66, 0xb3, 0x89, 0xf7, 0x8b, 0x2c, 0x16
.byte 0x45, 0x68, 0x91, 0x19, 0xe0, 0xce, 0x10, 0x16, 0xac, 0x82, 0xc9, 0xe1, 0xbe, 0xa7, 0xea, 0x71
.byte 0x0d, 0x7f, 0x1e, 0x12, 0x31, 0xc6, 0x37, 0x0b, 0xbf, 0x4f, 0xae, 0xb9, 0xf2, 0x62, 0xda, 0x06
.byte 0x4b, 0x5b, 0x3c, 0xce, 0x11, 0xb5, 0xa8, 0x6d, 0xd0, 0x10, 0xdc, 0xc1, 0x7d, 0xdd, 0x6f, 0x9c
.byte 0xe8, 0x6c, 0xff, 0x70, 0x0d, 0xc7, 0x11, 0x23, 0x25, 0x8c, 0x9d, 0xfb, 0xae, 0xb6, 0xb0, 0x47
.byte 0xc6, 0x9d, 0x61, 0x44, 0xa5, 0xf8, 0x7b, 0x31, 0x67, 0xf9, 0xf0, 0x1b, 0x71, 0xb2, 0xac, 0x1f
.byte 0x6a, 0xd2, 0xa5, 0xb6, 0x93, 0xf6, 0x00, 0x00, 0x00, 0x69, 0x58, 0xed, 0x31, 0xed, 0x17, 0xf8
.byte 0xe9, 0x50, 0x95, 0xbf, 0x65, 0x48, 0x99, 0x8d, 0xe8, 0x87, 0x5f, 0xfe, 0x28, 0x4b, 0x8c, 0x7b
.byte 0x7a, 0xb3, 0xa5, 0xe3, 0xe2, 0x33, 0x1c, 0xff, 0x4d, 0x99, 0x04, 0x32, 0x11, 0xdc, 0x7f, 0xcd
.byte 0x52, 0x13, 0xe7, 0xea, 0x87, 0x5a, 0xf1, 0xbf, 0x8f, 0x4f, 0xe1, 0xb9, 0xdb, 0x8c, 0x60, 0x5b
.byte 0x32, 0xd5, 0xa3, 0xdb, 0x05, 0x27, 0x58, 0xba, 0x90, 0xc6, 0xb8, 0xdd, 0x14, 0x49, 0xbe, 0x69
.byte 0x9f, 0xb9, 0x3e, 0x4e, 0xf2, 0xeb, 0x64, 0xb4, 0x53, 0xbf, 0x8b, 0x94, 0x67, 0x3b, 0x77, 0xed
.byte 0xba, 0x69, 0x70, 0x2b, 0x8f, 0x08, 0xc3, 0xd1, 0xc1, 0x15, 0xea, 0x56, 0x83, 0x6a, 0xff, 0x24
.byte 0x03, 0x15, 0x80, 0x00, 0x00, 0x00, 0x75, 0x58, 0xea, 0x5c, 0xa2, 0xf8, 0xde, 0x95, 0xac, 0xb9
.byte 0x68, 0x44, 0x0e, 0x25, 0xc2, 0xa1, 0x7e, 0x40, 0x22, 0x46, 0xc4, 0xb5, 0x1e, 0x19, 0x42, 0xc0
.byte 0xa9, 0x04, 0x2e, 0xda, 0xb8, 0xb4, 0xa2, 0x7b, 0xef, 0x28, 0x25, 0xa7, 0x60, 0xf9, 0x59, 0xc1
.byte 0xdb, 0x0f, 0xc6, 0x4b, 0x60, 0x88, 0x6b, 0xb5, 0xa9, 0x6e, 0x5d, 0x3c, 0x31, 0x75, 0xdc, 0xfb
.byte 0x19, 0xa9, 0xb7, 0x20, 0x9e, 0x6a, 0x12, 0xae, 0xba, 0x93, 0xb9, 0x4f, 0x71, 0x79, 0x26, 0x17
.byte 0x28, 0x01, 0x37, 0x4f, 0xf4, 0x6c, 0x5a, 0xab, 0x39, 0x9b, 0x7b, 0x0f, 0x0e, 0x89, 0xa7, 0x22
.byte 0x9a, 0x43, 0xc5, 0x0d, 0x44, 0xb0, 0xc2, 0xef, 0xb1, 0xdc, 0x10, 0x56, 0xd0, 0x12, 0x84, 0xe3
.byte 0x8a, 0xc3, 0x50, 0x49, 0x3c, 0x9a, 0xc5, 0x8a, 0x18, 0xef, 0xec, 0xa8, 0x00, 0x00, 0x00, 0x69
.byte 0x58, 0xe9, 0xe2, 0xc2, 0xc8, 0xa1, 0x24, 0x0a, 0x4b, 0x4a, 0xf1, 0x82, 0x23, 0xc9, 0x6a, 0x77
.byte 0x05, 0x83, 0xea, 0xcb, 0xf3, 0x24, 0x75, 0xe4, 0xb9, 0x52, 0x9f, 0xea, 0xaa, 0x0f, 0xf2, 0x9f
.byte 0xdf, 0xec, 0x55, 0x9b, 0x4d, 0x62, 0x64, 0xb4, 0x52, 0xa5, 0x1d, 0xe4, 0xcf, 0x50, 0x03, 0x26
.byte 0x87, 0x3a, 0xa7, 0x18, 0xaf, 0x82, 0x66, 0xf4, 0xda, 0x60, 0xb1, 0x85, 0x37, 0x02, 0x07, 0xe4
.byte 0xee, 0x0c, 0x62, 0xa1, 0x90, 0x7a, 0x91, 0x59, 0x95, 0x95, 0x17, 0x04, 0x36, 0x51, 0xcd, 0xf1
.byte 0x98, 0x62, 0xd6, 0xa2, 0xa6, 0x3d, 0x85, 0xa8, 0x26, 0x07, 0x83, 0x32, 0xc6, 0xf5, 0x92, 0x66
.byte 0x1e, 0xa4, 0x68, 0x04, 0x68, 0x45, 0x1e, 0x1d, 0x3e, 0x00, 0x00, 0x00, 0x64, 0x58, 0x86, 0xbf
.byte 0x26, 0x8f, 0xed, 0xbf, 0xfa, 0xd4, 0x31, 0xe5, 0xba, 0x89, 0xf6, 0x04, 0xe2, 0x3b, 0x57, 0x08
.byte 0xcb, 0xbb, 0x44, 0x87, 0xa4, 0xbe, 0xbf, 0x17, 0x49, 0x37, 0xd0, 0x1e, 0x56, 0x86, 0x5b, 0xb4
.byte 0x81, 0xd4, 0xae, 0x45, 0xfb, 0x31, 0x29, 0xb6, 0x59, 0x85, 0x27, 0x8c, 0xe6, 0xd0, 0x60, 0xa1
.byte 0x2f, 0x94, 0xac, 0x35, 0xe8, 0xa7, 0xc1, 0x74, 0x89, 0x05, 0xe9, 0xcb, 0x54, 0x7e, 0x40, 0xea
.byte 0xdb, 0x12, 0x0b, 0x75, 0xf9, 0x14, 0x4b, 0x23, 0x00, 0xd0, 0x57, 0xae, 0x9c, 0xc6, 0xfe, 0x5b
.byte 0x31, 0x7e, 0x32, 0x8f, 0xad, 0xba, 0x62, 0x8b, 0xf3, 0x51, 0xc4, 0x66, 0xcb, 0xdf, 0x52, 0x76
.byte 0xc0, 0x00, 0x00, 0x00, 0x5b, 0x58, 0x09, 0x4e, 0x90, 0x00, 0xc5, 0x73, 0x51, 0x12, 0xd0, 0xb9
.byte 0x9b, 0x2a, 0xc4, 0x18, 0x62, 0xb2, 0x59, 0xa6, 0x37, 0xd7, 0x6b, 0x86, 0xe6, 0x40, 0xf0, 0xf2
.byte 0x18, 0x86, 0x3e, 0x3b, 0x3a, 0xa7, 0x13, 0xcb, 0xf1, 0x9a, 0x3e, 0xf6, 0x79, 0x42, 0x43, 0x9c
.byte 0x88, 0xc0, 0x86, 0x50, 0x73, 0xe9, 0x2f, 0x0b, 0xa9, 0x96, 0x03, 0xdd, 0xcc, 0x80, 0xbd, 0x11
.byte 0x2f, 0xe5, 0x3e, 0x1e, 0xea, 0xdd, 0x7e, 0x11, 0x38, 0x34, 0x6d, 0x06, 0x80, 0x2a, 0x6b, 0xcd
.byte 0xb7, 0xc3, 0x4d, 0x4d, 0x59, 0x2a, 0xdc, 0x83, 0x31, 0xeb, 0x4e, 0x4e, 0xd6, 0x35, 0x47, 0x80
.byte 0x00, 0x00, 0x00, 0x56, 0x58, 0x09, 0xc5, 0x2e, 0x01, 0xc3, 0xb2, 0x94, 0xab, 0xb8, 0x81, 0xb9
.byte 0xb7, 0x00, 0xa9, 0xa4, 0x21, 0xdf, 0xda, 0x39, 0x0f, 0x39, 0xfe, 0x12, 0x22, 0xa4, 0xcb, 0x0e
.byte 0x7c, 0xbd, 0x05, 0xca, 0x01, 0x23, 0xad, 0xe5, 0x83, 0x68, 0x41, 0xa1, 0x85, 0xbb, 0xcb, 0x68
.byte 0x86, 0x78, 0x3a, 0xd5, 0x52, 0x49, 0x96, 0x54, 0x7b, 0xe0, 0x5e, 0x59, 0x8a, 0x81, 0xc8, 0xb9
.byte 0x84, 0xd3, 0x41, 0x60, 0xbe, 0x00, 0x95, 0xbd, 0xd2, 0xed, 0xb2, 0x81, 0x59, 0xd9, 0x21, 0xa6
.byte 0x74, 0xf2, 0xc3, 0x85, 0xff, 0xac, 0xc4, 0x7c, 0xf2, 0xf8, 0x00, 0x00, 0x00, 0x29, 0x58, 0x04
.byte 0xfe, 0xf5, 0x3f, 0x2e, 0xfc, 0xcb, 0x6f, 0x7f, 0x6e, 0x09, 0x49, 0x4f, 0x9a, 0x5e, 0x32, 0xed
.byte 0x4e, 0x2e, 0x73, 0x37, 0xbb, 0x21, 0x88, 0xbd, 0xdf, 0xca, 0x73, 0x58, 0xa4, 0x34, 0xf7, 0x9d
.byte 0x14, 0x28, 0xfc, 0xab, 0x6d, 0x0a, 0x5a, 0x00, 0x00, 0x00, 0x15, 0x58, 0x01, 0xf2, 0x5c, 0x90
.byte 0xc9, 0x88, 0xe7, 0xea, 0x4b, 0x85, 0x41, 0x1b, 0x7a, 0x90, 0x8b, 0x3e, 0x4c, 0x45, 0x79, 0x20
.byte 0x00, 0x00, 0x00, 0x15, 0x58, 0x01, 0xf2, 0x5e, 0x72, 0x36, 0x92, 0x1f, 0x05, 0xfa, 0x47, 0x8d
.byte 0x69, 0x2c, 0x8f, 0x20, 0x0a, 0xfb, 0xfb, 0x2b, 0x20, 0x00, 0x00, 0x00, 0x15, 0x58, 0x01, 0xf2
.byte 0x5e, 0x72, 0x36, 0x92, 0x1f, 0x05, 0xfa, 0x47, 0x8d, 0x69, 0x2c, 0x8f, 0x20, 0x0a, 0xfb, 0xfb
.byte 0x2b, 0x20, 0x00, 0x00, 0x00, 0x15, 0x58, 0x01, 0xf2, 0x5e, 0x72, 0x36, 0x92, 0x1f, 0x05, 0xfa
.byte 0x47, 0x8d, 0x69, 0x2c, 0x8f, 0x20, 0x0a, 0xfb, 0xfb, 0x2b, 0x20

.global _binary_0_p3_end
_binary_0_p3_end: /* for objcopy compatibility */


.global _0_p3_length
_0_p3_length:
.long 1323
