/* * Data converted from /home/<USER>/bysx/xiaozhi-esp32/main/assets/common/success.p3
 */
.data
#if !defined (__APPLE__) && !defined (__linux__)
.section .rodata.embedded
#endif

.global success_p3
success_p3:

.global _binary_success_p3_start
_binary_success_p3_start: /* for objcopy compatibility */
.byte 0x00, 0x00, 0x00, 0x42, 0x58, 0x22, 0xf9, 0x7a, 0x4a, 0x2d, 0x85, 0x66, 0x60, 0x7d, 0x43, 0x0e
.byte 0xe1, 0x6a, 0x33, 0xb8, 0x9f, 0x63, 0x54, 0xdd, 0xad, 0xd1, 0xee, 0xe4, 0xee, 0x9e, 0x2a, 0xff
.byte 0x75, 0xe8, 0x4e, 0xed, 0xef, 0xc4, 0x59, 0x00, 0x2d, 0x80, 0xd1, 0x81, 0x18, 0x08, 0xc6, 0x06
.byte 0x70, 0x50, 0x4b, 0xdd, 0xf0, 0x58, 0x1e, 0x26, 0x27, 0xfd, 0x49, 0x89, 0xa2, 0xf7, 0x03, 0x6e
.byte 0xc5, 0xac, 0x1f, 0xd1, 0x8e, 0x78, 0x00, 0x00, 0x00, 0x96, 0x58, 0xe8, 0xa0, 0xad, 0x20, 0xbf
.byte 0x8a, 0xc4, 0xdb, 0x3e, 0xe0, 0x77, 0xd8, 0x3b, 0x9b, 0xf5, 0x80, 0xe0, 0x1f, 0xa4, 0x6a, 0xc9
.byte 0x5a, 0xf6, 0xc2, 0xba, 0x5a, 0xa4, 0x85, 0x25, 0xfe, 0xa9, 0x65, 0xd1, 0xfe, 0x62, 0xfa, 0xae
.byte 0x27, 0xea, 0xf7, 0x05, 0xf5, 0x10, 0xe2, 0x88, 0xb1, 0x4b, 0x0a, 0x8e, 0x39, 0xf1, 0x56, 0xf6
.byte 0xa8, 0xf2, 0x63, 0xea, 0x0f, 0x76, 0xdd, 0x55, 0xfb, 0xc6, 0x33, 0xe2, 0xa9, 0x8c, 0x81, 0x6d
.byte 0x79, 0x7e, 0x18, 0x0c, 0x70, 0xe1, 0x82, 0xba, 0x1b, 0xf4, 0x97, 0x2f, 0x3c, 0x8e, 0xbd, 0x58
.byte 0x54, 0xae, 0xd8, 0x24, 0x4d, 0x15, 0xd8, 0xca, 0x3c, 0x21, 0xdb, 0x56, 0x30, 0x5d, 0x90, 0x32
.byte 0xbe, 0x31, 0x16, 0x53, 0xee, 0x6c, 0xaf, 0x4d, 0x41, 0xf3, 0x9c, 0xad, 0x22, 0x53, 0x24, 0x55
.byte 0x88, 0x46, 0xb1, 0x5a, 0x82, 0x39, 0x87, 0x5d, 0x95, 0x94, 0x9c, 0x95, 0x2d, 0x32, 0x6f, 0x8a
.byte 0xbb, 0xc9, 0xf5, 0x73, 0xf4, 0x9a, 0xb4, 0x35, 0x87, 0xaa, 0x55, 0x9d, 0xf5, 0xc2, 0x03, 0x80
.byte 0x00, 0x00, 0x00, 0x9f, 0x58, 0xeb, 0x7b, 0xad, 0xd2, 0xe9, 0x85, 0xe8, 0xb6, 0x8c, 0x03, 0x8d
.byte 0x98, 0x53, 0x75, 0x4f, 0x0b, 0x8a, 0x96, 0xfa, 0x22, 0xcf, 0x16, 0xeb, 0x86, 0x7c, 0x84, 0xdc
.byte 0xdb, 0xca, 0xe0, 0xfe, 0x03, 0x55, 0x5d, 0x86, 0xfc, 0xd2, 0x9e, 0x02, 0xac, 0x12, 0xcb, 0xd4
.byte 0x46, 0x43, 0x65, 0x7d, 0x9b, 0xf9, 0x41, 0x6d, 0x05, 0xa1, 0xd6, 0x63, 0x86, 0xe6, 0x02, 0xc7
.byte 0x3a, 0x68, 0x5c, 0x29, 0xf8, 0xe8, 0x0a, 0x2f, 0x43, 0xcd, 0xd8, 0xec, 0x43, 0x65, 0x19, 0xa7
.byte 0x07, 0x83, 0x3c, 0xbb, 0xf1, 0xd1, 0x95, 0xd5, 0xb7, 0x41, 0x99, 0xcf, 0xb4, 0x2e, 0x82, 0x49
.byte 0x01, 0x38, 0xbd, 0x81, 0x15, 0xff, 0x15, 0x0e, 0x5f, 0x9f, 0x2f, 0x5d, 0xdb, 0x6a, 0xba, 0xc0
.byte 0xf9, 0xa1, 0x94, 0x08, 0xf9, 0xed, 0x47, 0x92, 0xa4, 0x6c, 0x4c, 0x2f, 0x46, 0x09, 0x18, 0x15
.byte 0xe6, 0xe5, 0x19, 0x31, 0xd4, 0xbf, 0xc0, 0x22, 0x94, 0x9a, 0xa5, 0x94, 0x3a, 0x6a, 0x56, 0x53
.byte 0xb7, 0xd9, 0xe3, 0x0f, 0xc1, 0xf1, 0x78, 0x8b, 0x20, 0x20, 0xde, 0xe7, 0x04, 0x6e, 0x95, 0x57
.byte 0x10, 0x02, 0xfa, 0x00, 0x00, 0x00, 0xb4, 0x58, 0xeb, 0x11, 0x36, 0x1d, 0x6c, 0x5e, 0x47, 0xd2
.byte 0x36, 0x17, 0x1a, 0x41, 0xac, 0x7e, 0x96, 0x38, 0x19, 0xbc, 0xcd, 0xf2, 0x1e, 0x64, 0x75, 0x86
.byte 0xa6, 0xa0, 0x0a, 0x51, 0xf1, 0xa4, 0xc4, 0x38, 0xba, 0x11, 0x83, 0x21, 0xc2, 0xa1, 0x29, 0xc5
.byte 0xb2, 0x4f, 0x69, 0x1d, 0xbb, 0x32, 0x00, 0x16, 0xdd, 0x2e, 0x1a, 0xae, 0xfe, 0x5f, 0x84, 0xe8
.byte 0x21, 0x24, 0x65, 0xa2, 0x7e, 0xf9, 0x6d, 0x93, 0xd6, 0xef, 0x76, 0xf0, 0x11, 0xce, 0xf5, 0xde
.byte 0x8a, 0x85, 0x25, 0x4b, 0xa4, 0x1b, 0x77, 0xaa, 0x9f, 0xb1, 0x5a, 0xdb, 0xdf, 0x6a, 0xdf, 0xf8
.byte 0x64, 0x80, 0x4d, 0x34, 0xcf, 0x07, 0xea, 0xc1, 0x4e, 0x97, 0x87, 0x9c, 0x79, 0x9b, 0xdf, 0x02
.byte 0xb4, 0x55, 0xf4, 0xf2, 0x39, 0xcd, 0x58, 0x36, 0xe7, 0xdb, 0x34, 0x63, 0x1d, 0x56, 0x70, 0x5f
.byte 0xc1, 0xc8, 0x3b, 0x3a, 0x38, 0xf6, 0x27, 0x35, 0x1f, 0x0e, 0x14, 0xf0, 0xce, 0x3c, 0xf4, 0x15
.byte 0x85, 0x31, 0x99, 0x9e, 0xbd, 0xf5, 0xf7, 0xdd, 0xcd, 0xf2, 0x84, 0x6e, 0xe9, 0xa3, 0x27, 0x59
.byte 0x73, 0xd6, 0x12, 0xb1, 0xc6, 0x4d, 0x34, 0x41, 0x04, 0x5b, 0xe7, 0x12, 0xfb, 0x94, 0xfe, 0xde
.byte 0x01, 0xf2, 0x22, 0x06, 0x83, 0x7d, 0x69, 0x99, 0x0b, 0x09, 0x0c, 0x00, 0x00, 0x00, 0xb6, 0x58
.byte 0xe8, 0x42, 0xa6, 0xb9, 0xde, 0xbf, 0x45, 0xe0, 0xc3, 0x10, 0xe3, 0xef, 0xc9, 0x6b, 0x6a, 0xf1
.byte 0xcd, 0x15, 0x4c, 0xc2, 0x6e, 0x65, 0x90, 0xe9, 0x6d, 0x10, 0x20, 0x01, 0x54, 0xd1, 0x20, 0xd6
.byte 0x73, 0x3a, 0x02, 0x46, 0xb4, 0x29, 0x1a, 0xe2, 0xc0, 0x6c, 0x2b, 0x3d, 0x21, 0xeb, 0x80, 0xd2
.byte 0x3b, 0xba, 0x3d, 0x75, 0x04, 0xf4, 0x12, 0x55, 0x2d, 0x17, 0x8f, 0x32, 0xf8, 0x6e, 0xc9, 0x1f
.byte 0x08, 0x44, 0x19, 0xa3, 0xcf, 0x75, 0xce, 0xe4, 0xea, 0x45, 0x87, 0xba, 0x8c, 0x86, 0x70, 0xb0
.byte 0x99, 0x2b, 0x80, 0x8a, 0x84, 0x6a, 0x1c, 0x65, 0x04, 0xe6, 0xee, 0xca, 0x09, 0x30, 0xe3, 0x78
.byte 0x00, 0xde, 0x5b, 0x81, 0x8a, 0x72, 0xd9, 0x3c, 0x15, 0x29, 0xed, 0x77, 0x3d, 0x43, 0xe1, 0x36
.byte 0xc7, 0xf9, 0xf1, 0x8a, 0x80, 0xec, 0x3b, 0xfd, 0xd5, 0xfd, 0xc8, 0x67, 0x9c, 0xd9, 0xf6, 0x05
.byte 0x58, 0x19, 0x30, 0x99, 0x32, 0x96, 0x1f, 0x07, 0x53, 0x24, 0x62, 0xbe, 0x55, 0x6f, 0x06, 0x04
.byte 0xe8, 0x5b, 0x9c, 0x8f, 0x22, 0x7b, 0xb3, 0xfb, 0xbb, 0xcd, 0x04, 0x7c, 0x73, 0xdc, 0xfa, 0x0d
.byte 0x73, 0xf8, 0x75, 0x61, 0xff, 0x67, 0xa0, 0x09, 0x21, 0x60, 0xaf, 0x41, 0x07, 0x59, 0x9f, 0x05
.byte 0x9d, 0xd4, 0x00, 0x09, 0xe0, 0x00, 0x00, 0x00, 0xa1, 0x58, 0xe7, 0xc9, 0xee, 0xdf, 0x4a, 0xfc
.byte 0x53, 0xa4, 0xb4, 0x77, 0x82, 0x6a, 0xf5, 0xff, 0xca, 0xbc, 0x54, 0x1e, 0xe6, 0xfe, 0x47, 0x31
.byte 0x84, 0x2e, 0x1c, 0xbd, 0xfd, 0xd9, 0xe2, 0x1b, 0x8e, 0x77, 0xf6, 0x6c, 0x49, 0xf0, 0xc7, 0x94
.byte 0x2f, 0x7b, 0x4d, 0x54, 0x25, 0x7d, 0x84, 0x20, 0x90, 0x06, 0x81, 0xc0, 0xdc, 0x11, 0x34, 0xb7
.byte 0xfd, 0x34, 0x07, 0xba, 0x55, 0x2e, 0x9b, 0x58, 0x89, 0xfe, 0x8b, 0xd3, 0x8b, 0x35, 0x85, 0xaa
.byte 0x49, 0xf7, 0xbd, 0xbf, 0x13, 0xc5, 0x90, 0x5c, 0x48, 0x46, 0x3b, 0xd9, 0xdf, 0x92, 0x55, 0x8c
.byte 0x0f, 0x8b, 0xc2, 0x2b, 0x1b, 0xa3, 0xba, 0x40, 0x23, 0xaf, 0x68, 0x9a, 0xfb, 0xbf, 0x15, 0xba
.byte 0x48, 0xfb, 0xa8, 0x6b, 0x9c, 0x88, 0x4e, 0xf3, 0xe4, 0x78, 0x08, 0x06, 0x4d, 0x8c, 0x2c, 0xca
.byte 0xef, 0x0a, 0x03, 0xc1, 0xf8, 0x8a, 0xe1, 0x81, 0x18, 0x25, 0xaf, 0x10, 0xbe, 0xfb, 0xee, 0x12
.byte 0x60, 0x92, 0x9e, 0xa5, 0x07, 0xfe, 0x61, 0xf1, 0xb6, 0x30, 0x84, 0x7e, 0xcb, 0x18, 0x6d, 0xe2
.byte 0x88, 0xf9, 0x56, 0x46, 0x97, 0x8a, 0x58, 0xfc, 0x57, 0x30, 0x00, 0x00, 0x00, 0x93, 0x58, 0xe7
.byte 0x01, 0xa5, 0xcc, 0x05, 0x03, 0x36, 0x86, 0xf3, 0xee, 0x4f, 0x21, 0x18, 0x5b, 0x4e, 0xf2, 0xaf
.byte 0x7f, 0xe0, 0x3d, 0xf3, 0xeb, 0x79, 0x9f, 0x91, 0x71, 0x64, 0x48, 0x8c, 0x38, 0xdf, 0x37, 0x0c
.byte 0xc4, 0xf9, 0x69, 0x68, 0x30, 0xdb, 0xcd, 0x25, 0x9c, 0xef, 0x45, 0x3f, 0x08, 0x7e, 0x1a, 0x32
.byte 0x36, 0xa0, 0x20, 0xd1, 0x6b, 0xad, 0x55, 0xce, 0xad, 0xbf, 0xc9, 0x41, 0x41, 0x7d, 0x4d, 0x8c
.byte 0x31, 0x8e, 0x1f, 0x44, 0x68, 0x22, 0x20, 0x0a, 0xb0, 0xb5, 0xc0, 0x19, 0xfe, 0xd6, 0x52, 0x3d
.byte 0xb5, 0x29, 0xcc, 0x17, 0xc9, 0x15, 0x54, 0xcf, 0x5d, 0x3b, 0x4b, 0x0b, 0xf7, 0xcc, 0x4b, 0x03
.byte 0xed, 0x59, 0x00, 0xdd, 0x8e, 0x3c, 0xde, 0x02, 0xa0, 0x0e, 0xf3, 0xa8, 0x5d, 0x49, 0x70, 0x1f
.byte 0xc1, 0xb3, 0x43, 0x7c, 0xc9, 0x39, 0xbf, 0x13, 0x7c, 0x88, 0xef, 0x4a, 0x2c, 0x7d, 0x30, 0xf0
.byte 0x82, 0x62, 0x6d, 0xec, 0x14, 0x28, 0x24, 0xe4, 0x4e, 0xa3, 0x26, 0x60, 0x9a, 0xe6, 0x69, 0x46
.byte 0x9c, 0x00, 0x00, 0x00, 0x89, 0x58, 0xe0, 0x28, 0xfd, 0xae, 0xcc, 0xe6, 0xae, 0xcf, 0x84, 0x09
.byte 0xca, 0x07, 0xee, 0x4d, 0x85, 0x9b, 0x4b, 0xea, 0xf4, 0xa5, 0x5d, 0x68, 0x7e, 0x41, 0xd2, 0x6b
.byte 0x98, 0x41, 0x94, 0xf7, 0x16, 0x57, 0x04, 0x7d, 0x1f, 0x59, 0x46, 0x5e, 0x07, 0xb3, 0x78, 0xfe
.byte 0x4a, 0x32, 0xee, 0xdd, 0x6c, 0x5c, 0xfc, 0x09, 0x12, 0x75, 0x75, 0x78, 0xb0, 0x49, 0xdc, 0xf1
.byte 0xe6, 0x03, 0x7f, 0x1c, 0x64, 0xdd, 0xb8, 0x1f, 0x79, 0xe1, 0x32, 0x16, 0xe5, 0x4f, 0x29, 0x62
.byte 0x6b, 0x90, 0xc8, 0xf4, 0xff, 0x3a, 0x5e, 0xdb, 0x8c, 0xc1, 0xde, 0x6c, 0x6e, 0xb0, 0x62, 0x38
.byte 0xd9, 0xb1, 0xd2, 0x50, 0x33, 0x8c, 0x2b, 0x8b, 0x91, 0xc6, 0x6c, 0xdb, 0xd1, 0xcc, 0xa6, 0x3c
.byte 0x55, 0xf1, 0x87, 0xb3, 0xee, 0x74, 0xe5, 0xcb, 0x9c, 0xe5, 0x1e, 0xd9, 0x41, 0xd8, 0x90, 0xde
.byte 0xa0, 0xe0, 0x25, 0x7d, 0x4d, 0x76, 0xbd, 0xb0, 0x07, 0xa1, 0x9c, 0x3b, 0x41, 0x10, 0x00, 0x00
.byte 0x00, 0x71, 0x58, 0xc0, 0x27, 0x55, 0x50, 0xb0, 0x83, 0x53, 0x57, 0x70, 0xdd, 0xc2, 0x7b, 0xf6
.byte 0x80, 0xa9, 0x1b, 0x51, 0xa3, 0xb3, 0x70, 0xe7, 0xbb, 0x1d, 0xc3, 0xe5, 0xfc, 0x45, 0xff, 0x51
.byte 0x5c, 0x81, 0x39, 0x6a, 0x57, 0x67, 0xc1, 0x8f, 0xcb, 0xd3, 0xb0, 0x01, 0xd8, 0x54, 0xfc, 0x95
.byte 0x9c, 0x56, 0x1d, 0xa9, 0x9e, 0x1a, 0x50, 0xf1, 0x8c, 0x14, 0x82, 0x48, 0x54, 0xb1, 0x6c, 0xda
.byte 0x31, 0x6b, 0xa8, 0x0b, 0x72, 0x9e, 0xde, 0x2d, 0xf5, 0xfe, 0xe6, 0x14, 0xff, 0x1a, 0x38, 0x20
.byte 0x6c, 0xf7, 0x62, 0xaa, 0x20, 0x25, 0x66, 0x52, 0x50, 0x61, 0x42, 0x4c, 0x63, 0xc9, 0x61, 0xac
.byte 0xc5, 0x64, 0x3a, 0x90, 0x5f, 0xb3, 0xe7, 0x10, 0xeb, 0x85, 0xf2, 0xe1, 0x2b, 0x66, 0x74, 0x41
.byte 0xae, 0xdf, 0xb0, 0x00, 0x00, 0x00, 0x52, 0x58, 0x00, 0x99, 0xec, 0x63, 0x14, 0x3d, 0xff, 0x74
.byte 0x75, 0x11, 0xcc, 0x9c, 0x6a, 0x63, 0xf0, 0x59, 0x53, 0x36, 0xc7, 0xf9, 0xfc, 0x1a, 0x6e, 0x76
.byte 0xd4, 0xc6, 0x40, 0xf5, 0x6d, 0xc4, 0x5a, 0x23, 0xf9, 0xd7, 0xaf, 0x77, 0x43, 0xe9, 0x6a, 0xef
.byte 0xf3, 0xf6, 0x92, 0x55, 0xfd, 0xfe, 0x38, 0x68, 0x16, 0x09, 0x55, 0x45, 0x05, 0x71, 0x8b, 0xa4
.byte 0xa5, 0xc3, 0xed, 0x50, 0xde, 0x98, 0x2d, 0x11, 0x5a, 0xea, 0xb8, 0x36, 0xaa, 0x52, 0xf0, 0x8e
.byte 0x9b, 0xe6, 0x1f, 0x2e, 0x4e, 0x77, 0xe5, 0xb9, 0xe0, 0x00, 0x00, 0x00, 0x48, 0x58, 0x00, 0x98
.byte 0x0c, 0x14, 0xbe, 0x2d, 0xa9, 0xf2, 0x8e, 0xca, 0xf9, 0xf8, 0x50, 0x57, 0x3c, 0x3c, 0x77, 0x0e
.byte 0x15, 0xf2, 0xd4, 0xed, 0x91, 0x1d, 0xc6, 0xd7, 0x81, 0x20, 0x14, 0x3f, 0xbb, 0xdc, 0xdc, 0x2e
.byte 0x09, 0x45, 0xf1, 0x09, 0x0b, 0xa9, 0x75, 0x2e, 0x60, 0x34, 0x6f, 0xff, 0x17, 0xb0, 0xb2, 0x92
.byte 0xdf, 0x06, 0xc5, 0x93, 0x4c, 0x23, 0x02, 0xa3, 0x53, 0x5c, 0xca, 0x89, 0x99, 0xb1, 0x32, 0x88
.byte 0x75, 0xad, 0x0b, 0xbe, 0x20, 0x00, 0x00, 0x00, 0x50, 0x58, 0x04, 0x8a, 0x8a, 0x85, 0x09, 0x1a
.byte 0xad, 0x04, 0xf5, 0xd9, 0x42, 0xa1, 0x1f, 0xb3, 0xaa, 0x02, 0xc2, 0x42, 0x86, 0xbc, 0xc4, 0x28
.byte 0x4b, 0x5a, 0x30, 0xef, 0xca, 0x3c, 0xa9, 0x70, 0x7e, 0x0b, 0xb5, 0xbe, 0xd4, 0xc1, 0x5a, 0x27
.byte 0x4e, 0x21, 0xa3, 0xca, 0xbc, 0xba, 0xa7, 0xa5, 0x9c, 0x56, 0x09, 0x82, 0xbd, 0x7a, 0xb3, 0x25
.byte 0xfb, 0xcc, 0x6b, 0x82, 0x05, 0x6d, 0xd6, 0xe4, 0x12, 0xb3, 0x0e, 0x10, 0x5f, 0x0d, 0x2d, 0xd1
.byte 0x31, 0x58, 0xff, 0xbd, 0x3e, 0x55, 0x6a, 0x6b, 0x3d, 0x00, 0x00, 0x00, 0x4c, 0x58, 0x05, 0x53
.byte 0xc2, 0x84, 0x48, 0xa9, 0x03, 0x27, 0x6e, 0xff, 0x36, 0x5b, 0xa0, 0xd9, 0x36, 0xab, 0x58, 0xef
.byte 0x2d, 0xbe, 0xa4, 0xc4, 0xc4, 0x12, 0xcf, 0x05, 0x04, 0x29, 0x56, 0xb2, 0x69, 0xed, 0x29, 0xdc
.byte 0x36, 0xb6, 0x53, 0x8c, 0x31, 0x51, 0xef, 0xc5, 0xb2, 0xef, 0x70, 0xf8, 0xa9, 0x7e, 0x2f, 0x61
.byte 0xdd, 0x6a, 0x0f, 0x04, 0x7d, 0x62, 0x84, 0xf4, 0xd2, 0xfc, 0xd2, 0x55, 0x53, 0xce, 0x82, 0xa8
.byte 0xdd, 0xb7, 0x29, 0xbd, 0x86, 0x7e, 0x7b, 0x90, 0xe0, 0x00, 0x00, 0x00, 0x54, 0x58, 0x04, 0xce
.byte 0xee, 0xa4, 0x3c, 0xb3, 0xa9, 0xd9, 0x06, 0xc4, 0x16, 0xfa, 0xdb, 0x82, 0x98, 0x07, 0x56, 0x1b
.byte 0x74, 0x7c, 0x42, 0x5f, 0xae, 0xcd, 0xb5, 0xe9, 0x96, 0xdf, 0xae, 0x2c, 0x47, 0x99, 0x27, 0xc8
.byte 0xb8, 0x38, 0xe4, 0x4d, 0x1b, 0x5c, 0x85, 0x8c, 0x10, 0x04, 0x6b, 0xe4, 0x8e, 0xd3, 0x67, 0xe2
.byte 0x5f, 0xb9, 0x4e, 0xc6, 0x48, 0xbe, 0xdf, 0xee, 0xd1, 0xbc, 0xdb, 0xff, 0x36, 0xf5, 0xdc, 0x6f
.byte 0x95, 0xb6, 0x0d, 0x73, 0x5c, 0x78, 0x99, 0x1f, 0x95, 0x15, 0x0f, 0xad, 0x62, 0xa2, 0x5c, 0x0f
.byte 0x80, 0x00, 0x00, 0x00, 0x4c, 0x58, 0x04, 0x94, 0x2d, 0xe3, 0xba, 0xf5, 0x1c, 0xfc, 0x83, 0x3c
.byte 0x3f, 0xa7, 0x1c, 0x9c, 0x74, 0x8b, 0x11, 0xe7, 0x4c, 0x60, 0x9d, 0xfa, 0x3b, 0xee, 0x4c, 0x88
.byte 0x45, 0xd5, 0xe4, 0x2c, 0x23, 0xc6, 0x21, 0x27, 0x45, 0x96, 0x14, 0xb4, 0xaa, 0x5e, 0xa7, 0xc1
.byte 0x3b, 0xc0, 0xde, 0x22, 0x53, 0x8e, 0x8d, 0xa1, 0x09, 0xdd, 0x29, 0x80, 0xe6, 0x0a, 0x0f, 0xc0
.byte 0xc1, 0x57, 0xd4, 0x4a, 0x2e, 0xce, 0xae, 0x51, 0x95, 0x4d, 0xec, 0x0f, 0xaf, 0xa9, 0x9f, 0x5c
.byte 0xb2, 0x00, 0x00, 0x00, 0x73, 0x58, 0x64, 0x92, 0x9d, 0xfa, 0x6c, 0x2b, 0xd6, 0x29, 0x8c, 0x2f
.byte 0xce, 0xd0, 0x5c, 0x8d, 0x29, 0xb9, 0xc6, 0x7f, 0x29, 0xd8, 0xce, 0x91, 0x7a, 0xb0, 0x26, 0x3f
.byte 0xab, 0xa0, 0x30, 0x60, 0xef, 0x2b, 0x2f, 0xc5, 0x1f, 0x54, 0x0f, 0x7e, 0xfb, 0xc4, 0x19, 0x0c
.byte 0x0b, 0x35, 0x68, 0x2e, 0x39, 0xee, 0xb6, 0x3d, 0x79, 0x3a, 0xc1, 0x14, 0xd8, 0x72, 0xc9, 0x71
.byte 0x51, 0x30, 0x5b, 0x47, 0xda, 0xf6, 0x00, 0xd3, 0x09, 0xa7, 0xd3, 0x5f, 0x8e, 0xdd, 0xe8, 0x24
.byte 0x7a, 0xc6, 0x36, 0xb2, 0x8d, 0xbd, 0x68, 0x3c, 0x7f, 0xbf, 0x23, 0x96, 0x64, 0x08, 0x34, 0x85
.byte 0x3e, 0xff, 0x21, 0x48, 0xf4, 0xcb, 0x6b, 0x58, 0xdf, 0xa4, 0x17, 0x02, 0x42, 0xce, 0xac, 0xb7
.byte 0xb8, 0xcd, 0x34, 0xc9, 0x24, 0xe3, 0x98, 0x1a, 0x00, 0x00, 0x00, 0x5c, 0x58, 0x05, 0x50, 0xab
.byte 0xe2, 0x8a, 0xe4, 0x1a, 0xdb, 0xf4, 0x6b, 0x2a, 0x1b, 0x67, 0xe0, 0x55, 0xa7, 0x4f, 0x8f, 0x82
.byte 0x50, 0x42, 0x15, 0x7d, 0x62, 0x2d, 0x83, 0x3a, 0xcc, 0x35, 0x83, 0x9d, 0xe0, 0x19, 0x28, 0x05
.byte 0x0d, 0x90, 0x8e, 0x9e, 0x61, 0xdf, 0xa3, 0x33, 0xcb, 0x8a, 0x05, 0xe1, 0xff, 0x08, 0x55, 0x13
.byte 0xc1, 0xe2, 0x72, 0x21, 0xb7, 0x59, 0xb8, 0x43, 0xd3, 0x41, 0x8a, 0xe3, 0xf9, 0xbc, 0x18, 0xca
.byte 0x95, 0x72, 0x35, 0x31, 0xfd, 0xcf, 0xea, 0xdd, 0x8d, 0xef, 0x7d, 0x11, 0x9b, 0xb8, 0xec, 0x33
.byte 0xb3, 0x8b, 0xb8, 0x52, 0xa1, 0xec, 0x6b, 0x48

.global _binary_success_p3_end
_binary_success_p3_end: /* for objcopy compatibility */


.global success_p3_length
success_p3_length:
.long 2040
