/* * Data converted from /home/<USER>/bysx/xiaozhi-esp32/main/assets/zh-CN/6.p3
 */
.data
#if !defined (__APPLE__) && !defined (__linux__)
.section .rodata.embedded
#endif

.global _6_p3
_6_p3:

.global _binary_6_p3_start
_binary_6_p3_start: /* for objcopy compatibility */
.byte 0x00, 0x00, 0x00, 0x48, 0x58, 0x00, 0x5e, 0x85, 0x6e, 0x28, 0xf8, 0xe9, 0xcc, 0x80, 0x43, 0xfa
.byte 0x52, 0x9e, 0x3b, 0x3b, 0xc8, 0x25, 0x16, 0xce, 0xfb, 0x13, 0x0a, 0x22, 0xb9, 0xd4, 0xa3, 0xcc
.byte 0x28, 0xfd, 0x23, 0x64, 0x33, 0x74, 0x13, 0xc1, 0x1f, 0xd8, 0x8e, 0xc7, 0x78, 0x6b, 0xf9, 0x58
.byte 0x46, 0x3a, 0x26, 0xb1, 0x3e, 0x78, 0x74, 0x83, 0x2e, 0x39, 0xd9, 0x6f, 0xf4, 0x4b, 0x2b, 0x24
.byte 0x18, 0xf3, 0x49, 0x1a, 0xf5, 0x5e, 0xeb, 0x81, 0x96, 0x71, 0xb8, 0x14, 0x00, 0x00, 0x00, 0x56
.byte 0x58, 0x09, 0xe3, 0xdf, 0x40, 0xa4, 0xbb, 0x06, 0x03, 0xfd, 0x08, 0x1c, 0x0e, 0xc2, 0x74, 0x26
.byte 0x78, 0x57, 0x94, 0x5c, 0xe7, 0x4d, 0xe8, 0x6f, 0x55, 0xbd, 0xa3, 0x58, 0x48, 0x94, 0x5a, 0x0c
.byte 0x76, 0x22, 0x37, 0xbd, 0x42, 0x31, 0xd3, 0x91, 0x8d, 0x7b, 0xbe, 0xa4, 0xb7, 0x12, 0x1a, 0xf7
.byte 0x26, 0xdd, 0x74, 0x3e, 0x3a, 0x90, 0x7e, 0xf4, 0x55, 0x06, 0xbf, 0x1d, 0x12, 0xe0, 0xa7, 0xa9
.byte 0xc9, 0x2e, 0x4f, 0x7e, 0xc9, 0x45, 0x22, 0x04, 0x26, 0xcc, 0x14, 0x9a, 0x5f, 0x52, 0xb0, 0x48
.byte 0x1c, 0x8b, 0xbc, 0xf0, 0x5a, 0x76, 0x00, 0x00, 0x00, 0x85, 0x58, 0xe2, 0x4b, 0xd3, 0x2f, 0x07
.byte 0x27, 0x5a, 0xff, 0xb3, 0x51, 0xc3, 0x52, 0x22, 0x54, 0xc8, 0xe8, 0x7c, 0x2a, 0xfe, 0xfd, 0x99
.byte 0xfc, 0xd0, 0x57, 0x52, 0x42, 0xba, 0x3c, 0x5b, 0x51, 0xde, 0x91, 0xb5, 0x34, 0xeb, 0xd7, 0xdf
.byte 0xb7, 0xef, 0x57, 0x3c, 0x77, 0xc9, 0x71, 0xf0, 0x8d, 0xe3, 0x51, 0xce, 0x1a, 0x2a, 0x72, 0x43
.byte 0x2d, 0x3f, 0xb1, 0xcd, 0xa7, 0xf5, 0xa8, 0x44, 0xc8, 0xf9, 0x61, 0x09, 0xae, 0x91, 0x24, 0xd8
.byte 0xc0, 0xe1, 0x77, 0x8e, 0xd6, 0x75, 0x8a, 0xc4, 0x55, 0xf8, 0x12, 0x52, 0x33, 0x7a, 0x4f, 0x8e
.byte 0x12, 0x15, 0x90, 0x2f, 0x50, 0xc2, 0x59, 0x08, 0x7f, 0xc2, 0xf7, 0xb2, 0x74, 0xa6, 0xbb, 0xba
.byte 0xdc, 0x4b, 0x0d, 0xa0, 0x16, 0x12, 0x39, 0x15, 0xd3, 0x23, 0xcb, 0x13, 0x36, 0x06, 0xff, 0xe9
.byte 0xab, 0xf4, 0x6b, 0x7b, 0x19, 0xd9, 0x7c, 0x59, 0xfe, 0xd3, 0xe7, 0x3c, 0x7e, 0xc6, 0x49, 0x00
.byte 0x00, 0x00, 0x79, 0x58, 0xec, 0x2b, 0xc7, 0x5e, 0x87, 0x06, 0xab, 0x83, 0xff, 0x53, 0x5a, 0x12
.byte 0xfc, 0x43, 0x41, 0xe2, 0x8f, 0x4b, 0x5e, 0xfb, 0xe3, 0x86, 0x3e, 0xf5, 0xda, 0x4a, 0x90, 0xae
.byte 0xd8, 0x14, 0x71, 0x7f, 0x91, 0x9f, 0x99, 0x7e, 0x7f, 0x7e, 0x9b, 0xf8, 0xa3, 0x72, 0x99, 0x3b
.byte 0x11, 0x20, 0xcf, 0x06, 0x4c, 0xbe, 0x13, 0xd9, 0xa9, 0xed, 0xf5, 0xa5, 0xfd, 0xb6, 0x73, 0x7a
.byte 0x38, 0xd8, 0x68, 0x7a, 0xbc, 0x94, 0xf6, 0xe3, 0xfc, 0xfa, 0x81, 0xe2, 0x13, 0xf5, 0xf7, 0xe1
.byte 0xed, 0x6b, 0x28, 0x62, 0xe2, 0x00, 0x01, 0x6a, 0xa1, 0x56, 0x8f, 0x41, 0x4e, 0x0b, 0xe3, 0x98
.byte 0xdc, 0x25, 0x4e, 0x93, 0xba, 0x85, 0x9f, 0x69, 0xdf, 0x8d, 0xda, 0xe3, 0x44, 0x5d, 0xb8, 0xa1
.byte 0x14, 0x88, 0x51, 0x83, 0x5e, 0x6a, 0xc9, 0xf6, 0x2b, 0x96, 0x46, 0x20, 0x00, 0x00, 0x00, 0x72
.byte 0x58, 0xee, 0x2d, 0xb2, 0x2c, 0x1c, 0x89, 0x5c, 0xe4, 0x3e, 0x2e, 0x22, 0xb2, 0x57, 0xb5, 0x45
.byte 0x27, 0xe5, 0x55, 0x6a, 0xd2, 0x55, 0x19, 0x83, 0x47, 0x6f, 0xcd, 0x3f, 0xc0, 0xc2, 0xbf, 0xf2
.byte 0xbe, 0xb0, 0x1e, 0xa6, 0xfc, 0x73, 0x6a, 0x41, 0x6c, 0x87, 0x87, 0x9a, 0xe2, 0x75, 0x47, 0x4b
.byte 0x05, 0x41, 0x22, 0x84, 0xa4, 0xb9, 0x8d, 0xb4, 0x60, 0xfe, 0x7c, 0x82, 0xe3, 0x28, 0x2b, 0x3e
.byte 0xa1, 0xf2, 0x3c, 0xf3, 0x41, 0xd4, 0xa1, 0xb8, 0x14, 0xe0, 0xe8, 0x86, 0xe6, 0xf4, 0x29, 0xa7
.byte 0xaa, 0x02, 0x3d, 0x03, 0x4f, 0x7a, 0x10, 0x47, 0x24, 0x65, 0x13, 0x76, 0x7e, 0xc8, 0x6f, 0x4a
.byte 0x1d, 0xd0, 0x94, 0x94, 0xcf, 0x14, 0xcf, 0x39, 0xb2, 0x9a, 0x90, 0xcf, 0x77, 0xf3, 0x69, 0x3f
.byte 0x72, 0xa0, 0x00, 0x00, 0x00, 0x81, 0x58, 0xed, 0x87, 0xac, 0x80, 0x57, 0x6e, 0x39, 0x27, 0xe6
.byte 0xce, 0xfe, 0x30, 0x47, 0x32, 0x16, 0xe0, 0x8e, 0x71, 0xef, 0x5a, 0xfc, 0xd4, 0x58, 0xf8, 0x36
.byte 0xb7, 0x3b, 0xfa, 0xd2, 0x3c, 0x48, 0x13, 0x73, 0x2c, 0xaf, 0x0b, 0x02, 0xd1, 0xd2, 0xcf, 0x63
.byte 0x2c, 0x17, 0x13, 0x0d, 0x72, 0x20, 0xc6, 0x28, 0x7a, 0x17, 0xb3, 0xd9, 0x19, 0x6a, 0xc6, 0x5b
.byte 0x95, 0x27, 0xf3, 0x3f, 0xb7, 0x64, 0x66, 0x26, 0xf1, 0xc5, 0xd3, 0x76, 0xe6, 0x5b, 0x7f, 0x76
.byte 0x5d, 0x59, 0x8d, 0x21, 0x4e, 0x02, 0x34, 0xac, 0xde, 0x28, 0x3a, 0x45, 0x8e, 0xb7, 0x2d, 0x30
.byte 0x20, 0x8a, 0xea, 0x42, 0xca, 0x2a, 0xa4, 0xf2, 0xa4, 0xd9, 0xd0, 0x6f, 0xe9, 0x12, 0x3c, 0x97
.byte 0x13, 0x03, 0x2d, 0x87, 0x50, 0xb7, 0x5c, 0xbd, 0x1b, 0x36, 0x94, 0x06, 0x70, 0x19, 0x2f, 0x6c
.byte 0xa4, 0xcf, 0x56, 0x9b, 0x72, 0x7b, 0xf0, 0x00, 0x00, 0x00, 0x7b, 0x58, 0xed, 0xb7, 0x56, 0xf7
.byte 0xe8, 0xf0, 0xec, 0x7e, 0xf1, 0xa5, 0x79, 0x8d, 0xf6, 0xe2, 0x08, 0xef, 0xb4, 0xd5, 0x35, 0xf6
.byte 0x10, 0x64, 0x7a, 0x5d, 0x22, 0x83, 0x1b, 0x7f, 0xce, 0x23, 0x81, 0x72, 0xec, 0xfc, 0x81, 0x94
.byte 0xe5, 0xdf, 0x20, 0xd1, 0xb0, 0x07, 0x40, 0xa2, 0xaf, 0x5a, 0x6e, 0x45, 0xc2, 0x5d, 0x30, 0x0a
.byte 0x61, 0xe6, 0x34, 0xe3, 0xa1, 0xd7, 0x1c, 0x2d, 0xed, 0x4b, 0xda, 0xf9, 0xb2, 0xa8, 0x73, 0xb5
.byte 0xf7, 0x4d, 0x85, 0xda, 0xd7, 0xee, 0x59, 0x39, 0x22, 0x10, 0x41, 0xf9, 0xdc, 0x2e, 0x2b, 0xd1
.byte 0x30, 0x58, 0x35, 0x81, 0x75, 0x69, 0x6e, 0x96, 0x1c, 0xc6, 0x83, 0x8d, 0x2b, 0xe7, 0x5f, 0xa8
.byte 0xf1, 0x5e, 0xbf, 0xfb, 0x9b, 0x6d, 0x11, 0x0e, 0xdd, 0x14, 0x7a, 0x54, 0x34, 0xed, 0x4b, 0x1e
.byte 0x04, 0x8a, 0xf0, 0x04, 0x0f, 0xc3, 0x00, 0x00, 0x00, 0x6c, 0x58, 0xec, 0x58, 0x0e, 0x0c, 0x3e
.byte 0xb1, 0x57, 0x59, 0x40, 0xc3, 0xe0, 0xfe, 0x43, 0x4f, 0xb5, 0x89, 0xa0, 0x1e, 0x25, 0xa8, 0xd0
.byte 0x6a, 0xce, 0xde, 0x3f, 0x2a, 0x89, 0x45, 0xb4, 0x42, 0x48, 0x5f, 0x8a, 0x8d, 0x84, 0x3e, 0x9b
.byte 0x89, 0x3b, 0xe9, 0x7d, 0x3c, 0x31, 0x2b, 0xa0, 0x1d, 0x28, 0x06, 0x1d, 0x0f, 0x6a, 0x1a, 0x4d
.byte 0x70, 0x90, 0x48, 0x15, 0x61, 0xa6, 0x18, 0x43, 0x97, 0x1f, 0x6c, 0x9d, 0x53, 0x3c, 0x4d, 0x5d
.byte 0x09, 0x39, 0x35, 0xd7, 0xc5, 0xe4, 0x78, 0x2d, 0x2c, 0x61, 0x16, 0xf2, 0x66, 0x6c, 0x09, 0x36
.byte 0x74, 0x82, 0xd4, 0x39, 0x6a, 0x65, 0xfc, 0xd8, 0x1d, 0x99, 0x98, 0x38, 0x58, 0xb3, 0x7d, 0x28
.byte 0xe6, 0x73, 0xd2, 0x26, 0x4e, 0xe4, 0x00, 0x00, 0x00, 0x61, 0x58, 0x0a, 0x36, 0x4e, 0x0a, 0x8c
.byte 0x90, 0xa2, 0xd3, 0x2b, 0xf5, 0x3e, 0xdd, 0xa4, 0x16, 0xb3, 0xfb, 0x43, 0xac, 0x1c, 0x8b, 0xe8
.byte 0xda, 0x44, 0xb2, 0x96, 0x6e, 0xd7, 0xb1, 0x50, 0xda, 0xb6, 0xb5, 0xc0, 0x75, 0x0e, 0xc1, 0x64
.byte 0xa6, 0x89, 0x15, 0x55, 0x47, 0x6b, 0x43, 0x0a, 0x34, 0xde, 0x01, 0x5a, 0x9a, 0x4e, 0x18, 0xbf
.byte 0xed, 0x6d, 0x9c, 0x48, 0x33, 0xf3, 0x54, 0x86, 0xa1, 0xff, 0x97, 0x3c, 0xd7, 0x29, 0x9b, 0xf9
.byte 0x52, 0x67, 0xbe, 0xaa, 0x66, 0x5a, 0x08, 0x09, 0x5b, 0x2e, 0x0f, 0x36, 0x19, 0x07, 0xaf, 0x92
.byte 0xdc, 0x2a, 0xb6, 0x53, 0xf4, 0x21, 0x0d, 0xd1, 0xd5, 0x52, 0xa8, 0x00, 0x00, 0x00, 0x50, 0x58
.byte 0x09, 0xe3, 0x36, 0x60, 0xbf, 0xd6, 0x06, 0xed, 0x0b, 0x86, 0xfe, 0x59, 0xb2, 0xd3, 0xca, 0x8e
.byte 0x06, 0xa5, 0x91, 0x37, 0x7e, 0x24, 0xf5, 0x0c, 0xf2, 0x55, 0xb4, 0x02, 0x1e, 0xe4, 0x7c, 0xe8
.byte 0xce, 0xe8, 0xf5, 0x28, 0x03, 0x39, 0x81, 0x0f, 0x99, 0x1c, 0x2a, 0xf7, 0xd1, 0x3e, 0x5c, 0xf9
.byte 0xbf, 0xe3, 0xc7, 0xeb, 0xea, 0xc9, 0x8d, 0xec, 0xd5, 0xb6, 0x06, 0x29, 0x51, 0xbe, 0x9a, 0xa3
.byte 0x02, 0x3b, 0x71, 0x22, 0x80, 0xb7, 0x3d, 0x50, 0x81, 0x71, 0x4b, 0xbd, 0x16, 0x24, 0x8c, 0x00
.byte 0x00, 0x00, 0x5d, 0x58, 0x09, 0xe4, 0x17, 0x22, 0xf2, 0xdf, 0x61, 0x52, 0x1f, 0x35, 0x37, 0xf0
.byte 0x77, 0xa4, 0x54, 0xa7, 0xf4, 0xd5, 0x2b, 0xab, 0xde, 0x97, 0xf5, 0xcb, 0x64, 0x52, 0xbc, 0x6d
.byte 0xf2, 0xcc, 0x46, 0xd8, 0xf9, 0xa7, 0x77, 0xbf, 0xdb, 0x09, 0xc3, 0x00, 0xaf, 0xb2, 0xd8, 0xae
.byte 0xcf, 0x18, 0x53, 0x5c, 0x25, 0xb8, 0x08, 0x0e, 0x26, 0x28, 0xac, 0xab, 0x92, 0x49, 0x95, 0x9c
.byte 0x39, 0xf0, 0xa1, 0x52, 0x1d, 0x0f, 0x17, 0xb7, 0xa4, 0xcb, 0x1d, 0x3b, 0x79, 0x23, 0x78, 0x44
.byte 0x79, 0x36, 0xd5, 0x97, 0xd7, 0xd6, 0xd9, 0xc9, 0x8c, 0x6a, 0x50, 0x2d, 0x52, 0x8d, 0x94, 0x20
.byte 0x00, 0x00, 0x00, 0x2f, 0x58, 0x06, 0x9c, 0x61, 0x84, 0xf1, 0x98, 0x29, 0xda, 0x0c, 0xb4, 0x02
.byte 0xb8, 0xef, 0x87, 0xff, 0x38, 0x05, 0x91, 0x2e, 0x33, 0x29, 0xbe, 0xf2, 0x3e, 0xd4, 0x3a, 0x0b
.byte 0x8f, 0x76, 0xbd, 0x39, 0x13, 0xbb, 0x46, 0x4f, 0xea, 0xb4, 0x7a, 0x05, 0xbf, 0x8b, 0x6a, 0x45
.byte 0xff, 0x38, 0x34, 0x00, 0x00, 0x00, 0x15, 0x58, 0x01, 0xf2, 0x5e, 0x72, 0x36, 0x92, 0x1f, 0x05
.byte 0xfa, 0x47, 0x8d, 0x69, 0x2c, 0x8f, 0x20, 0x0a, 0xfb, 0xfb, 0x2b, 0x20, 0x00, 0x00, 0x00, 0x15
.byte 0x58, 0x01, 0xf2, 0x5e, 0x72, 0x36, 0x92, 0x1f, 0x05, 0xfa, 0x47, 0x8d, 0x69, 0x2c, 0x8f, 0x20
.byte 0x0a, 0xfb, 0xfb, 0x2b, 0x20, 0x00, 0x00, 0x00, 0x15, 0x58, 0x01, 0xf2, 0x5e, 0x72, 0x36, 0x92
.byte 0x1f, 0x05, 0xfa, 0x47, 0x8d, 0x69, 0x2c, 0x8f, 0x20, 0x0a, 0xfb, 0xfb, 0x2b, 0x20, 0x00, 0x00
.byte 0x00, 0x15, 0x58, 0x01, 0xf2, 0x5e, 0x72, 0x36, 0x92, 0x1f, 0x05, 0xfa, 0x47, 0x8d, 0x69, 0x2c
.byte 0x8f, 0x20, 0x0a, 0xfb, 0xfb, 0x2b, 0x20

.global _binary_6_p3_end
_binary_6_p3_end: /* for objcopy compatibility */


.global _6_p3_length
_6_p3_length:
.long 1351
