/* * Data converted from /home/<USER>/bysx/xiaozhi-esp32/main/assets/zh-CN/1.p3
 */
.data
#if !defined (__APPLE__) && !defined (__linux__)
.section .rodata.embedded
#endif

.global _1_p3
_1_p3:

.global _binary_1_p3_start
_binary_1_p3_start: /* for objcopy compatibility */
.byte 0x00, 0x00, 0x00, 0x53, 0x58, 0x00, 0x47, 0x47, 0xdc, 0x26, 0x98, 0xcd, 0x27, 0xee, 0x7b, 0x6b
.byte 0x9d, 0x24, 0x6c, 0xeb, 0x4b, 0x3a, 0x87, 0x65, 0xbb, 0x32, 0xa1, 0xd9, 0x84, 0x1d, 0xed, 0x00
.byte 0x80, 0xf2, 0x05, 0x59, 0x9f, 0x21, 0xd1, 0xec, 0x31, 0x25, 0x60, 0x15, 0x41, 0x99, 0xd8, 0x80
.byte 0x6f, 0x1b, 0x3a, 0x00, 0x97, 0xb5, 0xb8, 0x22, 0xc7, 0x74, 0x1d, 0x34, 0x72, 0xeb, 0x89, 0x1a
.byte 0x37, 0x70, 0xe2, 0x3c, 0x51, 0xc4, 0x48, 0xcb, 0x80, 0x41, 0x72, 0x6c, 0x32, 0x2a, 0x98, 0x16
.byte 0x0c, 0xe1, 0xde, 0xbd, 0xee, 0x5c, 0xc0, 0x00, 0x00, 0x00, 0x5d, 0x58, 0x07, 0xa4, 0xb5, 0x87
.byte 0xf0, 0x04, 0x8f, 0x1c, 0xe7, 0x27, 0x25, 0x63, 0xd2, 0xf3, 0x78, 0xb8, 0xe3, 0xda, 0x47, 0x37
.byte 0x95, 0xd7, 0xbe, 0x5d, 0xf0, 0xb0, 0x90, 0xcf, 0x70, 0x7f, 0xc1, 0x8b, 0x5c, 0xd5, 0xa5, 0x47
.byte 0x9e, 0x42, 0xf2, 0x08, 0x09, 0x32, 0x42, 0x46, 0x8b, 0xf9, 0xdc, 0xb0, 0xd9, 0xfc, 0x04, 0xca
.byte 0xcc, 0xb6, 0x82, 0xc1, 0x6e, 0x39, 0x44, 0x40, 0x7b, 0x04, 0x69, 0xb5, 0xf7, 0x5b, 0xd4, 0xbe
.byte 0x57, 0x15, 0x94, 0x7c, 0x03, 0xec, 0x3f, 0x7e, 0xa7, 0xd3, 0xa2, 0x61, 0xfd, 0x83, 0x27, 0xc5
.byte 0xc4, 0xea, 0x74, 0xfc, 0x38, 0xaa, 0x91, 0x02, 0x00, 0x00, 0x00, 0x92, 0x58, 0xe2, 0x53, 0x83
.byte 0x4b, 0x94, 0x8f, 0xba, 0xb9, 0x61, 0x96, 0xcb, 0x56, 0xdf, 0x27, 0x56, 0x1a, 0x06, 0x1d, 0xbb
.byte 0x77, 0x48, 0xdc, 0x18, 0xcf, 0x35, 0x56, 0xd9, 0x40, 0x7d, 0x13, 0x8f, 0x2f, 0x8e, 0x78, 0xb8
.byte 0x6a, 0xb4, 0x3b, 0x1c, 0x39, 0xe1, 0xf3, 0x37, 0xda, 0xb7, 0x1b, 0x52, 0xe8, 0xba, 0xf6, 0x39
.byte 0xbf, 0xde, 0xf4, 0xfa, 0xb4, 0xf6, 0x13, 0x47, 0x44, 0x19, 0x4d, 0xa4, 0xc2, 0x88, 0x01, 0xa9
.byte 0xa8, 0xf3, 0xa6, 0x4f, 0xec, 0x17, 0x55, 0x8f, 0x31, 0x26, 0xe5, 0x43, 0xf3, 0x0b, 0x1d, 0x5a
.byte 0xcd, 0xf8, 0x5c, 0x83, 0x23, 0xed, 0xf6, 0xc3, 0x07, 0xe4, 0x3d, 0x79, 0xc6, 0x63, 0x2d, 0xae
.byte 0x62, 0x42, 0xed, 0xc4, 0xb7, 0x9e, 0x90, 0xbd, 0xc3, 0xc3, 0xe0, 0xb6, 0xd2, 0x59, 0xd3, 0xeb
.byte 0x1c, 0x39, 0x4b, 0xc2, 0x25, 0x9b, 0xb6, 0x2c, 0xa1, 0x0e, 0x7e, 0xc6, 0xb8, 0x48, 0xf7, 0x41
.byte 0x9d, 0x50, 0x40, 0x9c, 0xf4, 0xf7, 0x40, 0xc8, 0x61, 0x9d, 0x65, 0x1a, 0x50, 0x64, 0x00, 0x00
.byte 0x00, 0x8f, 0x58, 0xea, 0x88, 0x76, 0xc1, 0xd6, 0x40, 0xa9, 0x25, 0x4a, 0x6b, 0xba, 0x17, 0x26
.byte 0x0d, 0xa7, 0x17, 0x6c, 0x9a, 0x9a, 0x36, 0x6c, 0xee, 0x7f, 0x2a, 0x90, 0x63, 0xb3, 0x09, 0x6a
.byte 0xdd, 0x0b, 0xa8, 0x45, 0xc6, 0x0e, 0xcd, 0x60, 0xf4, 0xf8, 0x30, 0xe9, 0x76, 0x96, 0x71, 0x05
.byte 0x2e, 0xe7, 0x21, 0xdc, 0x74, 0x7e, 0xbe, 0x55, 0xb0, 0xa4, 0x31, 0xa8, 0xa0, 0x9b, 0xfb, 0x81
.byte 0x53, 0xa8, 0x57, 0x20, 0xf3, 0x39, 0xb2, 0xec, 0xe1, 0x32, 0xa4, 0x66, 0xbb, 0xe0, 0x1b, 0x38
.byte 0x8b, 0xce, 0x40, 0x56, 0xd3, 0x3e, 0xd5, 0xaf, 0x91, 0xf5, 0x77, 0x4c, 0xfb, 0x1b, 0x45, 0xf7
.byte 0x0e, 0xdf, 0xc9, 0x7e, 0x7c, 0xce, 0x1a, 0x78, 0xd8, 0x97, 0x29, 0x41, 0xf8, 0xc6, 0x21, 0xe6
.byte 0xcc, 0x18, 0xbd, 0x34, 0x4e, 0x19, 0x15, 0x67, 0x6d, 0x0c, 0xa0, 0x89, 0x5a, 0x33, 0x52, 0x15
.byte 0x1c, 0xa4, 0xf5, 0xb5, 0xe9, 0x9e, 0xfb, 0xdb, 0xe4, 0xef, 0xde, 0x45, 0xb6, 0x5f, 0x2c, 0xab
.byte 0x83, 0x00, 0x00, 0x00, 0x81, 0x58, 0xed, 0xfd, 0xde, 0xbc, 0xaa, 0x5c, 0x83, 0xc3, 0x32, 0xb7
.byte 0x90, 0xde, 0x1d, 0x0a, 0xf7, 0x57, 0x4e, 0xd3, 0x5f, 0x53, 0x1e, 0x7f, 0x3c, 0xa8, 0x1a, 0x26
.byte 0xda, 0x66, 0xaa, 0x9e, 0x70, 0x35, 0x62, 0x8a, 0xcf, 0x0d, 0xf0, 0x70, 0x2f, 0x5b, 0x0e, 0x97
.byte 0x55, 0xe2, 0x21, 0xab, 0xb7, 0x24, 0x23, 0x62, 0x05, 0xca, 0x75, 0x05, 0x34, 0x78, 0x6a, 0xf1
.byte 0x53, 0xd2, 0xaa, 0xa0, 0xed, 0x53, 0x99, 0x0a, 0xe4, 0x57, 0x3b, 0xdf, 0x07, 0x31, 0xb5, 0xcc
.byte 0x23, 0x8d, 0xe3, 0xf7, 0x92, 0xdd, 0xed, 0xd9, 0xee, 0x8f, 0xfd, 0xe9, 0x9d, 0x9c, 0x1d, 0x91
.byte 0x14, 0x3e, 0xc3, 0x02, 0xb8, 0x6b, 0x76, 0xf1, 0xc0, 0xe0, 0x5e, 0x73, 0x86, 0x3c, 0x96, 0x00
.byte 0x3e, 0xef, 0x8e, 0x6a, 0x3f, 0xb4, 0xb8, 0xb5, 0xb8, 0x78, 0x32, 0x10, 0x50, 0x36, 0x66, 0x0b
.byte 0xdb, 0x2f, 0x76, 0xe8, 0x81, 0xe0, 0x00, 0x00, 0x00, 0x68, 0x58, 0xee, 0x1a, 0x26, 0xeb, 0x57
.byte 0x28, 0xdf, 0x8b, 0x82, 0xfb, 0xc2, 0xd7, 0xfb, 0xe8, 0x49, 0x73, 0xc0, 0x32, 0x40, 0xb4, 0x59
.byte 0xeb, 0xac, 0x8b, 0xed, 0x2b, 0xe6, 0x58, 0xcd, 0xb8, 0x68, 0x88, 0x05, 0x7d, 0xf6, 0x41, 0x53
.byte 0x70, 0xbc, 0xb5, 0x59, 0x12, 0xa0, 0x05, 0x35, 0x3c, 0x69, 0x0b, 0xc3, 0x92, 0x3f, 0x81, 0x17
.byte 0xb5, 0x8d, 0x5b, 0xeb, 0x8c, 0x5f, 0xfa, 0xa4, 0x8b, 0xed, 0x6e, 0x7f, 0x43, 0x2f, 0x4d, 0xf5
.byte 0xd0, 0xf0, 0x70, 0x9c, 0x92, 0x47, 0x3d, 0x7f, 0xf2, 0x26, 0x45, 0x3c, 0xe6, 0x77, 0x35, 0xcd
.byte 0xaa, 0x41, 0x80, 0xcb, 0x23, 0x8e, 0x6d, 0xa8, 0xc1, 0xfd, 0x37, 0x43, 0x0e, 0xa3, 0x18, 0x8b
.byte 0xd8, 0x90, 0x00, 0x00, 0x00, 0x64, 0x58, 0xec, 0xf2, 0x8b, 0x11, 0x2d, 0xe0, 0x51, 0x72, 0xc8
.byte 0x56, 0xfd, 0x7b, 0x5e, 0x32, 0x45, 0x33, 0x01, 0x91, 0xeb, 0xb7, 0xd1, 0x46, 0xf0, 0x75, 0xe9
.byte 0x38, 0x43, 0x11, 0xdc, 0xe0, 0x3b, 0x24, 0x14, 0x55, 0x95, 0xa4, 0x7e, 0xfa, 0x68, 0x7c, 0x0c
.byte 0x68, 0xdb, 0xfb, 0x95, 0x06, 0x81, 0x78, 0x06, 0xd1, 0xe3, 0xb5, 0x8f, 0xf2, 0x4a, 0x4e, 0x34
.byte 0xe9, 0x06, 0xd5, 0x6e, 0xa4, 0x87, 0xc7, 0xdc, 0x62, 0x9a, 0xe3, 0x9e, 0xa1, 0xf0, 0x56, 0xf3
.byte 0xa7, 0xe1, 0x3e, 0x01, 0x45, 0xcc, 0xb9, 0xca, 0x22, 0xd1, 0x04, 0x45, 0x53, 0xe0, 0x01, 0x7d
.byte 0x39, 0xd5, 0xc5, 0x68, 0x1b, 0x40, 0x31, 0x28, 0x50, 0x40, 0x00, 0x00, 0x00, 0x77, 0x58, 0xec
.byte 0x0d, 0x55, 0x6e, 0xc1, 0xc4, 0x7c, 0x1b, 0x06, 0x60, 0x4c, 0xa4, 0x09, 0xe2, 0x91, 0x56, 0xcf
.byte 0x6f, 0x53, 0xe4, 0xe3, 0x1a, 0x47, 0xde, 0xf2, 0x9a, 0x04, 0x50, 0x77, 0xde, 0xcf, 0x3a, 0x5e
.byte 0x0b, 0xf3, 0x32, 0x75, 0x7c, 0x77, 0x6d, 0xaf, 0xbf, 0xc2, 0x75, 0x3f, 0xe7, 0x2d, 0x24, 0x47
.byte 0x27, 0x15, 0x77, 0x66, 0xf4, 0xff, 0x19, 0xe1, 0x82, 0xd0, 0x37, 0x8e, 0xb3, 0x57, 0xbf, 0x3d
.byte 0x9e, 0xc8, 0x1b, 0xd9, 0xef, 0x52, 0x56, 0xb9, 0x16, 0x24, 0x84, 0xac, 0x92, 0x51, 0x57, 0x77
.byte 0x54, 0x8a, 0x55, 0xba, 0xc8, 0x06, 0xdd, 0xc5, 0x66, 0xcc, 0x94, 0x51, 0x7f, 0xee, 0xdb, 0x78
.byte 0xdb, 0x09, 0xb9, 0x78, 0x58, 0xf1, 0x37, 0xbf, 0x55, 0x9f, 0x66, 0xb2, 0x02, 0xad, 0xd2, 0x7d
.byte 0x0c, 0x7d, 0x2d, 0xc2, 0x80, 0x00, 0x00, 0x00, 0x89, 0x58, 0xea, 0x51, 0x4d, 0x72, 0xe6, 0xcf
.byte 0x40, 0x99, 0xc8, 0x14, 0x39, 0x75, 0xbe, 0xb9, 0x55, 0x20, 0x6a, 0xca, 0x16, 0xc1, 0x49, 0x37
.byte 0x08, 0xe6, 0xbb, 0x45, 0x6a, 0xa7, 0xaa, 0x64, 0xbe, 0xe3, 0x59, 0xdb, 0xd5, 0x1d, 0x56, 0x19
.byte 0x0b, 0x79, 0xa6, 0xb5, 0x66, 0x17, 0xce, 0xc8, 0x85, 0x94, 0xf3, 0x60, 0x1b, 0xfa, 0x28, 0xb7
.byte 0x73, 0xa4, 0xd6, 0x69, 0x6f, 0x4b, 0x0e, 0x0c, 0x7d, 0xca, 0xb0, 0xd9, 0x40, 0xcc, 0xdc, 0x7c
.byte 0x3e, 0x66, 0xa7, 0x9e, 0xf6, 0x1c, 0xf3, 0x76, 0xb4, 0xc3, 0xb2, 0xda, 0x26, 0xcd, 0xc4, 0xc6
.byte 0xf0, 0x2f, 0xa3, 0x59, 0x41, 0x92, 0xa1, 0x07, 0xe6, 0x75, 0x13, 0x0d, 0x8f, 0x17, 0x9c, 0xc0
.byte 0x1d, 0xe8, 0x82, 0xe5, 0x46, 0x11, 0x41, 0x1c, 0xe2, 0x5a, 0xdd, 0x02, 0xfe, 0x3f, 0x99, 0xf1
.byte 0xbb, 0x5a, 0x52, 0x2d, 0x72, 0x7c, 0x4b, 0xd8, 0xd4, 0x47, 0x2e, 0x03, 0x91, 0x71, 0xc4, 0x83
.byte 0xd2, 0x40, 0x00, 0x00, 0x00, 0x63, 0x58, 0x8f, 0x78, 0xd6, 0x5e, 0x1c, 0x48, 0x5b, 0x93, 0xcb
.byte 0x25, 0x78, 0x5e, 0x2c, 0xef, 0xea, 0x47, 0x79, 0x6e, 0x5f, 0x92, 0xc5, 0x46, 0xae, 0xb4, 0xce
.byte 0x6f, 0xc7, 0x0b, 0x29, 0xd8, 0xe8, 0x1c, 0xb5, 0xc4, 0x52, 0x16, 0x6e, 0xcc, 0x5f, 0x11, 0x5c
.byte 0x96, 0x1b, 0x01, 0xa2, 0x6f, 0x25, 0x35, 0xb1, 0xe2, 0x40, 0x18, 0x30, 0xfe, 0x4d, 0xc3, 0xdf
.byte 0x3f, 0xc2, 0x21, 0x7c, 0x98, 0x4f, 0xe1, 0xaf, 0x35, 0x6c, 0x80, 0x9a, 0xc5, 0x2b, 0x84, 0x51
.byte 0x58, 0x6f, 0x3e, 0x1a, 0xb8, 0x43, 0x60, 0x17, 0x03, 0x90, 0x53, 0x49, 0xdd, 0x95, 0x97, 0xa3
.byte 0xa3, 0xf4, 0x0d, 0xdb, 0xa0, 0xb5, 0x99, 0x89, 0x80, 0x00, 0x00, 0x00, 0x49, 0x58, 0x07, 0xc0
.byte 0x31, 0x11, 0x46, 0x55, 0x57, 0xce, 0x66, 0xbf, 0x2b, 0x77, 0xbc, 0xc0, 0x58, 0x02, 0x89, 0xf2
.byte 0x0f, 0x9f, 0xfb, 0x15, 0x38, 0xd3, 0x8c, 0x27, 0xa6, 0x31, 0xad, 0x8f, 0xea, 0x2c, 0x88, 0xf1
.byte 0x9d, 0xed, 0x41, 0x05, 0xd8, 0x77, 0xec, 0x2f, 0xfc, 0xed, 0xf2, 0x35, 0x29, 0x07, 0x21, 0x4f
.byte 0x63, 0x53, 0xe9, 0x6d, 0x65, 0x3a, 0x07, 0xe0, 0xe7, 0x01, 0x70, 0xc2, 0xfb, 0xc7, 0x64, 0x0d
.byte 0x70, 0xd0, 0xba, 0xa5, 0x2a, 0x80, 0x00, 0x00, 0x00, 0x33, 0x58, 0x07, 0x5a, 0x45, 0xe8, 0x7b
.byte 0x10, 0xcc, 0x34, 0xe2, 0x8a, 0xfb, 0x1e, 0x6f, 0x3b, 0xe1, 0x62, 0xcd, 0x66, 0x9f, 0x95, 0xf5
.byte 0xb2, 0x30, 0xcf, 0xb9, 0xf4, 0xf0, 0x16, 0xf3, 0xd9, 0x66, 0x4c, 0xbb, 0x52, 0x3b, 0x97, 0xda
.byte 0xe7, 0xf2, 0xa4, 0xf1, 0x54, 0x74, 0xd2, 0xe8, 0x4f, 0xab, 0x16, 0x2e, 0x10, 0x00, 0x00, 0x00
.byte 0x2e, 0x58, 0x02, 0x62, 0xa1, 0xe9, 0xd4, 0x57, 0xab, 0x8b, 0xaa, 0x6c, 0x8c, 0xdb, 0x58, 0xed
.byte 0xdd, 0xbb, 0xb0, 0x42, 0x71, 0x3b, 0xdd, 0x3d, 0xae, 0xf2, 0xaf, 0xe3, 0xb9, 0x82, 0x8a, 0xe0
.byte 0xf1, 0x49, 0xc3, 0x5e, 0xaa, 0xa0, 0x25, 0x46, 0x24, 0xa4, 0x6a, 0xc1, 0x8f, 0x9c, 0x18, 0x00
.byte 0x00, 0x00, 0x15, 0x58, 0x01, 0xf2, 0x5e, 0x74, 0x52, 0x7e, 0x3e, 0x57, 0xca, 0x82, 0x7f, 0x0c
.byte 0x19, 0xe9, 0xe3, 0x1b, 0x65, 0x1d, 0xd5, 0x7a, 0x00, 0x00, 0x00, 0x15, 0x58, 0x01, 0xf2, 0x5e
.byte 0x72, 0x36, 0x92, 0x1f, 0x05, 0xfa, 0x47, 0x8d, 0x69, 0x2c, 0x8f, 0x20, 0x0a, 0xfb, 0xfb, 0x2b
.byte 0x20, 0x00, 0x00, 0x00, 0x15, 0x58, 0x01, 0xf2, 0x5e, 0x72, 0x36, 0x92, 0x1f, 0x05, 0xfa, 0x47
.byte 0x8d, 0x69, 0x2c, 0x8f, 0x20, 0x0a, 0xfb, 0xfb, 0x2b, 0x20, 0x00, 0x00, 0x00, 0x15, 0x58, 0x01
.byte 0xf2, 0x5e, 0x72, 0x36, 0x92, 0x1f, 0x05, 0xfa, 0x47, 0x8d, 0x69, 0x2c, 0x8f, 0x20, 0x0a, 0xfb
.byte 0xfb, 0x2b, 0x20

.global _binary_1_p3_end
_binary_1_p3_end: /* for objcopy compatibility */


.global _1_p3_length
_1_p3_length:
.long 1475
