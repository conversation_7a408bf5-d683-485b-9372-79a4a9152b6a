/* * Data converted from /home/<USER>/bysx/xiaozhi-esp32/main/assets/zh-CN/7.p3
 */
.data
#if !defined (__APPLE__) && !defined (__linux__)
.section .rodata.embedded
#endif

.global _7_p3
_7_p3:

.global _binary_7_p3_start
_binary_7_p3_start: /* for objcopy compatibility */
.byte 0x00, 0x00, 0x00, 0x5f, 0x58, 0x00, 0x75, 0x6b, 0xa4, 0x40, 0x03, 0x29, 0xab, 0x25, 0xd8, 0xf8
.byte 0x48, 0x6d, 0x3e, 0xf3, 0x6e, 0x53, 0x15, 0x3c, 0x0a, 0x44, 0xb7, 0xce, 0x35, 0xc9, 0xf4, 0x63
.byte 0xee, 0x14, 0x92, 0x73, 0x4a, 0x1d, 0x1e, 0x97, 0x44, 0xc0, 0xcd, 0x06, 0x63, 0xb2, 0xf9, 0x1f
.byte 0xe7, 0x37, 0xd2, 0xf0, 0xac, 0xf6, 0x39, 0xe5, 0xaf, 0x94, 0x21, 0xaa, 0xbc, 0xcb, 0x98, 0x3b
.byte 0xd3, 0x71, 0xc8, 0xc0, 0x1e, 0xed, 0x15, 0x4c, 0xdf, 0x2f, 0x98, 0x87, 0x70, 0x93, 0xcb, 0x37
.byte 0x66, 0xe1, 0x8a, 0x38, 0x9b, 0xaa, 0x39, 0x99, 0x87, 0x06, 0xb1, 0x89, 0x1e, 0x09, 0xae, 0x6d
.byte 0x6f, 0x05, 0x30, 0x00, 0x00, 0x00, 0x5c, 0x58, 0x0a, 0xd7, 0x7f, 0x78, 0xe1, 0xe5, 0x31, 0x02
.byte 0xc7, 0xb7, 0x31, 0x9e, 0x74, 0x14, 0x23, 0xed, 0x49, 0x24, 0xbc, 0x91, 0x2e, 0xdf, 0x16, 0xdd
.byte 0xf6, 0x5e, 0x38, 0xd8, 0xc5, 0x9d, 0xae, 0x16, 0xf2, 0x60, 0xb8, 0xfe, 0xac, 0x03, 0x3d, 0xcc
.byte 0xf1, 0xd5, 0x81, 0x3d, 0x92, 0x6b, 0x88, 0xd8, 0xc1, 0x94, 0xa2, 0xeb, 0x40, 0xb8, 0xee, 0x7d
.byte 0xf9, 0xb3, 0x30, 0x72, 0x89, 0xbc, 0x93, 0x4d, 0x48, 0x5b, 0x29, 0x01, 0x74, 0x6e, 0x70, 0xf0
.byte 0xb0, 0xfd, 0x36, 0x71, 0xb9, 0x58, 0xfc, 0xbe, 0x99, 0x91, 0x69, 0x0f, 0x9d, 0x5c, 0xad, 0xf4
.byte 0x99, 0x18, 0x20, 0x00, 0x00, 0x00, 0x7d, 0x58, 0xef, 0x6e, 0x09, 0xa4, 0x74, 0xfb, 0x45, 0x23
.byte 0xf4, 0x66, 0xd8, 0x7c, 0x6a, 0x90, 0xca, 0x9a, 0xf6, 0x55, 0x5d, 0x3e, 0x6c, 0xac, 0x1a, 0x25
.byte 0x7c, 0x0a, 0x48, 0x70, 0x1a, 0x58, 0xd7, 0x91, 0x27, 0xbf, 0x5c, 0x27, 0x91, 0x1f, 0x1b, 0x41
.byte 0xf6, 0x31, 0xb1, 0xcf, 0xd8, 0x4a, 0xc1, 0x64, 0x0a, 0xcd, 0xdf, 0x55, 0x46, 0xe5, 0x3b, 0x36
.byte 0x5b, 0x0d, 0x02, 0x52, 0x08, 0x90, 0xc3, 0x0f, 0xb6, 0xaa, 0xa5, 0x99, 0x2e, 0x37, 0x2f, 0x84
.byte 0xd8, 0xaf, 0x2a, 0xc5, 0xab, 0xed, 0x8f, 0xd1, 0x75, 0x30, 0x6c, 0x30, 0x40, 0x29, 0xc3, 0x80
.byte 0x3d, 0x6f, 0x7c, 0x2b, 0xad, 0x40, 0xcb, 0x7d, 0xb7, 0x10, 0x45, 0x5e, 0x77, 0x27, 0xfa, 0x6a
.byte 0x2f, 0x06, 0xb7, 0x51, 0xa8, 0xc4, 0xd8, 0xec, 0x7f, 0xdf, 0xd8, 0x10, 0xa0, 0xa3, 0xb8, 0x3b
.byte 0x88, 0xba, 0xd7, 0x48, 0x00, 0x00, 0x00, 0x7b, 0x58, 0xe1, 0x49, 0xd4, 0xc6, 0x2c, 0x17, 0x04
.byte 0xc0, 0xc7, 0x59, 0x82, 0xb3, 0xe5, 0xea, 0xf1, 0x34, 0xd2, 0x9b, 0x54, 0x27, 0x1a, 0xc9, 0x12
.byte 0x7a, 0x64, 0x3c, 0xd3, 0x41, 0x85, 0x81, 0xcb, 0x2a, 0x42, 0x3b, 0x71, 0x31, 0x1c, 0x53, 0x51
.byte 0xe5, 0xaa, 0x20, 0xed, 0x18, 0x74, 0xb0, 0x44, 0x06, 0xd6, 0x35, 0x6f, 0x0a, 0x09, 0xce, 0x5a
.byte 0xdc, 0x34, 0x5f, 0xe4, 0x3c, 0xf2, 0xb1, 0xfc, 0x2e, 0x11, 0xd7, 0x60, 0xbd, 0xad, 0x68, 0x3a
.byte 0x5f, 0x01, 0xaa, 0x6b, 0xb2, 0x22, 0x6f, 0x2e, 0x4e, 0x30, 0x8e, 0xee, 0xdc, 0x27, 0xcf, 0x29
.byte 0xb6, 0x04, 0x3b, 0x97, 0x37, 0x88, 0xc0, 0xc5, 0x71, 0x11, 0x8e, 0x69, 0x22, 0x6a, 0x3a, 0xcd
.byte 0x23, 0x6e, 0xac, 0xda, 0xb6, 0x3c, 0x15, 0x15, 0xde, 0x07, 0x86, 0x05, 0x8c, 0x60, 0xa8, 0x95
.byte 0xc7, 0x70, 0x2c, 0x00, 0x00, 0x00, 0x74, 0x58, 0xe5, 0xd1, 0x0b, 0x41, 0xa4, 0xa0, 0x98, 0x7f
.byte 0x3c, 0x59, 0x3d, 0xe9, 0xbc, 0x98, 0x0a, 0x2b, 0x9a, 0xb1, 0x6e, 0x08, 0x0d, 0x4c, 0xf0, 0xf9
.byte 0xfe, 0x36, 0x88, 0x23, 0x26, 0x90, 0x6f, 0x32, 0xb7, 0x56, 0x5c, 0xa5, 0xce, 0x32, 0x7b, 0x7c
.byte 0xbf, 0xfe, 0x35, 0xf1, 0xff, 0x17, 0x64, 0x60, 0xca, 0x0a, 0xa3, 0xa2, 0x57, 0x91, 0x0f, 0x96
.byte 0x16, 0xc0, 0xcb, 0xee, 0x09, 0x03, 0x7a, 0x33, 0xbc, 0x73, 0xb6, 0x8c, 0xf1, 0x73, 0x32, 0x4f
.byte 0x56, 0x23, 0x8b, 0x36, 0xf5, 0x4a, 0x32, 0x06, 0x87, 0x33, 0xbc, 0x80, 0xbc, 0xda, 0x7b, 0xea
.byte 0xb6, 0x7a, 0xae, 0xd2, 0x52, 0xe8, 0xdf, 0x7d, 0x30, 0x84, 0x70, 0x28, 0xef, 0x39, 0x9e, 0xca
.byte 0xbd, 0x2a, 0x04, 0x44, 0x72, 0xca, 0x71, 0xd9, 0xc6, 0xc4, 0xb8, 0x00, 0x00, 0x00, 0x61, 0x58
.byte 0xe5, 0xa7, 0x34, 0x3c, 0x0a, 0xee, 0x0b, 0x01, 0xd4, 0xee, 0x64, 0xc8, 0xdc, 0x77, 0xc0, 0x6d
.byte 0xb7, 0x52, 0xda, 0x08, 0xee, 0x58, 0xfe, 0xfc, 0xb2, 0x5b, 0xf8, 0x45, 0x51, 0x5d, 0x10, 0x62
.byte 0xcb, 0x22, 0x5e, 0x20, 0x9e, 0x6b, 0x86, 0xb1, 0xdb, 0xd6, 0xf9, 0xb5, 0xf3, 0x0c, 0x80, 0xde
.byte 0x29, 0xce, 0x40, 0x68, 0x66, 0x3f, 0xd2, 0xea, 0xc0, 0xad, 0x56, 0x3b, 0xa9, 0xf3, 0xf6, 0x96
.byte 0x81, 0x5b, 0xdd, 0xaa, 0xa5, 0x58, 0x9f, 0xcc, 0xff, 0x4b, 0x0f, 0xb5, 0x8b, 0x3e, 0x3e, 0x33
.byte 0x37, 0x98, 0x9f, 0xc0, 0xce, 0x9f, 0x4c, 0xad, 0x18, 0x56, 0xf2, 0xba, 0x21, 0x60, 0x62, 0xfe
.byte 0x00, 0x00, 0x00, 0x52, 0x58, 0xee, 0x6f, 0x0f, 0x63, 0x77, 0xb1, 0x80, 0xe2, 0x48, 0x5f, 0x55
.byte 0x93, 0xa3, 0xda, 0x77, 0x54, 0x72, 0x0b, 0x2f, 0x0e, 0x6f, 0x23, 0x84, 0xcc, 0xd5, 0x4f, 0x37
.byte 0xac, 0x08, 0x23, 0x33, 0x39, 0x01, 0x59, 0x6c, 0x30, 0xa6, 0x0f, 0x97, 0x17, 0x36, 0xb3, 0xd8
.byte 0xef, 0xdf, 0x37, 0x91, 0x07, 0x0f, 0x8f, 0xfd, 0x0c, 0xa7, 0x5f, 0x5d, 0x24, 0x4f, 0xb9, 0x48
.byte 0x52, 0x74, 0x48, 0xea, 0xa1, 0x66, 0xa0, 0x66, 0x7a, 0x47, 0xd0, 0x01, 0xd3, 0x55, 0xe9, 0xb3
.byte 0x7b, 0x97, 0xe5, 0x12, 0x79, 0x40, 0x00, 0x00, 0x00, 0x4d, 0x58, 0xee, 0x9d, 0x63, 0xfa, 0x8c
.byte 0x1a, 0x30, 0x35, 0xef, 0x39, 0x2d, 0x19, 0x16, 0xf1, 0x58, 0xca, 0xdc, 0x6f, 0xba, 0xc9, 0xc7
.byte 0x99, 0x84, 0x58, 0xfb, 0x9e, 0x19, 0x3c, 0x73, 0x1b, 0x05, 0x4e, 0x0e, 0xfb, 0xf6, 0xa7, 0x1e
.byte 0x85, 0x7c, 0x42, 0xbc, 0x37, 0x61, 0x8c, 0xb5, 0x49, 0x08, 0x5e, 0x8c, 0xea, 0xc6, 0x85, 0x57
.byte 0xfa, 0xdd, 0xea, 0x86, 0xf3, 0x25, 0x02, 0x56, 0x41, 0x87, 0x36, 0x09, 0xe6, 0x41, 0xe3, 0xa4
.byte 0x29, 0x72, 0x9f, 0x48, 0x65, 0xbd, 0x74, 0x00, 0x00, 0x00, 0x6f, 0x58, 0xee, 0x69, 0x8a, 0x82
.byte 0x6a, 0xfb, 0x88, 0x7b, 0xa2, 0xfc, 0x40, 0xff, 0xb8, 0xdd, 0xc4, 0x25, 0xb6, 0xa0, 0xe7, 0xa9
.byte 0x86, 0x1f, 0x87, 0x26, 0xb3, 0xd6, 0x66, 0x44, 0xdc, 0xcb, 0x69, 0x4c, 0xe4, 0xdf, 0xdd, 0x15
.byte 0xd3, 0xab, 0xf6, 0x9c, 0x80, 0x17, 0x8f, 0x73, 0xbe, 0x35, 0x06, 0xa3, 0xcd, 0x6c, 0x3e, 0x9f
.byte 0xe5, 0xa6, 0xd1, 0x8f, 0xfd, 0x1f, 0x3d, 0xec, 0x5e, 0x9a, 0x2e, 0xe7, 0x2c, 0xd0, 0x4c, 0x4b
.byte 0x0a, 0xd3, 0x1b, 0x48, 0xec, 0xad, 0x9d, 0xa5, 0x47, 0x39, 0x12, 0xef, 0x5e, 0xb1, 0x69, 0x6b
.byte 0xc0, 0x52, 0x4a, 0x34, 0x70, 0x3e, 0x73, 0x10, 0x7c, 0x47, 0xcf, 0x4a, 0x5f, 0xd9, 0x12, 0x3b
.byte 0x84, 0xf0, 0x80, 0xbf, 0xb0, 0xe8, 0x65, 0x2b, 0xba, 0xc0, 0x00, 0x00, 0x00, 0x8e, 0x58, 0xed
.byte 0xde, 0xcc, 0xfe, 0x03, 0x95, 0x5a, 0x16, 0x03, 0x72, 0x48, 0x69, 0x93, 0x53, 0xd8, 0x34, 0xc0
.byte 0x30, 0x8b, 0x35, 0x5c, 0x35, 0x2e, 0x8d, 0x50, 0x44, 0x77, 0x56, 0x95, 0x40, 0x62, 0xf6, 0xa1
.byte 0x3b, 0xa8, 0xdd, 0xc5, 0x71, 0x2c, 0x94, 0x5a, 0x47, 0x2e, 0x06, 0x29, 0x48, 0x08, 0x21, 0x21
.byte 0x5f, 0x80, 0xed, 0x92, 0x23, 0x97, 0xdd, 0x54, 0xf2, 0xf8, 0xc6, 0x34, 0x8d, 0xd7, 0x12, 0x8b
.byte 0x14, 0xff, 0xe5, 0xe1, 0x35, 0x04, 0x98, 0x35, 0x49, 0xcc, 0x9c, 0x21, 0x5a, 0x94, 0x41, 0xde
.byte 0xd6, 0xbd, 0xb4, 0x08, 0xb5, 0xe4, 0x01, 0x9c, 0x73, 0xda, 0xb3, 0x51, 0xe6, 0xec, 0xf0, 0x0a
.byte 0x55, 0x72, 0x13, 0x1b, 0x11, 0x7a, 0x06, 0x0f, 0x80, 0xcc, 0xf7, 0xd9, 0xbe, 0x2e, 0x79, 0x3d
.byte 0x9e, 0xba, 0x7c, 0xf0, 0x64, 0xad, 0xa3, 0x27, 0x75, 0xbd, 0x8d, 0x48, 0x59, 0x9b, 0xe5, 0x59
.byte 0xe4, 0x86, 0xa8, 0x12, 0x91, 0x27, 0x0e, 0x2e, 0xe0, 0x64, 0x31, 0xe0, 0x00, 0x00, 0x00, 0x5d
.byte 0x58, 0x0c, 0x25, 0xdc, 0xd7, 0x84, 0xc2, 0x94, 0x6b, 0xbd, 0x01, 0x57, 0x70, 0x2f, 0xd9, 0x49
.byte 0x6b, 0x5b, 0x34, 0x0f, 0x95, 0x48, 0x0f, 0xac, 0xa2, 0xb0, 0xfb, 0x4e, 0x74, 0x06, 0x8d, 0xdd
.byte 0x75, 0xed, 0x32, 0xa9, 0xb2, 0x7c, 0x78, 0x7d, 0xc6, 0x34, 0x98, 0x88, 0x30, 0xf0, 0xae, 0x4e
.byte 0x8b, 0x6a, 0xfd, 0x34, 0xf0, 0xce, 0xa0, 0xaf, 0x58, 0xfa, 0x78, 0xc5, 0x0b, 0x24, 0x83, 0x43
.byte 0x23, 0xc7, 0x3c, 0x7c, 0xe5, 0xc8, 0x34, 0x79, 0x7a, 0xf8, 0xb4, 0x63, 0x00, 0x99, 0x38, 0x09
.byte 0xb2, 0x78, 0x8f, 0xbe, 0xcb, 0x5e, 0x10, 0xdc, 0xed, 0x97, 0xf1, 0x1a, 0x20, 0x00, 0x00, 0x00
.byte 0x5d, 0x58, 0x0b, 0x52, 0x77, 0x5e, 0xd4, 0xf6, 0xe4, 0x08, 0x35, 0x55, 0x91, 0x3b, 0xff, 0xd3
.byte 0x82, 0x73, 0xeb, 0xca, 0x65, 0x4d, 0x64, 0x1b, 0x75, 0x40, 0x63, 0x8f, 0x6a, 0x85, 0x7f, 0x72
.byte 0xa1, 0x2d, 0x38, 0x8e, 0xa1, 0x9f, 0x60, 0xfc, 0x6a, 0xac, 0x89, 0xb2, 0xe2, 0x5c, 0x7f, 0xf6
.byte 0x11, 0xab, 0xf1, 0xee, 0x4c, 0xec, 0x83, 0xa6, 0xd8, 0x2f, 0xef, 0xba, 0x74, 0x04, 0xbc, 0x8f
.byte 0x20, 0x4d, 0xe8, 0x28, 0x87, 0xb1, 0xa3, 0xef, 0x18, 0x42, 0x9b, 0xb0, 0xcb, 0xd3, 0x81, 0x77
.byte 0xee, 0xf1, 0xc9, 0x15, 0x57, 0xb4, 0x4a, 0x72, 0x85, 0xdc, 0x2a, 0x8c, 0x41, 0xe0, 0x00, 0x00
.byte 0x00, 0x50, 0x58, 0x2b, 0x51, 0xc7, 0xe5, 0xac, 0x05, 0x18, 0x0c, 0x54, 0x10, 0xec, 0x57, 0xfd
.byte 0x4d, 0x04, 0xd0, 0x74, 0xf3, 0xde, 0x75, 0x0a, 0x90, 0x8e, 0xa5, 0xea, 0x5c, 0x2f, 0x12, 0x8f
.byte 0xf6, 0xae, 0xa8, 0x59, 0x1b, 0x6a, 0xea, 0xf3, 0x2f, 0xee, 0x5c, 0x2f, 0xbe, 0xed, 0x99, 0x48
.byte 0x0f, 0xf1, 0xc9, 0xc8, 0x07, 0x67, 0xa6, 0x41, 0x72, 0xd0, 0xcf, 0x9b, 0x92, 0x0c, 0xd3, 0xf1
.byte 0xeb, 0x4e, 0x38, 0xe7, 0x1f, 0xa3, 0xad, 0x20, 0xbb, 0x5f, 0xf9, 0xc8, 0x53, 0x67, 0x3a, 0xa5
.byte 0xcc, 0xcb, 0x00, 0x00, 0x00, 0x37, 0x58, 0x03, 0x3a, 0x61, 0xbb, 0xec, 0x95, 0xfd, 0x1d, 0x1b
.byte 0xc5, 0xfa, 0x72, 0x0c, 0xee, 0xb1, 0x63, 0xee, 0x6f, 0xd2, 0x31, 0xa8, 0x01, 0x9d, 0x85, 0x80
.byte 0x6c, 0xb2, 0xac, 0x47, 0x94, 0x7a, 0xfa, 0x8f, 0x87, 0xdd, 0xdf, 0x4f, 0x94, 0xf8, 0xb1, 0xb4
.byte 0x39, 0xea, 0xfc, 0x69, 0x4f, 0x95, 0xf3, 0xbf, 0xfb, 0x3b, 0x37, 0x07, 0x98, 0x00, 0x00, 0x00
.byte 0x16, 0x58, 0x01, 0xf2, 0x5e, 0x71, 0x51, 0x7b, 0xb9, 0xea, 0xba, 0xb9, 0x6a, 0x81, 0x8c, 0x63
.byte 0x9e, 0xa6, 0xc3, 0x79, 0x97, 0x3f, 0x28, 0x00, 0x00, 0x00, 0x15, 0x58, 0x01, 0xf2, 0x5e, 0x72
.byte 0x36, 0x92, 0x1f, 0x05, 0xfa, 0x47, 0x8d, 0x69, 0x2c, 0x8f, 0x20, 0x0a, 0xfb, 0xfb, 0x2b, 0x20
.byte 0x00, 0x00, 0x00, 0x15, 0x58, 0x01, 0xf2, 0x5e, 0x72, 0x36, 0x92, 0x1f, 0x05, 0xfa, 0x47, 0x8d
.byte 0x69, 0x2c, 0x8f, 0x20, 0x0a, 0xfb, 0xfb, 0x2b, 0x20, 0x00, 0x00, 0x00, 0x15, 0x58, 0x01, 0xf2
.byte 0x5e, 0x72, 0x36, 0x92, 0x1f, 0x05, 0xfa, 0x47, 0x8d, 0x69, 0x2c, 0x8f, 0x20, 0x0a, 0xfb, 0xfb
.byte 0x2b, 0x20

.global _binary_7_p3_end
_binary_7_p3_end: /* for objcopy compatibility */


.global _7_p3_length
_7_p3_length:
.long 1538
