{"version": "1.2", "project_name": "xia<PERSON><PERSON>", "project_version": "1.8.4", "project_path": "/home/<USER>/bysx/xiaozhi-esp32", "idf_path": "/home/<USER>/esp32/esp-idf", "build_dir": "/home/<USER>/bysx/xiaozhi-esp32/build", "config_file": "/home/<USER>/bysx/xiaozhi-esp32/sdkconfig", "config_defaults": "/home/<USER>/bysx/xiaozhi-esp32/sdkconfig.defaults", "bootloader_elf": "/home/<USER>/bysx/xiaozhi-esp32/build/bootloader/bootloader.elf", "app_elf": "xiaozhi.elf", "app_bin": "xiaozhi.bin", "build_type": "flash_app", "git_revision": "v5.4.1-dirty", "target": "esp32s3", "rev": "", "min_rev": "0", "max_rev": "99", "phy_data_partition": "", "monitor_baud": "115200", "monitor_toolprefix": "xtensa-esp32s3-elf-", "c_compiler": "/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc", "config_environment": {"COMPONENT_KCONFIGS": "/home/<USER>/esp32/esp-idf/components/app_trace/Kconfig;/home/<USER>/esp32/esp-idf/components/bt/Kconfig;/home/<USER>/esp32/esp-idf/components/console/Kconfig;/home/<USER>/esp32/esp-idf/components/driver/Kconfig;/home/<USER>/esp32/esp-idf/components/efuse/Kconfig;/home/<USER>/esp32/esp-idf/components/esp-tls/Kconfig;/home/<USER>/esp32/esp-idf/components/esp_adc/Kconfig;/home/<USER>/esp32/esp-idf/components/esp_coex/Kconfig;/home/<USER>/esp32/esp-idf/components/esp_common/Kconfig;/home/<USER>/esp32/esp-idf/components/esp_driver_ana_cmpr/Kconfig;/home/<USER>/esp32/esp-idf/components/esp_driver_cam/Kconfig;/home/<USER>/esp32/esp-idf/components/esp_driver_dac/Kconfig;/home/<USER>/esp32/esp-idf/components/esp_driver_gpio/Kconfig;/home/<USER>/esp32/esp-idf/components/esp_driver_gptimer/Kconfig;/home/<USER>/esp32/esp-idf/components/esp_driver_i2c/Kconfig;/home/<USER>/esp32/esp-idf/components/esp_driver_i2s/Kconfig;/home/<USER>/esp32/esp-idf/components/esp_driver_isp/Kconfig;/home/<USER>/esp32/esp-idf/components/esp_driver_jpeg/Kconfig;/home/<USER>/esp32/esp-idf/components/esp_driver_ledc/Kconfig;/home/<USER>/esp32/esp-idf/components/esp_driver_mcpwm/Kconfig;/home/<USER>/esp32/esp-idf/components/esp_driver_parlio/Kconfig;/home/<USER>/esp32/esp-idf/components/esp_driver_pcnt/Kconfig;/home/<USER>/esp32/esp-idf/components/esp_driver_rmt/Kconfig;/home/<USER>/esp32/esp-idf/components/esp_driver_sdm/Kconfig;/home/<USER>/esp32/esp-idf/components/esp_driver_spi/Kconfig;/home/<USER>/esp32/esp-idf/components/esp_driver_touch_sens/Kconfig;/home/<USER>/esp32/esp-idf/components/esp_driver_tsens/Kconfig;/home/<USER>/esp32/esp-idf/components/esp_driver_uart/Kconfig;/home/<USER>/esp32/esp-idf/components/esp_driver_usb_serial_jtag/Kconfig;/home/<USER>/esp32/esp-idf/components/esp_eth/Kconfig;/home/<USER>/esp32/esp-idf/components/esp_event/Kconfig;/home/<USER>/esp32/esp-idf/components/esp_gdbstub/Kconfig;/home/<USER>/esp32/esp-idf/components/esp_hid/Kconfig;/home/<USER>/esp32/esp-idf/components/esp_http_client/Kconfig;/home/<USER>/esp32/esp-idf/components/esp_http_server/Kconfig;/home/<USER>/esp32/esp-idf/components/esp_https_ota/Kconfig;/home/<USER>/esp32/esp-idf/components/esp_https_server/Kconfig;/home/<USER>/esp32/esp-idf/components/esp_hw_support/Kconfig;/home/<USER>/esp32/esp-idf/components/esp_lcd/Kconfig;/home/<USER>/esp32/esp-idf/components/esp_mm/Kconfig;/home/<USER>/esp32/esp-idf/components/esp_netif/Kconfig;/home/<USER>/esp32/esp-idf/components/esp_partition/Kconfig;/home/<USER>/esp32/esp-idf/components/esp_phy/Kconfig;/home/<USER>/esp32/esp-idf/components/esp_pm/Kconfig;/home/<USER>/esp32/esp-idf/components/esp_psram/Kconfig;/home/<USER>/esp32/esp-idf/components/esp_ringbuf/Kconfig;/home/<USER>/esp32/esp-idf/components/esp_security/Kconfig;/home/<USER>/esp32/esp-idf/components/esp_system/Kconfig;/home/<USER>/esp32/esp-idf/components/esp_timer/Kconfig;/home/<USER>/esp32/esp-idf/components/esp_wifi/Kconfig;/home/<USER>/esp32/esp-idf/components/espcoredump/Kconfig;/home/<USER>/esp32/esp-idf/components/fatfs/Kconfig;/home/<USER>/esp32/esp-idf/components/freertos/Kconfig;/home/<USER>/esp32/esp-idf/components/hal/Kconfig;/home/<USER>/esp32/esp-idf/components/heap/Kconfig;/home/<USER>/esp32/esp-idf/components/ieee802154/Kconfig;/home/<USER>/esp32/esp-idf/components/log/Kconfig;/home/<USER>/esp32/esp-idf/components/lwip/Kconfig;/home/<USER>/esp32/esp-idf/components/mbedtls/Kconfig;/home/<USER>/esp32/esp-idf/components/mqtt/esp-mqtt/Kconfig;/home/<USER>/esp32/esp-idf/components/newlib/Kconfig;/home/<USER>/esp32/esp-idf/components/nvs_flash/Kconfig;/home/<USER>/esp32/esp-idf/components/nvs_sec_provider/Kconfig;/home/<USER>/esp32/esp-idf/components/openthread/Kconfig;/home/<USER>/esp32/esp-idf/components/protocomm/Kconfig;/home/<USER>/esp32/esp-idf/components/pthread/Kconfig;/home/<USER>/esp32/esp-idf/components/soc/Kconfig;/home/<USER>/esp32/esp-idf/components/spi_flash/Kconfig;/home/<USER>/esp32/esp-idf/components/spiffs/Kconfig;/home/<USER>/esp32/esp-idf/components/tcp_transport/Kconfig;/home/<USER>/esp32/esp-idf/components/ulp/Kconfig;/home/<USER>/esp32/esp-idf/components/unity/Kconfig;/home/<USER>/esp32/esp-idf/components/usb/Kconfig;/home/<USER>/esp32/esp-idf/components/vfs/Kconfig;/home/<USER>/esp32/esp-idf/components/wear_levelling/Kconfig;/home/<USER>/esp32/esp-idf/components/wifi_provisioning/Kconfig;/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif2022__image_player/Kconfig;/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__adc_battery_estimation/Kconfig;/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__adc_mic/Kconfig;/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__button/Kconfig;/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__cmake_utilities/Kconfig;/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/Kconfig;/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp32-camera/Kconfig;/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_codec_dev/Kconfig;/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_jpeg/Kconfig;/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_lcd_touch/Kconfig;/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_lcd_touch_cst816s/Kconfig;/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_lvgl_port/Kconfig;/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_mmap_assets/Kconfig;/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__knob/Kconfig;/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/Kconfig;/home/<USER>/bysx/xiaozhi-esp32/managed_components/tny-robotics__sh1106-esp-idf/Kconfig", "COMPONENT_KCONFIGS_PROJBUILD": "/home/<USER>/esp32/esp-idf/components/bootloader/Kconfig.projbuild;/home/<USER>/esp32/esp-idf/components/esp_app_format/Kconfig.projbuild;/home/<USER>/esp32/esp-idf/components/esp_rom/Kconfig.projbuild;/home/<USER>/esp32/esp-idf/components/esptool_py/Kconfig.projbuild;/home/<USER>/esp32/esp-idf/components/partition_table/Kconfig.projbuild;/home/<USER>/bysx/xiaozhi-esp32/main/Kconfig.projbuild;/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-sr/Kconfig.projbuild"}, "common_component_reqs": ["cxx", "newlib", "freertos", "esp_hw_support", "heap", "log", "soc", "hal", "esp_rom", "esp_common", "esp_system", "xtensa"], "build_components": ["78__esp-ml307", "78__esp-opus", "78__esp-opus-encoder", "78__esp-wifi-connect", "78__esp_lcd_nv3023", "78__xia<PERSON><PERSON>-fonts", "app_trace", "app_update", "bootloader", "bootloader_support", "bt", "cmock", "console", "cxx", "driver", "efuse", "esp-tls", "esp_adc", "esp_app_format", "esp_bootloader_format", "esp_coex", "esp_common", "esp_driver_ana_cmpr", "esp_driver_cam", "esp_driver_dac", "esp_driver_gpio", "esp_driver_gptimer", "esp_driver_i2c", "esp_driver_i2s", "esp_driver_isp", "esp_driver_jpeg", "esp_driver_ledc", "esp_driver_mcpwm", "esp_driver_parlio", "esp_driver_pcnt", "esp_driver_ppa", "esp_driver_rmt", "esp_driver_sdio", "esp_driver_sdm", "esp_driver_sdmmc", "esp_driver_sdspi", "esp_driver_spi", "esp_driver_touch_sens", "esp_driver_tsens", "esp_driver_uart", "esp_driver_usb_serial_jtag", "esp_eth", "esp_event", "esp_gdbstub", "esp_hid", "esp_http_client", "esp_http_server", "esp_https_ota", "esp_https_server", "esp_hw_support", "esp_lcd", "esp_local_ctrl", "esp_mm", "esp_netif", "esp_netif_stack", "esp_partition", "esp_phy", "esp_pm", "esp_psram", "esp_ringbuf", "esp_rom", "esp_security", "esp_system", "esp_timer", "esp_vfs_console", "esp_wifi", "espcoredump", "espressif2022__image_player", "espressif__adc_battery_estimation", "espressif__adc_mic", "espressif__button", "espressif__cmake_utilities", "espressif__dl_fft", "espressif__esp-dsp", "espressif__esp-sr", "espressif__esp32-camera", "espressif__esp_codec_dev", "espressif__esp_io_expander", "espressif__esp_io_expander_tca9554", "espressif__esp_io_expander_tca95xx_16bit", "espressif__esp_jpeg", "espressif__esp_lcd_axs15231b", "espressif__esp_lcd_gc9a01", "espressif__esp_lcd_ili9341", "espressif__esp_lcd_panel_io_additions", "espressif__esp_lcd_spd2010", "espressif__esp_lcd_st77916", "espressif__esp_lcd_st7796", "espressif__esp_lcd_touch", "espressif__esp_lcd_touch_cst816s", "espressif__esp_lcd_touch_ft5x06", "espressif__esp_lcd_touch_gt911", "espressif__esp_lvgl_port", "espressif__esp_mmap_assets", "espressif__knob", "espressif__led_strip", "esptool_py", "fatfs", "freertos", "hal", "heap", "http_parser", "idf_test", "ieee802154", "json", "log", "lvgl__lvgl", "lwip", "main", "mbedtls", "mqtt", "newlib", "nvs_flash", "nvs_sec_provider", "openthread", "partition_table", "perfmon", "protobuf-c", "protocomm", "pthread", "rt", "sdmmc", "soc", "spi_flash", "spiffs", "tcp_transport", "tny-robotics__sh1106-esp-idf", "touch_element", "txp666__otto-emoji-gif-component", "ulp", "unity", "usb", "vfs", "waveshare__esp_lcd_sh8601", "waveshare__esp_lcd_touch_cst9217", "wear_levelling", "wifi_provisioning", "wpa_supplicant", "wvirgil123__esp_jpeg_simd", "wvirgil123__sscma_client", "xtensa", ""], "build_component_paths": ["/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-ml307", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus-encoder", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-wifi-connect", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp_lcd_nv3023", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__xiaozhi-fonts", "/home/<USER>/esp32/esp-idf/components/app_trace", "/home/<USER>/esp32/esp-idf/components/app_update", "/home/<USER>/esp32/esp-idf/components/bootloader", "/home/<USER>/esp32/esp-idf/components/bootloader_support", "/home/<USER>/esp32/esp-idf/components/bt", "/home/<USER>/esp32/esp-idf/components/cmock", "/home/<USER>/esp32/esp-idf/components/console", "/home/<USER>/esp32/esp-idf/components/cxx", "/home/<USER>/esp32/esp-idf/components/driver", "/home/<USER>/esp32/esp-idf/components/efuse", "/home/<USER>/esp32/esp-idf/components/esp-tls", "/home/<USER>/esp32/esp-idf/components/esp_adc", "/home/<USER>/esp32/esp-idf/components/esp_app_format", "/home/<USER>/esp32/esp-idf/components/esp_bootloader_format", "/home/<USER>/esp32/esp-idf/components/esp_coex", "/home/<USER>/esp32/esp-idf/components/esp_common", "/home/<USER>/esp32/esp-idf/components/esp_driver_ana_cmpr", "/home/<USER>/esp32/esp-idf/components/esp_driver_cam", "/home/<USER>/esp32/esp-idf/components/esp_driver_dac", "/home/<USER>/esp32/esp-idf/components/esp_driver_gpio", "/home/<USER>/esp32/esp-idf/components/esp_driver_gptimer", "/home/<USER>/esp32/esp-idf/components/esp_driver_i2c", "/home/<USER>/esp32/esp-idf/components/esp_driver_i2s", "/home/<USER>/esp32/esp-idf/components/esp_driver_isp", "/home/<USER>/esp32/esp-idf/components/esp_driver_jpeg", "/home/<USER>/esp32/esp-idf/components/esp_driver_ledc", "/home/<USER>/esp32/esp-idf/components/esp_driver_mcpwm", "/home/<USER>/esp32/esp-idf/components/esp_driver_parlio", "/home/<USER>/esp32/esp-idf/components/esp_driver_pcnt", "/home/<USER>/esp32/esp-idf/components/esp_driver_ppa", "/home/<USER>/esp32/esp-idf/components/esp_driver_rmt", "/home/<USER>/esp32/esp-idf/components/esp_driver_sdio", "/home/<USER>/esp32/esp-idf/components/esp_driver_sdm", "/home/<USER>/esp32/esp-idf/components/esp_driver_sdmmc", "/home/<USER>/esp32/esp-idf/components/esp_driver_sdspi", "/home/<USER>/esp32/esp-idf/components/esp_driver_spi", "/home/<USER>/esp32/esp-idf/components/esp_driver_touch_sens", "/home/<USER>/esp32/esp-idf/components/esp_driver_tsens", "/home/<USER>/esp32/esp-idf/components/esp_driver_uart", "/home/<USER>/esp32/esp-idf/components/esp_driver_usb_serial_jtag", "/home/<USER>/esp32/esp-idf/components/esp_eth", "/home/<USER>/esp32/esp-idf/components/esp_event", "/home/<USER>/esp32/esp-idf/components/esp_gdbstub", "/home/<USER>/esp32/esp-idf/components/esp_hid", "/home/<USER>/esp32/esp-idf/components/esp_http_client", "/home/<USER>/esp32/esp-idf/components/esp_http_server", "/home/<USER>/esp32/esp-idf/components/esp_https_ota", "/home/<USER>/esp32/esp-idf/components/esp_https_server", "/home/<USER>/esp32/esp-idf/components/esp_hw_support", "/home/<USER>/esp32/esp-idf/components/esp_lcd", "/home/<USER>/esp32/esp-idf/components/esp_local_ctrl", "/home/<USER>/esp32/esp-idf/components/esp_mm", "/home/<USER>/esp32/esp-idf/components/esp_netif", "/home/<USER>/esp32/esp-idf/components/esp_netif_stack", "/home/<USER>/esp32/esp-idf/components/esp_partition", "/home/<USER>/esp32/esp-idf/components/esp_phy", "/home/<USER>/esp32/esp-idf/components/esp_pm", "/home/<USER>/esp32/esp-idf/components/esp_psram", "/home/<USER>/esp32/esp-idf/components/esp_ringbuf", "/home/<USER>/esp32/esp-idf/components/esp_rom", "/home/<USER>/esp32/esp-idf/components/esp_security", "/home/<USER>/esp32/esp-idf/components/esp_system", "/home/<USER>/esp32/esp-idf/components/esp_timer", "/home/<USER>/esp32/esp-idf/components/esp_vfs_console", "/home/<USER>/esp32/esp-idf/components/esp_wifi", "/home/<USER>/esp32/esp-idf/components/espcoredump", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif2022__image_player", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__adc_battery_estimation", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__adc_mic", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__button", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__cmake_utilities", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__dl_fft", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-sr", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp32-camera", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_codec_dev", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_io_expander", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_io_expander_tca9554", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_io_expander_tca95xx_16bit", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_jpeg", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_lcd_axs15231b", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_lcd_gc9a01", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_lcd_ili9341", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_lcd_panel_io_additions", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_lcd_spd2010", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_lcd_st77916", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_lcd_st7796", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_lcd_touch", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_lcd_touch_cst816s", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_lcd_touch_ft5x06", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_lcd_touch_gt911", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_lvgl_port", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_mmap_assets", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__knob", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__led_strip", "/home/<USER>/esp32/esp-idf/components/esptool_py", "/home/<USER>/esp32/esp-idf/components/fatfs", "/home/<USER>/esp32/esp-idf/components/freertos", "/home/<USER>/esp32/esp-idf/components/hal", "/home/<USER>/esp32/esp-idf/components/heap", "/home/<USER>/esp32/esp-idf/components/http_parser", "/home/<USER>/esp32/esp-idf/components/idf_test", "/home/<USER>/esp32/esp-idf/components/ieee802154", "/home/<USER>/esp32/esp-idf/components/json", "/home/<USER>/esp32/esp-idf/components/log", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl", "/home/<USER>/esp32/esp-idf/components/lwip", "/home/<USER>/bysx/xiaozhi-esp32/main", "/home/<USER>/esp32/esp-idf/components/mbedtls", "/home/<USER>/esp32/esp-idf/components/mqtt", "/home/<USER>/esp32/esp-idf/components/newlib", "/home/<USER>/esp32/esp-idf/components/nvs_flash", "/home/<USER>/esp32/esp-idf/components/nvs_sec_provider", "/home/<USER>/esp32/esp-idf/components/openthread", "/home/<USER>/esp32/esp-idf/components/partition_table", "/home/<USER>/esp32/esp-idf/components/perfmon", "/home/<USER>/esp32/esp-idf/components/protobuf-c", "/home/<USER>/esp32/esp-idf/components/protocomm", "/home/<USER>/esp32/esp-idf/components/pthread", "/home/<USER>/esp32/esp-idf/components/rt", "/home/<USER>/esp32/esp-idf/components/sdmmc", "/home/<USER>/esp32/esp-idf/components/soc", "/home/<USER>/esp32/esp-idf/components/spi_flash", "/home/<USER>/esp32/esp-idf/components/spiffs", "/home/<USER>/esp32/esp-idf/components/tcp_transport", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/tny-robotics__sh1106-esp-idf", "/home/<USER>/esp32/esp-idf/components/touch_element", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/txp666__otto-emoji-gif-component", "/home/<USER>/esp32/esp-idf/components/ulp", "/home/<USER>/esp32/esp-idf/components/unity", "/home/<USER>/esp32/esp-idf/components/usb", "/home/<USER>/esp32/esp-idf/components/vfs", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/waveshare__esp_lcd_sh8601", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/waveshare__esp_lcd_touch_cst9217", "/home/<USER>/esp32/esp-idf/components/wear_levelling", "/home/<USER>/esp32/esp-idf/components/wifi_provisioning", "/home/<USER>/esp32/esp-idf/components/wpa_supplicant", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/wvirgil123__esp_jpeg_simd", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/wvirgil123__sscma_client", "/home/<USER>/esp32/esp-idf/components/xtensa", ""], "build_component_info": {"78__esp-ml307": {"alias": "idf::78__esp-ml307", "target": "___idf_78__esp-ml307", "prefix": "idf", "dir": "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-ml307", "type": "LIBRARY", "lib": "__idf_78__esp-ml307", "reqs": ["esp_driver_gpio", "esp_driver_uart", "esp-tls", "pthread", "mqtt"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/78__esp-ml307/lib78__esp-ml307.a", "sources": ["/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-ml307/src/at_uart.cc", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-ml307/src/at_modem.cc", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-ml307/src/ec801e/ec801e_at_modem.cc", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-ml307/src/ec801e/ec801e_tcp.cc", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-ml307/src/ec801e/ec801e_ssl.cc", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-ml307/src/ec801e/ec801e_udp.cc", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-ml307/src/ec801e/ec801e_mqtt.cc", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-ml307/src/ml307/ml307_at_modem.cc", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-ml307/src/ml307/ml307_tcp.cc", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-ml307/src/ml307/ml307_ssl.cc", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-ml307/src/ml307/ml307_mqtt.cc", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-ml307/src/ml307/ml307_udp.cc", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-ml307/src/ml307/ml307_http.cc", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-ml307/src/esp/esp_network.cc", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-ml307/src/esp/esp_ssl.cc", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-ml307/src/esp/esp_tcp.cc", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-ml307/src/esp/esp_mqtt.cc", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-ml307/src/esp/esp_udp.cc", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-ml307/src/web_socket.cc", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-ml307/src/http_client.cc"], "include_dirs": ["include"]}, "78__esp-opus": {"alias": "idf::78__esp-opus", "target": "___idf_78__esp-opus", "prefix": "idf", "dir": "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus", "type": "LIBRARY", "lib": "__idf_78__esp-opus", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/78__esp-opus/lib78__esp-opus.a", "sources": ["/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/celt/bands.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/celt/celt.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/celt/celt_encoder.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/celt/celt_decoder.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/celt/cwrs.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/celt/entcode.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/celt/entdec.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/celt/entenc.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/celt/kiss_fft.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/celt/laplace.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/celt/mathops.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/celt/mdct.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/celt/modes.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/celt/pitch.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/celt/celt_lpc.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/celt/quant_bands.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/celt/rate.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/celt/vq.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/src/opus.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/src/opus_decoder.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/src/opus_encoder.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/src/extensions.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/src/opus_multistream.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/src/opus_multistream_encoder.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/src/opus_multistream_decoder.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/src/repacketizer.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/src/opus_projection_encoder.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/src/opus_projection_decoder.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/src/mapping_matrix.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/silk/CNG.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/silk/code_signs.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/silk/init_decoder.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/silk/decode_core.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/silk/decode_frame.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/silk/decode_parameters.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/silk/decode_indices.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/silk/decode_pulses.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/silk/decoder_set_fs.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/silk/dec_API.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/silk/enc_API.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/silk/encode_indices.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/silk/encode_pulses.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/silk/gain_quant.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/silk/interpolate.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/silk/LP_variable_cutoff.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/silk/NLSF_decode.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/silk/NSQ.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/silk/NSQ_del_dec.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/silk/PLC.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/silk/shell_coder.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/silk/tables_gain.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/silk/tables_LTP.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/silk/tables_NLSF_CB_NB_MB.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/silk/tables_NLSF_CB_WB.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/silk/tables_other.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/silk/tables_pitch_lag.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/silk/tables_pulses_per_block.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/silk/VAD.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/silk/control_audio_bandwidth.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/silk/quant_LTP_gains.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/silk/VQ_WMat_EC.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/silk/HP_variable_cutoff.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/silk/NLSF_encode.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/silk/NLSF_VQ.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/silk/NLSF_unpack.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/silk/NLSF_del_dec_quant.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/silk/process_NLSFs.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/silk/stereo_LR_to_MS.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/silk/stereo_MS_to_LR.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/silk/check_control_input.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/silk/control_SNR.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/silk/init_encoder.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/silk/control_codec.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/silk/A2NLSF.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/silk/ana_filt_bank_1.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/silk/biquad_alt.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/silk/bwexpander_32.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/silk/bwexpander.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/silk/debug.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/silk/decode_pitch.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/silk/inner_prod_aligned.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/silk/lin2log.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/silk/log2lin.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/silk/LPC_analysis_filter.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/silk/LPC_inv_pred_gain.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/silk/table_LSF_cos.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/silk/NLSF2A.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/silk/NLSF_stabilize.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/silk/NLSF_VQ_weights_laroia.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/silk/pitch_est_tables.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/silk/resampler.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/silk/resampler_down2_3.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/silk/resampler_down2.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/silk/resampler_private_AR2.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/silk/resampler_private_down_FIR.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/silk/resampler_private_IIR_FIR.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/silk/resampler_private_up2_HQ.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/silk/resampler_rom.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/silk/sigm_Q15.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/silk/sort.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/silk/sum_sqr_shift.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/silk/stereo_decode_pred.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/silk/stereo_encode_pred.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/silk/stereo_find_predictor.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/silk/stereo_quant_pred.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/silk/LPC_fit.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/silk/fixed/LTP_analysis_filter_FIX.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/silk/fixed/LTP_scale_ctrl_FIX.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/silk/fixed/corrMatrix_FIX.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/silk/fixed/encode_frame_FIX.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/silk/fixed/find_LPC_FIX.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/silk/fixed/find_LTP_FIX.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/silk/fixed/find_pitch_lags_FIX.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/silk/fixed/find_pred_coefs_FIX.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/silk/fixed/noise_shape_analysis_FIX.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/silk/fixed/process_gains_FIX.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/silk/fixed/regularize_correlations_FIX.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/silk/fixed/residual_energy16_FIX.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/silk/fixed/residual_energy_FIX.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/silk/fixed/warped_autocorrelation_FIX.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/silk/fixed/apply_sine_window_FIX.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/silk/fixed/autocorr_FIX.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/silk/fixed/burg_modified_FIX.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/silk/fixed/k2a_FIX.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/silk/fixed/k2a_Q16_FIX.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/silk/fixed/pitch_analysis_core_FIX.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/silk/fixed/vector_ops_FIX.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/silk/fixed/schur64_FIX.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/silk/fixed/schur_FIX.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus/silk/arm/arm_silk_map.c"], "include_dirs": ["include"]}, "78__esp-opus-encoder": {"alias": "idf::78__esp-opus-encoder", "target": "___idf_78__esp-opus-encoder", "prefix": "idf", "dir": "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus-encoder", "type": "LIBRARY", "lib": "__idf_78__esp-opus-encoder", "reqs": [], "priv_reqs": ["78__esp-opus"], "managed_reqs": [], "managed_priv_reqs": ["78__esp-opus"], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/78__esp-opus-encoder/lib78__esp-opus-encoder.a", "sources": ["/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus-encoder/opus_encoder.cc", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus-encoder/opus_decoder.cc", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus-encoder/opus_resampler.cc"], "include_dirs": ["include"]}, "78__esp-wifi-connect": {"alias": "idf::78__esp-wifi-connect", "target": "___idf_78__esp-wifi-connect", "prefix": "idf", "dir": "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-wifi-connect", "type": "LIBRARY", "lib": "__idf_78__esp-wifi-connect", "reqs": ["esp_timer", "esp_http_server", "esp_wifi", "nvs_flash", "json"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/78__esp-wifi-connect/lib78__esp-wifi-connect.a", "sources": ["/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-wifi-connect/wifi_configuration_ap.cc", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-wifi-connect/wifi_station.cc", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-wifi-connect/ssid_manager.cc", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-wifi-connect/dns_server.cc", "/home/<USER>/bysx/xiaozhi-esp32/build/wifi_configuration.html.S", "/home/<USER>/bysx/xiaozhi-esp32/build/wifi_configuration_done.html.S"], "include_dirs": ["include"]}, "78__esp_lcd_nv3023": {"alias": "idf::78__esp_lcd_nv3023", "target": "___idf_78__esp_lcd_nv3023", "prefix": "idf", "dir": "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp_lcd_nv3023", "type": "LIBRARY", "lib": "__idf_78__esp_lcd_nv3023", "reqs": ["esp_lcd"], "priv_reqs": ["driver", "espressif__cmake_utilities"], "managed_reqs": [], "managed_priv_reqs": ["espressif__cmake_utilities"], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/78__esp_lcd_nv3023/lib78__esp_lcd_nv3023.a", "sources": ["/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp_lcd_nv3023/esp_lcd_nv3023.c"], "include_dirs": ["include"]}, "78__xiaozhi-fonts": {"alias": "idf::78__xiaozhi-fonts", "target": "___idf_78__xiaozhi-fonts", "prefix": "idf", "dir": "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__xiaozhi-fonts", "type": "LIBRARY", "lib": "__idf_78__xiaozhi-fonts", "reqs": [], "priv_reqs": ["lvgl__lvgl"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/78__xiaozhi-fonts/lib78__xiaozhi-fonts.a", "sources": ["/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__xiaozhi-fonts/src/font_awesome_14_1.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__xiaozhi-fonts/src/font_awesome_16_4.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__xiaozhi-fonts/src/font_awesome_20_4.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__xiaozhi-fonts/src/font_awesome_30_1.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__xiaozhi-fonts/src/font_awesome_30_4.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__xiaozhi-fonts/src/font_emoji_32.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__xiaozhi-fonts/src/font_emoji_64.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__xiaozhi-fonts/src/font_puhui_14_1.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__xiaozhi-fonts/src/font_puhui_16_4.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__xiaozhi-fonts/src/font_puhui_20_4.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__xiaozhi-fonts/src/font_puhui_30_4.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__xiaozhi-fonts/src/emoji/emoji_1f602_32.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__xiaozhi-fonts/src/emoji/emoji_1f602_64.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__xiaozhi-fonts/src/emoji/emoji_1f606_32.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__xiaozhi-fonts/src/emoji/emoji_1f606_64.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__xiaozhi-fonts/src/emoji/emoji_1f609_32.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__xiaozhi-fonts/src/emoji/emoji_1f609_64.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__xiaozhi-fonts/src/emoji/emoji_1f60c_32.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__xiaozhi-fonts/src/emoji/emoji_1f60c_64.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__xiaozhi-fonts/src/emoji/emoji_1f60d_32.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__xiaozhi-fonts/src/emoji/emoji_1f60d_64.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__xiaozhi-fonts/src/emoji/emoji_1f60e_32.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__xiaozhi-fonts/src/emoji/emoji_1f60e_64.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__xiaozhi-fonts/src/emoji/emoji_1f60f_32.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__xiaozhi-fonts/src/emoji/emoji_1f60f_64.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__xiaozhi-fonts/src/emoji/emoji_1f614_32.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__xiaozhi-fonts/src/emoji/emoji_1f614_64.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__xiaozhi-fonts/src/emoji/emoji_1f618_32.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__xiaozhi-fonts/src/emoji/emoji_1f618_64.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__xiaozhi-fonts/src/emoji/emoji_1f61c_32.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__xiaozhi-fonts/src/emoji/emoji_1f61c_64.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__xiaozhi-fonts/src/emoji/emoji_1f620_32.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__xiaozhi-fonts/src/emoji/emoji_1f620_64.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__xiaozhi-fonts/src/emoji/emoji_1f62d_32.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__xiaozhi-fonts/src/emoji/emoji_1f62d_64.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__xiaozhi-fonts/src/emoji/emoji_1f62f_32.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__xiaozhi-fonts/src/emoji/emoji_1f62f_64.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__xiaozhi-fonts/src/emoji/emoji_1f631_32.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__xiaozhi-fonts/src/emoji/emoji_1f631_64.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__xiaozhi-fonts/src/emoji/emoji_1f633_32.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__xiaozhi-fonts/src/emoji/emoji_1f633_64.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__xiaozhi-fonts/src/emoji/emoji_1f634_32.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__xiaozhi-fonts/src/emoji/emoji_1f634_64.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__xiaozhi-fonts/src/emoji/emoji_1f636_32.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__xiaozhi-fonts/src/emoji/emoji_1f636_64.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__xiaozhi-fonts/src/emoji/emoji_1f642_32.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__xiaozhi-fonts/src/emoji/emoji_1f642_64.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__xiaozhi-fonts/src/emoji/emoji_1f644_32.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__xiaozhi-fonts/src/emoji/emoji_1f644_64.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__xiaozhi-fonts/src/emoji/emoji_1f914_32.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__xiaozhi-fonts/src/emoji/emoji_1f914_64.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__xiaozhi-fonts/src/emoji/emoji_1f924_32.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__xiaozhi-fonts/src/emoji/emoji_1f924_64.c"], "include_dirs": ["include"]}, "app_trace": {"alias": "idf::app_trace", "target": "___idf_app_trace", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/app_trace", "type": "LIBRARY", "lib": "__idf_app_trace", "reqs": ["esp_timer"], "priv_reqs": ["esp_driver_gptimer", "esp_driver_gpio", "esp_driver_uart"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/app_trace/libapp_trace.a", "sources": ["/home/<USER>/esp32/esp-idf/components/app_trace/app_trace.c", "/home/<USER>/esp32/esp-idf/components/app_trace/app_trace_util.c", "/home/<USER>/esp32/esp-idf/components/app_trace/host_file_io.c", "/home/<USER>/esp32/esp-idf/components/app_trace/port/port_uart.c"], "include_dirs": ["include"]}, "app_update": {"alias": "idf::app_update", "target": "___idf_app_update", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/app_update", "type": "LIBRARY", "lib": "__idf_app_update", "reqs": ["partition_table", "bootloader_support", "esp_app_format", "esp_bootloader_format", "esp_partition"], "priv_reqs": ["esptool_py", "efuse", "spi_flash"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/app_update/libapp_update.a", "sources": ["/home/<USER>/esp32/esp-idf/components/app_update/esp_ota_ops.c", "/home/<USER>/esp32/esp-idf/components/app_update/esp_ota_app_desc.c"], "include_dirs": ["include"]}, "bootloader": {"alias": "idf::bootloader", "target": "___idf_bootloader", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/bootloader", "type": "CONFIG_ONLY", "lib": "__idf_bootloader", "reqs": [], "priv_reqs": ["partition_table", "esptool_py"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": []}, "bootloader_support": {"alias": "idf::bootloader_support", "target": "___idf_bootloader_support", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/bootloader_support", "type": "LIBRARY", "lib": "__idf_bootloader_support", "reqs": ["soc"], "priv_reqs": ["spi_flash", "mbedtls", "efuse", "heap", "esp_bootloader_format", "esp_app_format"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/bootloader_support/libbootloader_support.a", "sources": ["/home/<USER>/esp32/esp-idf/components/bootloader_support/src/bootloader_common.c", "/home/<USER>/esp32/esp-idf/components/bootloader_support/src/bootloader_common_loader.c", "/home/<USER>/esp32/esp-idf/components/bootloader_support/src/bootloader_clock_init.c", "/home/<USER>/esp32/esp-idf/components/bootloader_support/src/bootloader_mem.c", "/home/<USER>/esp32/esp-idf/components/bootloader_support/src/bootloader_random.c", "/home/<USER>/esp32/esp-idf/components/bootloader_support/src/bootloader_efuse.c", "/home/<USER>/esp32/esp-idf/components/bootloader_support/src/flash_encrypt.c", "/home/<USER>/esp32/esp-idf/components/bootloader_support/src/secure_boot.c", "/home/<USER>/esp32/esp-idf/components/bootloader_support/src/bootloader_random_esp32s3.c", "/home/<USER>/esp32/esp-idf/components/bootloader_support/bootloader_flash/src/bootloader_flash.c", "/home/<USER>/esp32/esp-idf/components/bootloader_support/bootloader_flash/src/flash_qio_mode.c", "/home/<USER>/esp32/esp-idf/components/bootloader_support/bootloader_flash/src/bootloader_flash_config_esp32s3.c", "/home/<USER>/esp32/esp-idf/components/bootloader_support/src/bootloader_utility.c", "/home/<USER>/esp32/esp-idf/components/bootloader_support/src/flash_partitions.c", "/home/<USER>/esp32/esp-idf/components/bootloader_support/src/esp_image_format.c", "/home/<USER>/esp32/esp-idf/components/bootloader_support/src/idf/bootloader_sha.c", "/home/<USER>/esp32/esp-idf/components/bootloader_support/src/esp32s3/secure_boot_secure_features.c"], "include_dirs": ["include", "bootloader_flash/include"]}, "bt": {"alias": "idf::bt", "target": "___idf_bt", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/bt", "type": "CONFIG_ONLY", "lib": "__idf_bt", "reqs": ["esp_timer", "esp_wifi"], "priv_reqs": ["nvs_flash", "soc", "esp_pm", "esp_phy", "esp_coex", "mbedtls", "esp_driver_uart", "vfs", "esp_ringbuf", "esp_driver_spi", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": []}, "cmock": {"alias": "idf::cmock", "target": "___idf_cmock", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/cmock", "type": "LIBRARY", "lib": "__idf_cmock", "reqs": ["unity"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/cmock/libcmock.a", "sources": ["/home/<USER>/esp32/esp-idf/components/cmock/CMock/src/cmock.c"], "include_dirs": ["CMock/src"]}, "console": {"alias": "idf::console", "target": "___idf_console", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/console", "type": "LIBRARY", "lib": "__idf_console", "reqs": ["vfs", "esp_vfs_console"], "priv_reqs": ["esp_driver_uart", "esp_driver_usb_serial_jtag"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/console/libconsole.a", "sources": ["/home/<USER>/esp32/esp-idf/components/console/commands.c", "/home/<USER>/esp32/esp-idf/components/console/esp_console_common.c", "/home/<USER>/esp32/esp-idf/components/console/split_argv.c", "/home/<USER>/esp32/esp-idf/components/console/linenoise/linenoise.c", "/home/<USER>/esp32/esp-idf/components/console/esp_console_repl_chip.c", "/home/<USER>/esp32/esp-idf/components/console/argtable3/arg_cmd.c", "/home/<USER>/esp32/esp-idf/components/console/argtable3/arg_date.c", "/home/<USER>/esp32/esp-idf/components/console/argtable3/arg_dbl.c", "/home/<USER>/esp32/esp-idf/components/console/argtable3/arg_dstr.c", "/home/<USER>/esp32/esp-idf/components/console/argtable3/arg_end.c", "/home/<USER>/esp32/esp-idf/components/console/argtable3/arg_file.c", "/home/<USER>/esp32/esp-idf/components/console/argtable3/arg_hashtable.c", "/home/<USER>/esp32/esp-idf/components/console/argtable3/arg_int.c", "/home/<USER>/esp32/esp-idf/components/console/argtable3/arg_lit.c", "/home/<USER>/esp32/esp-idf/components/console/argtable3/arg_rem.c", "/home/<USER>/esp32/esp-idf/components/console/argtable3/arg_rex.c", "/home/<USER>/esp32/esp-idf/components/console/argtable3/arg_str.c", "/home/<USER>/esp32/esp-idf/components/console/argtable3/arg_utils.c", "/home/<USER>/esp32/esp-idf/components/console/argtable3/argtable3.c"], "include_dirs": ["/home/<USER>/esp32/esp-idf/components/console"]}, "cxx": {"alias": "idf::cxx", "target": "___idf_cxx", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/cxx", "type": "LIBRARY", "lib": "__idf_cxx", "reqs": [], "priv_reqs": ["pthread", "esp_system"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/cxx/libcxx.a", "sources": ["/home/<USER>/esp32/esp-idf/components/cxx/cxx_exception_stubs.cpp", "/home/<USER>/esp32/esp-idf/components/cxx/cxx_guards.cpp", "/home/<USER>/esp32/esp-idf/components/cxx/cxx_init.cpp"], "include_dirs": []}, "driver": {"alias": "idf::driver", "target": "___idf_driver", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/driver", "type": "LIBRARY", "lib": "__idf_driver", "reqs": ["esp_pm", "esp_ringbuf", "freertos", "soc", "hal", "esp_hw_support", "esp_driver_gpio", "esp_driver_pcnt", "esp_driver_gptimer", "esp_driver_spi", "esp_driver_mcpwm", "esp_driver_ana_cmpr", "esp_driver_i2s", "esp_driver_sdmmc", "esp_driver_sdspi", "esp_driver_sdio", "esp_driver_dac", "esp_driver_rmt", "esp_driver_tsens", "esp_driver_sdm", "esp_driver_i2c", "esp_driver_uart", "esp_driver_ledc", "esp_driver_parlio", "esp_driver_usb_serial_jtag"], "priv_reqs": ["efuse", "esp_timer", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/driver/libdriver.a", "sources": ["/home/<USER>/esp32/esp-idf/components/driver/deprecated/adc_legacy.c", "/home/<USER>/esp32/esp-idf/components/driver/deprecated/adc_dma_legacy.c", "/home/<USER>/esp32/esp-idf/components/driver/deprecated/timer_legacy.c", "/home/<USER>/esp32/esp-idf/components/driver/i2c/i2c.c", "/home/<USER>/esp32/esp-idf/components/driver/deprecated/i2s_legacy.c", "/home/<USER>/esp32/esp-idf/components/driver/deprecated/mcpwm_legacy.c", "/home/<USER>/esp32/esp-idf/components/driver/deprecated/pcnt_legacy.c", "/home/<USER>/esp32/esp-idf/components/driver/deprecated/rmt_legacy.c", "/home/<USER>/esp32/esp-idf/components/driver/deprecated/sigma_delta_legacy.c", "/home/<USER>/esp32/esp-idf/components/driver/deprecated/rtc_temperature_legacy.c", "/home/<USER>/esp32/esp-idf/components/driver/touch_sensor/touch_sensor_common.c", "/home/<USER>/esp32/esp-idf/components/driver/touch_sensor/esp32s3/touch_sensor.c", "/home/<USER>/esp32/esp-idf/components/driver/twai/twai.c"], "include_dirs": ["deprecated", "i2c/include", "touch_sensor/include", "twai/include", "touch_sensor/esp32s3/include"]}, "efuse": {"alias": "idf::efuse", "target": "___idf_efuse", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/efuse", "type": "LIBRARY", "lib": "__idf_efuse", "reqs": [], "priv_reqs": ["bootloader_support", "soc", "spi_flash", "esp_system", "esp_partition", "esp_app_format"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/efuse/libefuse.a", "sources": ["/home/<USER>/esp32/esp-idf/components/efuse/esp32s3/esp_efuse_table.c", "/home/<USER>/esp32/esp-idf/components/efuse/esp32s3/esp_efuse_fields.c", "/home/<USER>/esp32/esp-idf/components/efuse/esp32s3/esp_efuse_rtc_calib.c", "/home/<USER>/esp32/esp-idf/components/efuse/esp32s3/esp_efuse_utility.c", "/home/<USER>/esp32/esp-idf/components/efuse/src/esp_efuse_api.c", "/home/<USER>/esp32/esp-idf/components/efuse/src/esp_efuse_fields.c", "/home/<USER>/esp32/esp-idf/components/efuse/src/esp_efuse_utility.c", "/home/<USER>/esp32/esp-idf/components/efuse/src/efuse_controller/keys/with_key_purposes/esp_efuse_api_key.c", "/home/<USER>/esp32/esp-idf/components/efuse/src/esp_efuse_startup.c"], "include_dirs": ["include", "esp32s3/include"]}, "esp-tls": {"alias": "idf::esp-tls", "target": "___idf_esp-tls", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp-tls", "type": "LIBRARY", "lib": "__idf_esp-tls", "reqs": ["mbedtls"], "priv_reqs": ["http_parser", "esp_timer", "lwip"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/esp-tls/libesp-tls.a", "sources": ["/home/<USER>/esp32/esp-idf/components/esp-tls/esp_tls.c", "/home/<USER>/esp32/esp-idf/components/esp-tls/esp-tls-crypto/esp_tls_crypto.c", "/home/<USER>/esp32/esp-idf/components/esp-tls/esp_tls_error_capture.c", "/home/<USER>/esp32/esp-idf/components/esp-tls/esp_tls_platform_port.c", "/home/<USER>/esp32/esp-idf/components/esp-tls/esp_tls_mbedtls.c"], "include_dirs": ["/home/<USER>/esp32/esp-idf/components/esp-tls", "esp-tls-crypto"]}, "esp_adc": {"alias": "idf::esp_adc", "target": "___idf_esp_adc", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_adc", "type": "LIBRARY", "lib": "__idf_esp_adc", "reqs": [], "priv_reqs": ["driver", "esp_driver_gpio", "efuse", "esp_pm", "esp_ringbuf", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/esp_adc/libesp_adc.a", "sources": ["/home/<USER>/esp32/esp-idf/components/esp_adc/adc_oneshot.c", "/home/<USER>/esp32/esp-idf/components/esp_adc/adc_common.c", "/home/<USER>/esp32/esp-idf/components/esp_adc/adc_cali.c", "/home/<USER>/esp32/esp-idf/components/esp_adc/adc_cali_curve_fitting.c", "/home/<USER>/esp32/esp-idf/components/esp_adc/deprecated/esp_adc_cal_common_legacy.c", "/home/<USER>/esp32/esp-idf/components/esp_adc/adc_continuous.c", "/home/<USER>/esp32/esp-idf/components/esp_adc/adc_monitor.c", "/home/<USER>/esp32/esp-idf/components/esp_adc/gdma/adc_dma.c", "/home/<USER>/esp32/esp-idf/components/esp_adc/adc_filter.c", "/home/<USER>/esp32/esp-idf/components/esp_adc/esp32s3/curve_fitting_coefficients.c", "/home/<USER>/esp32/esp-idf/components/esp_adc/deprecated/esp32s3/esp_adc_cal_legacy.c"], "include_dirs": ["include", "interface", "esp32s3/include", "deprecated/include"]}, "esp_app_format": {"alias": "idf::esp_app_format", "target": "___idf_esp_app_format", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_app_format", "type": "LIBRARY", "lib": "__idf_esp_app_format", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/esp_app_format/libesp_app_format.a", "sources": ["/home/<USER>/esp32/esp-idf/components/esp_app_format/esp_app_desc.c"], "include_dirs": ["include"]}, "esp_bootloader_format": {"alias": "idf::esp_bootloader_format", "target": "___idf_esp_bootloader_format", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_bootloader_format", "type": "LIBRARY", "lib": "__idf_esp_bootloader_format", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/esp_bootloader_format/libesp_bootloader_format.a", "sources": ["/home/<USER>/esp32/esp-idf/components/esp_bootloader_format/esp_bootloader_desc.c"], "include_dirs": ["include"]}, "esp_coex": {"alias": "idf::esp_coex", "target": "___idf_esp_coex", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_coex", "type": "LIBRARY", "lib": "__idf_esp_coex", "reqs": [], "priv_reqs": ["esp_timer", "driver", "esp_event"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/esp_coex/libesp_coex.a", "sources": ["/home/<USER>/esp32/esp-idf/components/esp_coex/esp32s3/esp_coex_adapter.c", "/home/<USER>/esp32/esp-idf/components/esp_coex/src/coexist_debug_diagram.c", "/home/<USER>/esp32/esp-idf/components/esp_coex/src/coexist_debug.c"], "include_dirs": ["include"]}, "esp_common": {"alias": "idf::esp_common", "target": "___idf_esp_common", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_common", "type": "LIBRARY", "lib": "__idf_esp_common", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/esp_common/libesp_common.a", "sources": ["/home/<USER>/esp32/esp-idf/components/esp_common/src/esp_err_to_name.c"], "include_dirs": ["include"]}, "esp_driver_ana_cmpr": {"alias": "idf::esp_driver_ana_cmpr", "target": "___idf_esp_driver_ana_cmpr", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_driver_ana_cmpr", "type": "CONFIG_ONLY", "lib": "__idf_esp_driver_ana_cmpr", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["include"]}, "esp_driver_cam": {"alias": "idf::esp_driver_cam", "target": "___idf_esp_driver_cam", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_driver_cam", "type": "LIBRARY", "lib": "__idf_esp_driver_cam", "reqs": ["esp_driver_isp", "esp_mm"], "priv_reqs": ["esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/esp_driver_cam/libesp_driver_cam.a", "sources": ["/home/<USER>/esp32/esp-idf/components/esp_driver_cam/esp_cam_ctlr.c", "/home/<USER>/esp32/esp-idf/components/esp_driver_cam/dvp_share_ctrl.c"], "include_dirs": ["include", "interface"]}, "esp_driver_dac": {"alias": "idf::esp_driver_dac", "target": "___idf_esp_driver_dac", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_driver_dac", "type": "CONFIG_ONLY", "lib": "__idf_esp_driver_dac", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["./include"]}, "esp_driver_gpio": {"alias": "idf::esp_driver_gpio", "target": "___idf_esp_driver_gpio", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_driver_gpio", "type": "LIBRARY", "lib": "__idf_esp_driver_gpio", "reqs": [], "priv_reqs": ["esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/esp_driver_gpio/libesp_driver_gpio.a", "sources": ["/home/<USER>/esp32/esp-idf/components/esp_driver_gpio/src/gpio.c", "/home/<USER>/esp32/esp-idf/components/esp_driver_gpio/src/gpio_glitch_filter_ops.c", "/home/<USER>/esp32/esp-idf/components/esp_driver_gpio/src/rtc_io.c", "/home/<USER>/esp32/esp-idf/components/esp_driver_gpio/src/dedic_gpio.c", "/home/<USER>/esp32/esp-idf/components/esp_driver_gpio/src/gpio_pin_glitch_filter.c"], "include_dirs": ["include"]}, "esp_driver_gptimer": {"alias": "idf::esp_driver_gptimer", "target": "___idf_esp_driver_gptimer", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_driver_gptimer", "type": "LIBRARY", "lib": "__idf_esp_driver_gptimer", "reqs": ["esp_pm"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/esp_driver_gptimer/libesp_driver_gptimer.a", "sources": ["/home/<USER>/esp32/esp-idf/components/esp_driver_gptimer/src/gptimer.c", "/home/<USER>/esp32/esp-idf/components/esp_driver_gptimer/src/gptimer_common.c"], "include_dirs": ["include"]}, "esp_driver_i2c": {"alias": "idf::esp_driver_i2c", "target": "___idf_esp_driver_i2c", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_driver_i2c", "type": "LIBRARY", "lib": "__idf_esp_driver_i2c", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_pm", "esp_ringbuf"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/esp_driver_i2c/libesp_driver_i2c.a", "sources": ["/home/<USER>/esp32/esp-idf/components/esp_driver_i2c/i2c_master.c", "/home/<USER>/esp32/esp-idf/components/esp_driver_i2c/i2c_common.c", "/home/<USER>/esp32/esp-idf/components/esp_driver_i2c/i2c_slave.c"], "include_dirs": ["include"]}, "esp_driver_i2s": {"alias": "idf::esp_driver_i2s", "target": "___idf_esp_driver_i2s", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_driver_i2s", "type": "LIBRARY", "lib": "__idf_esp_driver_i2s", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_pm", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/esp_driver_i2s/libesp_driver_i2s.a", "sources": ["/home/<USER>/esp32/esp-idf/components/esp_driver_i2s/i2s_common.c", "/home/<USER>/esp32/esp-idf/components/esp_driver_i2s/i2s_std.c", "/home/<USER>/esp32/esp-idf/components/esp_driver_i2s/i2s_pdm.c", "/home/<USER>/esp32/esp-idf/components/esp_driver_i2s/i2s_tdm.c", "/home/<USER>/esp32/esp-idf/components/esp_driver_i2s/i2s_platform.c"], "include_dirs": ["include"]}, "esp_driver_isp": {"alias": "idf::esp_driver_isp", "target": "___idf_esp_driver_isp", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_driver_isp", "type": "CONFIG_ONLY", "lib": "__idf_esp_driver_isp", "reqs": ["esp_mm"], "priv_reqs": ["esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["include"]}, "esp_driver_jpeg": {"alias": "idf::esp_driver_jpeg", "target": "___idf_esp_driver_jpeg", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_driver_jpeg", "type": "CONFIG_ONLY", "lib": "__idf_esp_driver_jpeg", "reqs": [], "priv_reqs": ["esp_mm", "esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["include"]}, "esp_driver_ledc": {"alias": "idf::esp_driver_ledc", "target": "___idf_esp_driver_ledc", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_driver_ledc", "type": "LIBRARY", "lib": "__idf_esp_driver_ledc", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/esp_driver_ledc/libesp_driver_ledc.a", "sources": ["/home/<USER>/esp32/esp-idf/components/esp_driver_ledc/src/ledc.c"], "include_dirs": ["include"]}, "esp_driver_mcpwm": {"alias": "idf::esp_driver_mcpwm", "target": "___idf_esp_driver_mcpwm", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_driver_mcpwm", "type": "LIBRARY", "lib": "__idf_esp_driver_mcpwm", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/esp_driver_mcpwm/libesp_driver_mcpwm.a", "sources": ["/home/<USER>/esp32/esp-idf/components/esp_driver_mcpwm/src/mcpwm_cap.c", "/home/<USER>/esp32/esp-idf/components/esp_driver_mcpwm/src/mcpwm_cmpr.c", "/home/<USER>/esp32/esp-idf/components/esp_driver_mcpwm/src/mcpwm_com.c", "/home/<USER>/esp32/esp-idf/components/esp_driver_mcpwm/src/mcpwm_fault.c", "/home/<USER>/esp32/esp-idf/components/esp_driver_mcpwm/src/mcpwm_gen.c", "/home/<USER>/esp32/esp-idf/components/esp_driver_mcpwm/src/mcpwm_oper.c", "/home/<USER>/esp32/esp-idf/components/esp_driver_mcpwm/src/mcpwm_sync.c", "/home/<USER>/esp32/esp-idf/components/esp_driver_mcpwm/src/mcpwm_timer.c"], "include_dirs": ["include"]}, "esp_driver_parlio": {"alias": "idf::esp_driver_parlio", "target": "___idf_esp_driver_parlio", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_driver_parlio", "type": "CONFIG_ONLY", "lib": "__idf_esp_driver_parlio", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["include"]}, "esp_driver_pcnt": {"alias": "idf::esp_driver_pcnt", "target": "___idf_esp_driver_pcnt", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_driver_pcnt", "type": "LIBRARY", "lib": "__idf_esp_driver_pcnt", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/esp_driver_pcnt/libesp_driver_pcnt.a", "sources": ["/home/<USER>/esp32/esp-idf/components/esp_driver_pcnt/src/pulse_cnt.c"], "include_dirs": ["include"]}, "esp_driver_ppa": {"alias": "idf::esp_driver_ppa", "target": "___idf_esp_driver_ppa", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_driver_ppa", "type": "CONFIG_ONLY", "lib": "__idf_esp_driver_ppa", "reqs": [], "priv_reqs": ["esp_mm", "esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["include"]}, "esp_driver_rmt": {"alias": "idf::esp_driver_rmt", "target": "___idf_esp_driver_rmt", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_driver_rmt", "type": "LIBRARY", "lib": "__idf_esp_driver_rmt", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/esp_driver_rmt/libesp_driver_rmt.a", "sources": ["/home/<USER>/esp32/esp-idf/components/esp_driver_rmt/src/rmt_common.c", "/home/<USER>/esp32/esp-idf/components/esp_driver_rmt/src/rmt_encoder.c", "/home/<USER>/esp32/esp-idf/components/esp_driver_rmt/src/rmt_rx.c", "/home/<USER>/esp32/esp-idf/components/esp_driver_rmt/src/rmt_tx.c"], "include_dirs": ["include"]}, "esp_driver_sdio": {"alias": "idf::esp_driver_sdio", "target": "___idf_esp_driver_sdio", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_driver_sdio", "type": "CONFIG_ONLY", "lib": "__idf_esp_driver_sdio", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_ringbuf"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["include"]}, "esp_driver_sdm": {"alias": "idf::esp_driver_sdm", "target": "___idf_esp_driver_sdm", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_driver_sdm", "type": "LIBRARY", "lib": "__idf_esp_driver_sdm", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/esp_driver_sdm/libesp_driver_sdm.a", "sources": ["/home/<USER>/esp32/esp-idf/components/esp_driver_sdm/src/sdm.c"], "include_dirs": ["include"]}, "esp_driver_sdmmc": {"alias": "idf::esp_driver_sdmmc", "target": "___idf_esp_driver_sdmmc", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_driver_sdmmc", "type": "LIBRARY", "lib": "__idf_esp_driver_sdmmc", "reqs": ["sdmmc", "esp_driver_gpio"], "priv_reqs": ["esp_timer", "esp_pm", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/esp_driver_sdmmc/libesp_driver_sdmmc.a", "sources": ["/home/<USER>/esp32/esp-idf/components/esp_driver_sdmmc/src/sdmmc_transaction.c", "/home/<USER>/esp32/esp-idf/components/esp_driver_sdmmc/src/sdmmc_host.c"], "include_dirs": ["include"]}, "esp_driver_sdspi": {"alias": "idf::esp_driver_sdspi", "target": "___idf_esp_driver_sdspi", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_driver_sdspi", "type": "LIBRARY", "lib": "__idf_esp_driver_sdspi", "reqs": ["sdmmc", "esp_driver_spi", "esp_driver_gpio"], "priv_reqs": ["esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/esp_driver_sdspi/libesp_driver_sdspi.a", "sources": ["/home/<USER>/esp32/esp-idf/components/esp_driver_sdspi/src/sdspi_crc.c", "/home/<USER>/esp32/esp-idf/components/esp_driver_sdspi/src/sdspi_host.c", "/home/<USER>/esp32/esp-idf/components/esp_driver_sdspi/src/sdspi_transaction.c"], "include_dirs": ["include"]}, "esp_driver_spi": {"alias": "idf::esp_driver_spi", "target": "___idf_esp_driver_spi", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_driver_spi", "type": "LIBRARY", "lib": "__idf_esp_driver_spi", "reqs": ["esp_pm"], "priv_reqs": ["esp_timer", "esp_mm", "esp_driver_gpio", "esp_ringbuf"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/esp_driver_spi/libesp_driver_spi.a", "sources": ["/home/<USER>/esp32/esp-idf/components/esp_driver_spi/src/gpspi/spi_common.c", "/home/<USER>/esp32/esp-idf/components/esp_driver_spi/src/gpspi/spi_master.c", "/home/<USER>/esp32/esp-idf/components/esp_driver_spi/src/gpspi/spi_slave.c", "/home/<USER>/esp32/esp-idf/components/esp_driver_spi/src/gpspi/spi_dma.c", "/home/<USER>/esp32/esp-idf/components/esp_driver_spi/src/gpspi/spi_slave_hd.c"], "include_dirs": ["include"]}, "esp_driver_touch_sens": {"alias": "idf::esp_driver_touch_sens", "target": "___idf_esp_driver_touch_sens", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_driver_touch_sens", "type": "CONFIG_ONLY", "lib": "__idf_esp_driver_touch_sens", "reqs": [], "priv_reqs": ["esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": []}, "esp_driver_tsens": {"alias": "idf::esp_driver_tsens", "target": "___idf_esp_driver_tsens", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_driver_tsens", "type": "LIBRARY", "lib": "__idf_esp_driver_tsens", "reqs": [], "priv_reqs": ["efuse"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/esp_driver_tsens/libesp_driver_tsens.a", "sources": ["/home/<USER>/esp32/esp-idf/components/esp_driver_tsens/src/temperature_sensor.c"], "include_dirs": ["include"]}, "esp_driver_uart": {"alias": "idf::esp_driver_uart", "target": "___idf_esp_driver_uart", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_driver_uart", "type": "LIBRARY", "lib": "__idf_esp_driver_uart", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio", "esp_ringbuf"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/esp_driver_uart/libesp_driver_uart.a", "sources": ["/home/<USER>/esp32/esp-idf/components/esp_driver_uart/src/uart.c", "/home/<USER>/esp32/esp-idf/components/esp_driver_uart/src/uart_vfs.c"], "include_dirs": ["include"]}, "esp_driver_usb_serial_jtag": {"alias": "idf::esp_driver_usb_serial_jtag", "target": "___idf_esp_driver_usb_serial_jtag", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_driver_usb_serial_jtag", "type": "LIBRARY", "lib": "__idf_esp_driver_usb_serial_jtag", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_ringbuf", "esp_pm", "esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/esp_driver_usb_serial_jtag/libesp_driver_usb_serial_jtag.a", "sources": ["/home/<USER>/esp32/esp-idf/components/esp_driver_usb_serial_jtag/src/usb_serial_jtag.c", "/home/<USER>/esp32/esp-idf/components/esp_driver_usb_serial_jtag/src/usb_serial_jtag_connection_monitor.c", "/home/<USER>/esp32/esp-idf/components/esp_driver_usb_serial_jtag/src/usb_serial_jtag_vfs.c"], "include_dirs": ["include"]}, "esp_eth": {"alias": "idf::esp_eth", "target": "___idf_esp_eth", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_eth", "type": "LIBRARY", "lib": "__idf_esp_eth", "reqs": ["esp_event"], "priv_reqs": ["log", "esp_timer", "esp_driver_spi", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/esp_eth/libesp_eth.a", "sources": ["/home/<USER>/esp32/esp-idf/components/esp_eth/src/esp_eth.c", "/home/<USER>/esp32/esp-idf/components/esp_eth/src/phy/esp_eth_phy_802_3.c", "/home/<USER>/esp32/esp-idf/components/esp_eth/src/esp_eth_netif_glue.c"], "include_dirs": ["include"]}, "esp_event": {"alias": "idf::esp_event", "target": "___idf_esp_event", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_event", "type": "LIBRARY", "lib": "__idf_esp_event", "reqs": ["log", "esp_common", "freertos"], "priv_reqs": ["esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/esp_event/libesp_event.a", "sources": ["/home/<USER>/esp32/esp-idf/components/esp_event/default_event_loop.c", "/home/<USER>/esp32/esp-idf/components/esp_event/esp_event.c", "/home/<USER>/esp32/esp-idf/components/esp_event/esp_event_private.c"], "include_dirs": ["include"]}, "esp_gdbstub": {"alias": "idf::esp_gdbstub", "target": "___idf_esp_gdbstub", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_gdbstub", "type": "LIBRARY", "lib": "__idf_esp_gdbstub", "reqs": ["freertos"], "priv_reqs": ["soc", "esp_rom", "esp_system"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/esp_gdbstub/libesp_gdbstub.a", "sources": ["/home/<USER>/esp32/esp-idf/components/esp_gdbstub/src/gdbstub.c", "/home/<USER>/esp32/esp-idf/components/esp_gdbstub/src/gdbstub_transport.c", "/home/<USER>/esp32/esp-idf/components/esp_gdbstub/src/packet.c", "/home/<USER>/esp32/esp-idf/components/esp_gdbstub/src/port/xtensa/gdbstub_xtensa.c", "/home/<USER>/esp32/esp-idf/components/esp_gdbstub/src/port/xtensa/gdbstub-entry.S", "/home/<USER>/esp32/esp-idf/components/esp_gdbstub/src/port/xtensa/xt_debugexception.S"], "include_dirs": ["include"]}, "esp_hid": {"alias": "idf::esp_hid", "target": "___idf_esp_hid", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_hid", "type": "LIBRARY", "lib": "__idf_esp_hid", "reqs": ["esp_event", "bt"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/esp_hid/libesp_hid.a", "sources": ["/home/<USER>/esp32/esp-idf/components/esp_hid/src/esp_hidd.c", "/home/<USER>/esp32/esp-idf/components/esp_hid/src/esp_hidh.c", "/home/<USER>/esp32/esp-idf/components/esp_hid/src/esp_hid_common.c"], "include_dirs": ["include"]}, "esp_http_client": {"alias": "idf::esp_http_client", "target": "___idf_esp_http_client", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_http_client", "type": "LIBRARY", "lib": "__idf_esp_http_client", "reqs": ["lwip", "esp_event"], "priv_reqs": ["tcp_transport", "http_parser"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/esp_http_client/libesp_http_client.a", "sources": ["/home/<USER>/esp32/esp-idf/components/esp_http_client/esp_http_client.c", "/home/<USER>/esp32/esp-idf/components/esp_http_client/lib/http_auth.c", "/home/<USER>/esp32/esp-idf/components/esp_http_client/lib/http_header.c", "/home/<USER>/esp32/esp-idf/components/esp_http_client/lib/http_utils.c"], "include_dirs": ["include"]}, "esp_http_server": {"alias": "idf::esp_http_server", "target": "___idf_esp_http_server", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_http_server", "type": "LIBRARY", "lib": "__idf_esp_http_server", "reqs": ["http_parser", "esp_event"], "priv_reqs": ["mbedtls", "lwip", "esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/esp_http_server/libesp_http_server.a", "sources": ["/home/<USER>/esp32/esp-idf/components/esp_http_server/src/httpd_main.c", "/home/<USER>/esp32/esp-idf/components/esp_http_server/src/httpd_parse.c", "/home/<USER>/esp32/esp-idf/components/esp_http_server/src/httpd_sess.c", "/home/<USER>/esp32/esp-idf/components/esp_http_server/src/httpd_txrx.c", "/home/<USER>/esp32/esp-idf/components/esp_http_server/src/httpd_uri.c", "/home/<USER>/esp32/esp-idf/components/esp_http_server/src/httpd_ws.c", "/home/<USER>/esp32/esp-idf/components/esp_http_server/src/util/ctrl_sock.c"], "include_dirs": ["include"]}, "esp_https_ota": {"alias": "idf::esp_https_ota", "target": "___idf_esp_https_ota", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_https_ota", "type": "LIBRARY", "lib": "__idf_esp_https_ota", "reqs": ["esp_http_client", "bootloader_support", "esp_app_format", "esp_event"], "priv_reqs": ["log", "app_update"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/esp_https_ota/libesp_https_ota.a", "sources": ["/home/<USER>/esp32/esp-idf/components/esp_https_ota/src/esp_https_ota.c"], "include_dirs": ["include"]}, "esp_https_server": {"alias": "idf::esp_https_server", "target": "___idf_esp_https_server", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_https_server", "type": "LIBRARY", "lib": "__idf_esp_https_server", "reqs": ["esp_http_server", "esp-tls", "esp_event"], "priv_reqs": ["lwip"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/esp_https_server/libesp_https_server.a", "sources": ["/home/<USER>/esp32/esp-idf/components/esp_https_server/src/https_server.c"], "include_dirs": ["include"]}, "esp_hw_support": {"alias": "idf::esp_hw_support", "target": "___idf_esp_hw_support", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_hw_support", "type": "LIBRARY", "lib": "__idf_esp_hw_support", "reqs": ["soc"], "priv_reqs": ["efuse", "spi_flash", "bootloader_support", "esp_security", "esp_driver_gpio", "esp_timer", "esp_pm", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/esp_hw_support/libesp_hw_support.a", "sources": ["/home/<USER>/esp32/esp-idf/components/esp_hw_support/cpu.c", "/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/esp_cpu_intr.c", "/home/<USER>/esp32/esp-idf/components/esp_hw_support/esp_memory_utils.c", "/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/cpu_region_protect.c", "/home/<USER>/esp32/esp-idf/components/esp_hw_support/esp_clk.c", "/home/<USER>/esp32/esp-idf/components/esp_hw_support/clk_ctrl_os.c", "/home/<USER>/esp32/esp-idf/components/esp_hw_support/hw_random.c", "/home/<USER>/esp32/esp-idf/components/esp_hw_support/intr_alloc.c", "/home/<USER>/esp32/esp-idf/components/esp_hw_support/mac_addr.c", "/home/<USER>/esp32/esp-idf/components/esp_hw_support/periph_ctrl.c", "/home/<USER>/esp32/esp-idf/components/esp_hw_support/revision.c", "/home/<USER>/esp32/esp-idf/components/esp_hw_support/rtc_module.c", "/home/<USER>/esp32/esp-idf/components/esp_hw_support/sleep_modem.c", "/home/<USER>/esp32/esp-idf/components/esp_hw_support/sleep_modes.c", "/home/<USER>/esp32/esp-idf/components/esp_hw_support/sleep_console.c", "/home/<USER>/esp32/esp-idf/components/esp_hw_support/sleep_usb.c", "/home/<USER>/esp32/esp-idf/components/esp_hw_support/sleep_gpio.c", "/home/<USER>/esp32/esp-idf/components/esp_hw_support/sleep_event.c", "/home/<USER>/esp32/esp-idf/components/esp_hw_support/regi2c_ctrl.c", "/home/<USER>/esp32/esp-idf/components/esp_hw_support/esp_gpio_reserve.c", "/home/<USER>/esp32/esp-idf/components/esp_hw_support/sar_periph_ctrl_common.c", "/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/io_mux.c", "/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/esp_clk_tree.c", "/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp_clk_tree_common.c", "/home/<USER>/esp32/esp-idf/components/esp_hw_support/dma/esp_dma_utils.c", "/home/<USER>/esp32/esp-idf/components/esp_hw_support/dma/gdma_link.c", "/home/<USER>/esp32/esp-idf/components/esp_hw_support/spi_share_hw_ctrl.c", "/home/<USER>/esp32/esp-idf/components/esp_hw_support/spi_bus_lock.c", "/home/<USER>/esp32/esp-idf/components/esp_hw_support/clk_utils.c", "/home/<USER>/esp32/esp-idf/components/esp_hw_support/adc_share_hw_ctrl.c", "/home/<USER>/esp32/esp-idf/components/esp_hw_support/dma/gdma.c", "/home/<USER>/esp32/esp-idf/components/esp_hw_support/deprecated/gdma_legacy.c", "/home/<USER>/esp32/esp-idf/components/esp_hw_support/dma/esp_async_memcpy.c", "/home/<USER>/esp32/esp-idf/components/esp_hw_support/dma/async_memcpy_gdma.c", "/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/systimer.c", "/home/<USER>/esp32/esp-idf/components/esp_hw_support/mspi_timing_tuning.c", "/home/<USER>/esp32/esp-idf/components/esp_hw_support/mspi_timing_by_mspi_delay.c", "/home/<USER>/esp32/esp-idf/components/esp_hw_support/sleep_wake_stub.c", "/home/<USER>/esp32/esp-idf/components/esp_hw_support/esp_clock_output.c", "/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/rtc_clk.c", "/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/rtc_clk_init.c", "/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/rtc_init.c", "/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/rtc_sleep.c", "/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/rtc_time.c", "/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/chip_info.c", "/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/sar_periph_ctrl.c", "/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/mspi_timing_config.c", "/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp32s3/esp_memprot.c", "/home/<USER>/esp32/esp-idf/components/esp_hw_support/port/esp_memprot_conv.c", "/home/<USER>/esp32/esp-idf/components/esp_hw_support/lowpower/port/esp32s3/sleep_cpu.c"], "include_dirs": ["include", "include/soc", "include/soc/esp32s3", "dma/include", "ldo/include", "debug_probe/include"]}, "esp_lcd": {"alias": "idf::esp_lcd", "target": "___idf_esp_lcd", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_lcd", "type": "LIBRARY", "lib": "__idf_esp_lcd", "reqs": ["driver", "esp_driver_gpio", "esp_driver_i2c", "esp_driver_spi"], "priv_reqs": ["esp_mm", "esp_psram", "esp_pm", "esp_driver_i2s"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/esp_lcd/libesp_lcd.a", "sources": ["/home/<USER>/esp32/esp-idf/components/esp_lcd/src/esp_lcd_common.c", "/home/<USER>/esp32/esp-idf/components/esp_lcd/src/esp_lcd_panel_io.c", "/home/<USER>/esp32/esp-idf/components/esp_lcd/src/esp_lcd_panel_nt35510.c", "/home/<USER>/esp32/esp-idf/components/esp_lcd/src/esp_lcd_panel_ssd1306.c", "/home/<USER>/esp32/esp-idf/components/esp_lcd/src/esp_lcd_panel_st7789.c", "/home/<USER>/esp32/esp-idf/components/esp_lcd/src/esp_lcd_panel_ops.c", "/home/<USER>/esp32/esp-idf/components/esp_lcd/i2c/esp_lcd_panel_io_i2c_v1.c", "/home/<USER>/esp32/esp-idf/components/esp_lcd/i2c/esp_lcd_panel_io_i2c_v2.c", "/home/<USER>/esp32/esp-idf/components/esp_lcd/spi/esp_lcd_panel_io_spi.c", "/home/<USER>/esp32/esp-idf/components/esp_lcd/i80/esp_lcd_panel_io_i80.c", "/home/<USER>/esp32/esp-idf/components/esp_lcd/rgb/esp_lcd_panel_rgb.c"], "include_dirs": ["include", "interface", "rgb/include"]}, "esp_local_ctrl": {"alias": "idf::esp_local_ctrl", "target": "___idf_esp_local_ctrl", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_local_ctrl", "type": "LIBRARY", "lib": "__idf_esp_local_ctrl", "reqs": ["protocomm", "esp_https_server"], "priv_reqs": ["protobuf-c"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/esp_local_ctrl/libesp_local_ctrl.a", "sources": ["/home/<USER>/esp32/esp-idf/components/esp_local_ctrl/src/esp_local_ctrl.c", "/home/<USER>/esp32/esp-idf/components/esp_local_ctrl/src/esp_local_ctrl_handler.c", "/home/<USER>/esp32/esp-idf/components/esp_local_ctrl/proto-c/esp_local_ctrl.pb-c.c", "/home/<USER>/esp32/esp-idf/components/esp_local_ctrl/src/esp_local_ctrl_transport_httpd.c"], "include_dirs": ["include"]}, "esp_mm": {"alias": "idf::esp_mm", "target": "___idf_esp_mm", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_mm", "type": "LIBRARY", "lib": "__idf_esp_mm", "reqs": [], "priv_reqs": ["heap", "spi_flash"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/esp_mm/libesp_mm.a", "sources": ["/home/<USER>/esp32/esp-idf/components/esp_mm/esp_mmu_map.c", "/home/<USER>/esp32/esp-idf/components/esp_mm/port/esp32s3/ext_mem_layout.c", "/home/<USER>/esp32/esp-idf/components/esp_mm/esp_cache.c", "/home/<USER>/esp32/esp-idf/components/esp_mm/heap_align_hw.c"], "include_dirs": ["include"]}, "esp_netif": {"alias": "idf::esp_netif", "target": "___idf_esp_netif", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_netif", "type": "LIBRARY", "lib": "__idf_esp_netif", "reqs": ["esp_event"], "priv_reqs": ["esp_netif_stack"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/esp_netif/libesp_netif.a", "sources": ["/home/<USER>/esp32/esp-idf/components/esp_netif/esp_netif_handlers.c", "/home/<USER>/esp32/esp-idf/components/esp_netif/esp_netif_objects.c", "/home/<USER>/esp32/esp-idf/components/esp_netif/esp_netif_defaults.c", "/home/<USER>/esp32/esp-idf/components/esp_netif/lwip/esp_netif_lwip.c", "/home/<USER>/esp32/esp-idf/components/esp_netif/lwip/esp_netif_sntp.c", "/home/<USER>/esp32/esp-idf/components/esp_netif/lwip/esp_netif_lwip_defaults.c", "/home/<USER>/esp32/esp-idf/components/esp_netif/lwip/netif/wlanif.c", "/home/<USER>/esp32/esp-idf/components/esp_netif/lwip/netif/ethernetif.c", "/home/<USER>/esp32/esp-idf/components/esp_netif/lwip/netif/esp_pbuf_ref.c"], "include_dirs": ["include"]}, "esp_netif_stack": {"alias": "idf::esp_netif_stack", "target": "___idf_esp_netif_stack", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_netif_stack", "type": "CONFIG_ONLY", "lib": "__idf_esp_netif_stack", "reqs": ["lwip"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": []}, "esp_partition": {"alias": "idf::esp_partition", "target": "___idf_esp_partition", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_partition", "type": "LIBRARY", "lib": "__idf_esp_partition", "reqs": [], "priv_reqs": ["esp_system", "spi_flash", "partition_table", "bootloader_support", "app_update"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/esp_partition/libesp_partition.a", "sources": ["/home/<USER>/esp32/esp-idf/components/esp_partition/partition.c", "/home/<USER>/esp32/esp-idf/components/esp_partition/partition_target.c"], "include_dirs": ["include"]}, "esp_phy": {"alias": "idf::esp_phy", "target": "___idf_esp_phy", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_phy", "type": "LIBRARY", "lib": "__idf_esp_phy", "reqs": [], "priv_reqs": ["nvs_flash", "driver", "efuse", "esp_timer", "esp_wifi"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/esp_phy/libesp_phy.a", "sources": ["/home/<USER>/esp32/esp-idf/components/esp_phy/src/phy_override.c", "/home/<USER>/esp32/esp-idf/components/esp_phy/src/lib_printf.c", "/home/<USER>/esp32/esp-idf/components/esp_phy/src/phy_common.c", "/home/<USER>/esp32/esp-idf/components/esp_phy/src/phy_init.c", "/home/<USER>/esp32/esp-idf/components/esp_phy/esp32s3/phy_init_data.c", "/home/<USER>/esp32/esp-idf/components/esp_phy/src/btbb_init.c"], "include_dirs": ["include", "esp32s3/include"]}, "esp_pm": {"alias": "idf::esp_pm", "target": "___idf_esp_pm", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_pm", "type": "LIBRARY", "lib": "__idf_esp_pm", "reqs": [], "priv_reqs": ["esp_system", "esp_driver_gpio", "esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/esp_pm/libesp_pm.a", "sources": ["/home/<USER>/esp32/esp-idf/components/esp_pm/pm_locks.c", "/home/<USER>/esp32/esp-idf/components/esp_pm/pm_trace.c", "/home/<USER>/esp32/esp-idf/components/esp_pm/pm_impl.c"], "include_dirs": ["include"]}, "esp_psram": {"alias": "idf::esp_psram", "target": "___idf_esp_psram", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_psram", "type": "LIBRARY", "lib": "__idf_esp_psram", "reqs": [], "priv_reqs": ["heap", "spi_flash", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/esp_psram/libesp_psram.a", "sources": ["/home/<USER>/esp32/esp-idf/components/esp_psram/esp_psram.c", "/home/<USER>/esp32/esp-idf/components/esp_psram/mmu_psram_flash.c", "/home/<USER>/esp32/esp-idf/components/esp_psram/esp32s3/esp_psram_impl_octal.c"], "include_dirs": ["include"]}, "esp_ringbuf": {"alias": "idf::esp_ringbuf", "target": "___idf_esp_ringbuf", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_ringbuf", "type": "LIBRARY", "lib": "__idf_esp_ringbuf", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/esp_ringbuf/libesp_ringbuf.a", "sources": ["/home/<USER>/esp32/esp-idf/components/esp_ringbuf/ringbuf.c"], "include_dirs": ["include"]}, "esp_rom": {"alias": "idf::esp_rom", "target": "___idf_esp_rom", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_rom", "type": "LIBRARY", "lib": "__idf_esp_rom", "reqs": [], "priv_reqs": ["soc", "hal"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/esp_rom/libesp_rom.a", "sources": ["/home/<USER>/esp32/esp-idf/components/esp_rom/patches/esp_rom_sys.c", "/home/<USER>/esp32/esp-idf/components/esp_rom/patches/esp_rom_print.c", "/home/<USER>/esp32/esp-idf/components/esp_rom/patches/esp_rom_crc.c", "/home/<USER>/esp32/esp-idf/components/esp_rom/patches/esp_rom_uart.c", "/home/<USER>/esp32/esp-idf/components/esp_rom/patches/esp_rom_spiflash.c", "/home/<USER>/esp32/esp-idf/components/esp_rom/patches/esp_rom_efuse.c", "/home/<USER>/esp32/esp-idf/components/esp_rom/patches/esp_rom_gpio.c", "/home/<USER>/esp32/esp-idf/components/esp_rom/patches/esp_rom_longjmp.S", "/home/<USER>/esp32/esp-idf/components/esp_rom/patches/esp_rom_systimer.c", "/home/<USER>/esp32/esp-idf/components/esp_rom/patches/esp_rom_wdt.c", "/home/<USER>/esp32/esp-idf/components/esp_rom/patches/esp_rom_cache_esp32s2_esp32s3.c", "/home/<USER>/esp32/esp-idf/components/esp_rom/patches/esp_rom_cache_writeback_esp32s3.S"], "include_dirs": ["include", "esp32s3/include", "esp32s3/include/esp32s3", "esp32s3"]}, "esp_security": {"alias": "idf::esp_security", "target": "___idf_esp_security", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_security", "type": "LIBRARY", "lib": "__idf_esp_security", "reqs": [], "priv_reqs": ["efuse", "esp_hw_support", "esp_system", "esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/esp_security/libesp_security.a", "sources": ["/home/<USER>/esp32/esp-idf/components/esp_security/src/init.c", "/home/<USER>/esp32/esp-idf/components/esp_security/src/esp_hmac.c", "/home/<USER>/esp32/esp-idf/components/esp_security/src/esp_ds.c", "/home/<USER>/esp32/esp-idf/components/esp_security/src/esp_crypto_lock.c"], "include_dirs": ["include"]}, "esp_system": {"alias": "idf::esp_system", "target": "___idf_esp_system", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_system", "type": "LIBRARY", "lib": "__idf_esp_system", "reqs": [], "priv_reqs": ["spi_flash", "esp_timer", "esp_mm", "bootloader_support", "esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/esp_system/libesp_system.a", "sources": ["/home/<USER>/esp32/esp-idf/components/esp_system/esp_err.c", "/home/<USER>/esp32/esp-idf/components/esp_system/crosscore_int.c", "/home/<USER>/esp32/esp-idf/components/esp_system/esp_ipc.c", "/home/<USER>/esp32/esp-idf/components/esp_system/freertos_hooks.c", "/home/<USER>/esp32/esp-idf/components/esp_system/int_wdt.c", "/home/<USER>/esp32/esp-idf/components/esp_system/panic.c", "/home/<USER>/esp32/esp-idf/components/esp_system/esp_system.c", "/home/<USER>/esp32/esp-idf/components/esp_system/startup.c", "/home/<USER>/esp32/esp-idf/components/esp_system/startup_funcs.c", "/home/<USER>/esp32/esp-idf/components/esp_system/system_time.c", "/home/<USER>/esp32/esp-idf/components/esp_system/stack_check.c", "/home/<USER>/esp32/esp-idf/components/esp_system/ubsan.c", "/home/<USER>/esp32/esp-idf/components/esp_system/xt_wdt.c", "/home/<USER>/esp32/esp-idf/components/esp_system/task_wdt/task_wdt.c", "/home/<USER>/esp32/esp-idf/components/esp_system/task_wdt/task_wdt_impl_timergroup.c", "/home/<USER>/esp32/esp-idf/components/esp_system/port/cpu_start.c", "/home/<USER>/esp32/esp-idf/components/esp_system/port/panic_handler.c", "/home/<USER>/esp32/esp-idf/components/esp_system/port/esp_system_chip.c", "/home/<USER>/esp32/esp-idf/components/esp_system/port/image_process.c", "/home/<USER>/esp32/esp-idf/components/esp_system/port/brownout.c", "/home/<USER>/esp32/esp-idf/components/esp_system/port/esp_ipc_isr.c", "/home/<USER>/esp32/esp-idf/components/esp_system/port/arch/xtensa/esp_ipc_isr_port.c", "/home/<USER>/esp32/esp-idf/components/esp_system/port/arch/xtensa/esp_ipc_isr_handler.S", "/home/<USER>/esp32/esp-idf/components/esp_system/port/arch/xtensa/esp_ipc_isr_routines.S", "/home/<USER>/esp32/esp-idf/components/esp_system/port/arch/xtensa/panic_arch.c", "/home/<USER>/esp32/esp-idf/components/esp_system/port/arch/xtensa/panic_handler_asm.S", "/home/<USER>/esp32/esp-idf/components/esp_system/port/arch/xtensa/expression_with_stack.c", "/home/<USER>/esp32/esp-idf/components/esp_system/port/arch/xtensa/expression_with_stack_asm.S", "/home/<USER>/esp32/esp-idf/components/esp_system/port/arch/xtensa/debug_helpers.c", "/home/<USER>/esp32/esp-idf/components/esp_system/port/arch/xtensa/debug_helpers_asm.S", "/home/<USER>/esp32/esp-idf/components/esp_system/port/arch/xtensa/debug_stubs.c", "/home/<USER>/esp32/esp-idf/components/esp_system/port/arch/xtensa/trax.c", "/home/<USER>/esp32/esp-idf/components/esp_system/port/soc/esp32s3/highint_hdl.S", "/home/<USER>/esp32/esp-idf/components/esp_system/port/soc/esp32s3/clk.c", "/home/<USER>/esp32/esp-idf/components/esp_system/port/soc/esp32s3/reset_reason.c", "/home/<USER>/esp32/esp-idf/components/esp_system/port/soc/esp32s3/system_internal.c", "/home/<USER>/esp32/esp-idf/components/esp_system/port/soc/esp32s3/cache_err_int.c", "/home/<USER>/esp32/esp-idf/components/esp_system/port/soc/esp32s3/apb_backup_dma.c"], "include_dirs": ["include"]}, "esp_timer": {"alias": "idf::esp_timer", "target": "___idf_esp_timer", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_timer", "type": "LIBRARY", "lib": "__idf_esp_timer", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/esp_timer/libesp_timer.a", "sources": ["/home/<USER>/esp32/esp-idf/components/esp_timer/src/esp_timer.c", "/home/<USER>/esp32/esp-idf/components/esp_timer/src/esp_timer_init.c", "/home/<USER>/esp32/esp-idf/components/esp_timer/src/ets_timer_legacy.c", "/home/<USER>/esp32/esp-idf/components/esp_timer/src/system_time.c", "/home/<USER>/esp32/esp-idf/components/esp_timer/src/esp_timer_impl_common.c", "/home/<USER>/esp32/esp-idf/components/esp_timer/src/esp_timer_impl_systimer.c"], "include_dirs": ["include"]}, "esp_vfs_console": {"alias": "idf::esp_vfs_console", "target": "___idf_esp_vfs_console", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_vfs_console", "type": "LIBRARY", "lib": "__idf_esp_vfs_console", "reqs": [], "priv_reqs": ["vfs", "esp_driver_uart", "esp_driver_usb_serial_jtag"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/esp_vfs_console/libesp_vfs_console.a", "sources": ["/home/<USER>/esp32/esp-idf/components/esp_vfs_console/vfs_console.c"], "include_dirs": ["include"]}, "esp_wifi": {"alias": "idf::esp_wifi", "target": "___idf_esp_wifi", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_wifi", "type": "LIBRARY", "lib": "__idf_esp_wifi", "reqs": ["esp_event", "esp_phy", "esp_netif"], "priv_reqs": ["driver", "esptool_py", "esp_pm", "esp_timer", "nvs_flash", "wpa_supplicant", "hal", "lwip", "esp_coex"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/esp_wifi/libesp_wifi.a", "sources": ["/home/<USER>/esp32/esp-idf/components/esp_wifi/src/lib_printf.c", "/home/<USER>/esp32/esp-idf/components/esp_wifi/src/mesh_event.c", "/home/<USER>/esp32/esp-idf/components/esp_wifi/src/smartconfig.c", "/home/<USER>/esp32/esp-idf/components/esp_wifi/src/wifi_init.c", "/home/<USER>/esp32/esp-idf/components/esp_wifi/src/wifi_default.c", "/home/<USER>/esp32/esp-idf/components/esp_wifi/src/wifi_netif.c", "/home/<USER>/esp32/esp-idf/components/esp_wifi/src/wifi_default_ap.c", "/home/<USER>/esp32/esp-idf/components/esp_wifi/esp32s3/esp_adapter.c", "/home/<USER>/esp32/esp-idf/components/esp_wifi/src/smartconfig_ack.c"], "include_dirs": ["include", "include/local", "wifi_apps/include", "wifi_apps/nan_app/include"]}, "espcoredump": {"alias": "idf::espcoredump", "target": "___idf_espcoredump", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/espcoredump", "type": "LIBRARY", "lib": "__idf_espcoredump", "reqs": [], "priv_reqs": ["esp_partition", "spi_flash", "bootloader_support", "mbedtls", "esp_rom", "soc", "esp_system", "esp_driver_gpio", "driver"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/espcoredump/libespcoredump.a", "sources": ["/home/<USER>/esp32/esp-idf/components/espcoredump/src/core_dump_init.c", "/home/<USER>/esp32/esp-idf/components/espcoredump/src/core_dump_common.c", "/home/<USER>/esp32/esp-idf/components/espcoredump/src/core_dump_flash.c", "/home/<USER>/esp32/esp-idf/components/espcoredump/src/core_dump_uart.c", "/home/<USER>/esp32/esp-idf/components/espcoredump/src/core_dump_elf.c", "/home/<USER>/esp32/esp-idf/components/espcoredump/src/core_dump_binary.c", "/home/<USER>/esp32/esp-idf/components/espcoredump/src/core_dump_sha.c", "/home/<USER>/esp32/esp-idf/components/espcoredump/src/core_dump_crc.c", "/home/<USER>/esp32/esp-idf/components/espcoredump/src/port/xtensa/core_dump_port.c"], "include_dirs": ["include", "include/port/xtensa"]}, "espressif2022__image_player": {"alias": "idf::espressif2022__image_player", "target": "___idf_espressif2022__image_player", "prefix": "idf", "dir": "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif2022__image_player", "type": "LIBRARY", "lib": "__idf_espressif2022__image_player", "reqs": [], "priv_reqs": ["espressif__cmake_utilities"], "managed_reqs": [], "managed_priv_reqs": ["espressif__cmake_utilities"], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/espressif2022__image_player/libespressif2022__image_player.a", "sources": ["/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif2022__image_player/anim_dec.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif2022__image_player/anim_player.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif2022__image_player/anim_vfs.c"], "include_dirs": ["include"]}, "espressif__adc_battery_estimation": {"alias": "idf::espressif__adc_battery_estimation", "target": "___idf_espressif__adc_battery_estimation", "prefix": "idf", "dir": "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__adc_battery_estimation", "type": "LIBRARY", "lib": "__idf_espressif__adc_battery_estimation", "reqs": ["esp_adc", "esp_timer"], "priv_reqs": ["espressif__cmake_utilities"], "managed_reqs": [], "managed_priv_reqs": ["espressif__cmake_utilities"], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/espressif__adc_battery_estimation/libespressif__adc_battery_estimation.a", "sources": ["/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__adc_battery_estimation/adc_battery_estimation.c"], "include_dirs": ["include"]}, "espressif__adc_mic": {"alias": "idf::espressif__adc_mic", "target": "___idf_espressif__adc_mic", "prefix": "idf", "dir": "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__adc_mic", "type": "LIBRARY", "lib": "__idf_espressif__adc_mic", "reqs": ["esp_adc"], "priv_reqs": ["espressif__cmake_utilities", "espressif__esp_codec_dev"], "managed_reqs": [], "managed_priv_reqs": ["espressif__cmake_utilities", "espressif__esp_codec_dev"], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/espressif__adc_mic/libespressif__adc_mic.a", "sources": ["/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__adc_mic/adc_mic.c"], "include_dirs": ["."]}, "espressif__button": {"alias": "idf::espressif__button", "target": "___idf_espressif__button", "prefix": "idf", "dir": "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__button", "type": "LIBRARY", "lib": "__idf_espressif__button", "reqs": ["driver", "esp_adc"], "priv_reqs": ["esp_timer", "espressif__cmake_utilities"], "managed_reqs": [], "managed_priv_reqs": ["espressif__cmake_utilities"], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/espressif__button/libespressif__button.a", "sources": ["/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__button/button_gpio.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__button/iot_button.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__button/button_matrix.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__button/button_adc.c"], "include_dirs": ["include", "interface"]}, "espressif__cmake_utilities": {"alias": "idf::espressif__cmake_utilities", "target": "___idf_espressif__cmake_utilities", "prefix": "idf", "dir": "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__cmake_utilities", "type": "CONFIG_ONLY", "lib": "__idf_espressif__cmake_utilities", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": []}, "espressif__dl_fft": {"alias": "idf::espressif__dl_fft", "target": "___idf_espressif__dl_fft", "prefix": "idf", "dir": "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__dl_fft", "type": "LIBRARY", "lib": "__idf_espressif__dl_fft", "reqs": ["espressif__esp-dsp"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/espressif__dl_fft/libespressif__dl_fft.a", "sources": ["/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__dl_fft/dl_fft_f32.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__dl_fft/dl_fft_s16.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__dl_fft/dl_rfft_f32.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__dl_fft/dl_rfft_s16.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__dl_fft/base/dl_fft2r_fc32_ansi.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__dl_fft/base/dl_fft4r_fc32_ansi.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__dl_fft/base/dl_fft2r_sc16_ansi.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__dl_fft/base/dl_fft_base.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__dl_fft/base/isa/esp32s3/dl_fft2r_fc32_aes3.S", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__dl_fft/base/isa/esp32s3/dl_fft4r_fc32_aes3.S"], "include_dirs": [".", "base", "base/isa"]}, "espressif__esp-dsp": {"alias": "idf::espressif__esp-dsp", "target": "___idf_espressif__esp-dsp", "prefix": "idf", "dir": "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp", "type": "LIBRARY", "lib": "__idf_espressif__esp-dsp", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/espressif__esp-dsp/libespressif__esp-dsp.a", "sources": ["/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/common/misc/dsps_pwroftwo.cpp", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/common/misc/aes3_tie_log.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/dotprod/float/dsps_dotprod_f32_ae32.S", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/dotprod/float/dsps_dotprod_f32_m_ae32.S", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/dotprod/float/dsps_dotprode_f32_ae32.S", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/dotprod/float/dsps_dotprode_f32_m_ae32.S", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/dotprod/float/dsps_dotprod_f32_ansi.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/dotprod/float/dsps_dotprode_f32_ansi.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/dotprod/float/dsps_dotprod_f32_aes3.S", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/dotprod/float/dsps_dotprod_f32_arp4.S", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/dotprod/float/dsps_dotprode_f32_arp4.S", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/dotprod/fixed/dsps_dotprod_s16_ae32.S", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/dotprod/fixed/dsps_dotprod_s16_m_ae32.S", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/dotprod/fixed/dsps_dotprod_s16_ansi.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/dotprod/fixed/dsps_dotprod_s16_arp4.S", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/dotprod/float/dspi_dotprod_f32_ansi.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/dotprod/float/dspi_dotprod_off_f32_ansi.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/dotprod/fixed/dspi_dotprod_s16_ansi.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/dotprod/fixed/dspi_dotprod_u16_ansi.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/dotprod/fixed/dspi_dotprod_s8_ansi.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/dotprod/fixed/dspi_dotprod_u8_ansi.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/dotprod/fixed/dspi_dotprod_off_s16_ansi.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/dotprod/fixed/dspi_dotprod_off_u16_ansi.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/dotprod/fixed/dspi_dotprod_off_s8_ansi.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/dotprod/fixed/dspi_dotprod_off_u8_ansi.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/dotprod/fixed/dspi_dotprod_s16_aes3.S", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/dotprod/fixed/dspi_dotprod_u16_aes3.S", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/dotprod/fixed/dspi_dotprod_off_s16_aes3.S", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/dotprod/fixed/dspi_dotprod_off_u16_aes3.S", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/dotprod/fixed/dspi_dotprod_s8_aes3.S", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/dotprod/fixed/dspi_dotprod_u8_aes3.S", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/dotprod/fixed/dspi_dotprod_off_u8_aes3.S", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/dotprod/fixed/dspi_dotprod_off_s8_aes3.S", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/dotprod/fixed/dspi_dotprod_s16_arp4.S", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/dotprod/fixed/dspi_dotprod_s8_arp4.S", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/dotprod/fixed/dspi_dotprod_u16_arp4.S", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/dotprod/fixed/dspi_dotprod_u8_arp4.S", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/dotprod/fixed/dspi_dotprod_off_s16_arp4.S", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/dotprod/fixed/dspi_dotprod_off_u16_arp4.S", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/dotprod/fixed/dspi_dotprod_off_s8_arp4.S", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/dotprod/fixed/dspi_dotprod_off_u8_arp4.S", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/matrix/mul/float/dspm_mult_3x3x1_f32_ae32.S", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/matrix/mul/float/dspm_mult_3x3x3_f32_ae32.S", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/matrix/mul/float/dspm_mult_4x4x1_f32_ae32.S", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/matrix/mul/float/dspm_mult_4x4x4_f32_ae32.S", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/matrix/mul/float/dspm_mult_f32_ae32.S", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/matrix/mul/float/dspm_mult_f32_aes3.S", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/matrix/mul/float/dspm_mult_f32_ansi.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/matrix/mul/float/dspm_mult_f32_arp4.S", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/matrix/mul/float/dspm_mult_ex_f32_ansi.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/matrix/mul/float/dspm_mult_ex_f32_ae32.S", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/matrix/mul/float/dspm_mult_ex_f32_arp4.S", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/matrix/mul/float/dspm_mult_ex_f32_aes3.S", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/matrix/mul/fixed/dspm_mult_s16_ae32.S", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/matrix/mul/fixed/dspm_mult_s16_m_ae32_vector.S", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/matrix/mul/fixed/dspm_mult_s16_m_ae32.S", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/matrix/mul/fixed/dspm_mult_s16_ansi.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/matrix/mul/fixed/dspm_mult_s16_aes3.S", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/matrix/mul/fixed/dspm_mult_s16_arp4.S", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/matrix/add/float/dspm_add_f32_ansi.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/matrix/add/float/dspm_add_f32_ae32.S", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/matrix/addc/float/dspm_addc_f32_ansi.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/matrix/addc/float/dspm_addc_f32_ae32.S", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/matrix/mulc/float/dspm_mulc_f32_ansi.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/matrix/mulc/float/dspm_mulc_f32_ae32.S", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/matrix/sub/float/dspm_sub_f32_ansi.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/matrix/sub/float/dspm_sub_f32_ae32.S", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/matrix/mat/mat.cpp", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/math/mulc/float/dsps_mulc_f32_ansi.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/math/addc/float/dsps_addc_f32_ansi.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/math/mulc/fixed/dsps_mulc_s16_ansi.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/math/mulc/fixed/dsps_mulc_s16_ae32.S", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/math/add/float/dsps_add_f32_ansi.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/math/add/fixed/dsps_add_s16_ansi.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/math/add/fixed/dsps_add_s16_ae32.S", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/math/add/fixed/dsps_add_s16_aes3.S", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/math/add/fixed/dsps_add_s8_ansi.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/math/add/fixed/dsps_add_s8_aes3.S", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/math/sub/float/dsps_sub_f32_ansi.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/math/sub/fixed/dsps_sub_s16_ansi.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/math/sub/fixed/dsps_sub_s16_ae32.S", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/math/sub/fixed/dsps_sub_s16_aes3.S", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/math/sub/fixed/dsps_sub_s8_ansi.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/math/sub/fixed/dsps_sub_s8_aes3.S", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/math/mul/float/dsps_mul_f32_ansi.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/math/mul/fixed/dsps_mul_s16_ansi.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/math/mul/fixed/dsps_mul_s16_ae32.S", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/math/mul/fixed/dsps_mul_s16_aes3.S", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/math/mul/fixed/dsps_mul_s8_ansi.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/math/mul/fixed/dsps_mul_s8_aes3.S", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/math/mulc/float/dsps_mulc_f32_ae32.S", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/math/addc/float/dsps_addc_f32_ae32.S", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/math/add/float/dsps_add_f32_ae32.S", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/math/sub/float/dsps_sub_f32_ae32.S", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/math/mul/float/dsps_mul_f32_ae32.S", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/math/sqrt/float/dsps_sqrt_f32_ansi.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/fft/float/dsps_fft2r_fc32_ae32_.S", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/fft/float/dsps_fft2r_fc32_aes3_.S", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/fft/float/dsps_fft2r_fc32_arp4.S", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/fft/float/dsps_fft2r_fc32_ansi.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/fft/float/dsps_fft2r_fc32_ae32.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/fft/float/dsps_bit_rev_lookup_fc32_aes3.S", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/fft/float/dsps_fft4r_fc32_ansi.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/fft/float/dsps_fft4r_fc32_ae32.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/fft/float/dsps_fft4r_fc32_ae32_.S", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/fft/float/dsps_fft4r_fc32_aes3_.S", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/fft/float/dsps_fft4r_fc32_arp4.S", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/fft/float/dsps_fft2r_bitrev_tables_fc32.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/fft/float/dsps_fft4r_bitrev_tables_fc32.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/fft/fixed/dsps_fft2r_sc16_ae32.S", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/fft/fixed/dsps_fft2r_sc16_ansi.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/fft/fixed/dsps_fft2r_sc16_aes3.S", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/fft/fixed/dsps_fft2r_sc16_arp4.S", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/dct/float/dsps_dct_f32.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/dct/float/dsps_dctiv_f32.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/dct/float/dsps_dstiv_f32.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/support/snr/float/dsps_snr_f32.cpp", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/support/sfdr/float/dsps_sfdr_f32.cpp", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/support/misc/dsps_d_gen.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/support/misc/dsps_h_gen.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/support/misc/dsps_tone_gen.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/support/cplx_gen/dsps_cplx_gen.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/support/cplx_gen/dsps_cplx_gen.S", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/support/cplx_gen/dsps_cplx_gen_init.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/support/mem/esp32s3/dsps_memset_aes3.S", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/support/mem/esp32s3/dsps_memcpy_aes3.S", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/support/view/dsps_view.cpp", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/windows/hann/float/dsps_wind_hann_f32.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/windows/blackman/float/dsps_wind_blackman_f32.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/windows/blackman_harris/float/dsps_wind_blackman_harris_f32.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/windows/blackman_nuttall/float/dsps_wind_blackman_nuttall_f32.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/windows/nuttall/float/dsps_wind_nuttall_f32.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/windows/flat_top/float/dsps_wind_flat_top_f32.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/conv/float/dsps_conv_f32_ansi.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/conv/float/dspi_conv_f32_ansi.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/conv/float/dsps_conv_f32_ae32.S", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/conv/float/dsps_corr_f32_ansi.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/conv/float/dsps_corr_f32_ae32.S", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/conv/float/dsps_ccorr_f32_ansi.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/conv/float/dsps_ccorr_f32_ae32.S", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/iir/biquad/dsps_biquad_f32_ae32.S", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/iir/biquad/dsps_biquad_sf32_ae32.S", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/iir/biquad/dsps_biquad_f32_aes3.S", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/iir/biquad/dsps_biquad_f32_arp4.S", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/iir/biquad/dsps_biquad_sf32_arp4.S", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/iir/biquad/dsps_biquad_f32_ansi.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/iir/biquad/dsps_biquad_sf32_ansi.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/iir/biquad/dsps_biquad_gen_f32.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/fir/float/dsps_fir_f32_ae32.S", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/fir/float/dsps_fir_f32_aes3.S", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/fir/float/dsps_fird_f32_ae32.S", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/fir/float/dsps_fird_f32_aes3.S", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/fir/float/dsps_fird_f32_arp4.S", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/fir/float/dsps_fir_f32_ansi.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/fir/float/dsps_fir_init_f32.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/fir/float/dsps_fird_f32_ansi.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/fir/float/dsps_fird_init_f32.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/fir/fixed/dsps_fird_init_s16.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/fir/fixed/dsps_fird_s16_ansi.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/fir/fixed/dsps_fird_s16_ae32.S", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/fir/fixed/dsps_fir_s16_m_ae32.S", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/fir/fixed/dsps_fird_s16_aes3.S", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/fir/fixed/dsps_fird_s16_arp4.S", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/kalman/ekf/common/ekf.cpp", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp/modules/kalman/ekf_imu13states/ekf_imu13states.cpp"], "include_dirs": ["modules/dotprod/include", "modules/support/include", "modules/support/mem/include", "modules/windows/include", "modules/windows/hann/include", "modules/windows/blackman/include", "modules/windows/blackman_harris/include", "modules/windows/blackman_nuttall/include", "modules/windows/nuttall/include", "modules/windows/flat_top/include", "modules/iir/include", "modules/fir/include", "modules/math/include", "modules/math/add/include", "modules/math/sub/include", "modules/math/mul/include", "modules/math/addc/include", "modules/math/mulc/include", "modules/math/sqrt/include", "modules/matrix/mul/include", "modules/matrix/add/include", "modules/matrix/addc/include", "modules/matrix/mulc/include", "modules/matrix/sub/include", "modules/matrix/include", "modules/fft/include", "modules/dct/include", "modules/conv/include", "modules/common/include", "modules/matrix/mul/test/include", "modules/kalman/ekf/include", "modules/kalman/ekf_imu13states/include"]}, "espressif__esp-sr": {"alias": "idf::espressif__esp-sr", "target": "___idf_espressif__esp-sr", "prefix": "idf", "dir": "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-sr", "type": "LIBRARY", "lib": "__idf_espressif__esp-sr", "reqs": ["json", "spiffs", "esp_partition"], "priv_reqs": ["spi_flash", "espressif__dl_fft", "espressif__esp-dsp"], "managed_reqs": [], "managed_priv_reqs": ["espressif__dl_fft", "espressif__esp-dsp"], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/espressif__esp-sr/libespressif__esp-sr.a", "sources": ["/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-sr/src/model_path.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-sr/src/esp_sr_debug.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-sr/src/esp_mn_speech_commands.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-sr/src/esp_process_sdkconfig.c"], "include_dirs": ["esp-tts/esp_tts_chinese/include", "include/esp32s3", "src/include"]}, "espressif__esp32-camera": {"alias": "idf::espressif__esp32-camera", "target": "___idf_espressif__esp32-camera", "prefix": "idf", "dir": "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp32-camera", "type": "LIBRARY", "lib": "__idf_espressif__esp32-camera", "reqs": ["driver", "espressif__esp_jpeg"], "priv_reqs": ["freertos", "nvs_flash", "esp_timer"], "managed_reqs": ["espressif__esp_jpeg"], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/espressif__esp32-camera/libespressif__esp32-camera.a", "sources": ["/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp32-camera/conversions/yuv.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp32-camera/conversions/to_jpg.cpp", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp32-camera/conversions/to_bmp.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp32-camera/conversions/jpge.cpp", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp32-camera/driver/esp_camera.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp32-camera/driver/cam_hal.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp32-camera/driver/sensor.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp32-camera/sensors/ov2640.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp32-camera/sensors/ov3660.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp32-camera/sensors/ov5640.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp32-camera/sensors/ov7725.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp32-camera/sensors/ov7670.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp32-camera/sensors/nt99141.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp32-camera/sensors/gc0308.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp32-camera/sensors/gc2145.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp32-camera/sensors/gc032a.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp32-camera/sensors/bf3005.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp32-camera/sensors/bf20a6.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp32-camera/sensors/sc101iot.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp32-camera/sensors/sc030iot.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp32-camera/sensors/sc031gs.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp32-camera/sensors/mega_ccm.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp32-camera/sensors/hm1055.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp32-camera/sensors/hm0360.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp32-camera/target/esp32s3/ll_cam.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp32-camera/driver/sccb-ng.c"], "include_dirs": ["driver/include", "conversions/include"]}, "espressif__esp_codec_dev": {"alias": "idf::espressif__esp_codec_dev", "target": "___idf_espressif__esp_codec_dev", "prefix": "idf", "dir": "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_codec_dev", "type": "LIBRARY", "lib": "__idf_espressif__esp_codec_dev", "reqs": ["driver"], "priv_reqs": ["freertos"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/espressif__esp_codec_dev/libespressif__esp_codec_dev.a", "sources": ["/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_codec_dev/esp_codec_dev.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_codec_dev/esp_codec_dev_vol.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_codec_dev/esp_codec_dev_if.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_codec_dev/audio_codec_sw_vol.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_codec_dev/platform/audio_codec_gpio.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_codec_dev/platform/audio_codec_ctrl_i2c.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_codec_dev/platform/audio_codec_data_i2s.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_codec_dev/platform/audio_codec_ctrl_spi.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_codec_dev/platform/esp_codec_dev_os.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_codec_dev/device/es8311/es8311.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_codec_dev/device/es8156/es8156.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_codec_dev/device/es7243e/es7243e.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_codec_dev/device/es7210/es7210.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_codec_dev/device/es7243/es7243.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_codec_dev/device/es8388/es8388.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_codec_dev/device/tas5805m/tas5805m.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_codec_dev/device/es8374/es8374.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_codec_dev/device/aw88298/aw88298.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_codec_dev/device/es8389/es8389.c"], "include_dirs": ["include", "interface", "device/include"]}, "espressif__esp_io_expander": {"alias": "idf::espressif__esp_io_expander", "target": "___idf_espressif__esp_io_expander", "prefix": "idf", "dir": "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_io_expander", "type": "LIBRARY", "lib": "__idf_espressif__esp_io_expander", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/espressif__esp_io_expander/libespressif__esp_io_expander.a", "sources": ["/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_io_expander/esp_io_expander.c"], "include_dirs": ["include"]}, "espressif__esp_io_expander_tca9554": {"alias": "idf::espressif__esp_io_expander_tca9554", "target": "___idf_espressif__esp_io_expander_tca9554", "prefix": "idf", "dir": "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_io_expander_tca9554", "type": "LIBRARY", "lib": "__idf_espressif__esp_io_expander_tca9554", "reqs": ["driver", "espressif__esp_io_expander"], "priv_reqs": [], "managed_reqs": ["espressif__esp_io_expander"], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/espressif__esp_io_expander_tca9554/libespressif__esp_io_expander_tca9554.a", "sources": ["/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_io_expander_tca9554/esp_io_expander_tca9554.c"], "include_dirs": ["include"]}, "espressif__esp_io_expander_tca95xx_16bit": {"alias": "idf::espressif__esp_io_expander_tca95xx_16bit", "target": "___idf_espressif__esp_io_expander_tca95xx_16bit", "prefix": "idf", "dir": "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_io_expander_tca95xx_16bit", "type": "LIBRARY", "lib": "__idf_espressif__esp_io_expander_tca95xx_16bit", "reqs": ["driver", "espressif__esp_io_expander"], "priv_reqs": [], "managed_reqs": ["espressif__esp_io_expander"], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/espressif__esp_io_expander_tca95xx_16bit/libespressif__esp_io_expander_tca95xx_16bit.a", "sources": ["/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_io_expander_tca95xx_16bit/esp_io_expander_tca95xx_16bit.c"], "include_dirs": ["include"]}, "espressif__esp_jpeg": {"alias": "idf::espressif__esp_jpeg", "target": "___idf_espressif__esp_jpeg", "prefix": "idf", "dir": "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_jpeg", "type": "LIBRARY", "lib": "__idf_espressif__esp_jpeg", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/espressif__esp_jpeg/libespressif__esp_jpeg.a", "sources": ["/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_jpeg/jpeg_decoder.c"], "include_dirs": ["include"]}, "espressif__esp_lcd_axs15231b": {"alias": "idf::espressif__esp_lcd_axs15231b", "target": "___idf_espressif__esp_lcd_axs15231b", "prefix": "idf", "dir": "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_lcd_axs15231b", "type": "LIBRARY", "lib": "__idf_espressif__esp_lcd_axs15231b", "reqs": ["driver", "esp_lcd", "espressif__esp_lcd_touch"], "priv_reqs": ["espressif__cmake_utilities"], "managed_reqs": ["espressif__esp_lcd_touch"], "managed_priv_reqs": ["espressif__cmake_utilities"], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/espressif__esp_lcd_axs15231b/libespressif__esp_lcd_axs15231b.a", "sources": ["/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_lcd_axs15231b/esp_lcd_axs15231b.c"], "include_dirs": ["include"]}, "espressif__esp_lcd_gc9a01": {"alias": "idf::espressif__esp_lcd_gc9a01", "target": "___idf_espressif__esp_lcd_gc9a01", "prefix": "idf", "dir": "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_lcd_gc9a01", "type": "LIBRARY", "lib": "__idf_espressif__esp_lcd_gc9a01", "reqs": ["esp_lcd"], "priv_reqs": ["driver", "espressif__cmake_utilities"], "managed_reqs": [], "managed_priv_reqs": ["espressif__cmake_utilities"], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/espressif__esp_lcd_gc9a01/libespressif__esp_lcd_gc9a01.a", "sources": ["/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_lcd_gc9a01/esp_lcd_gc9a01.c"], "include_dirs": ["include"]}, "espressif__esp_lcd_ili9341": {"alias": "idf::espressif__esp_lcd_ili9341", "target": "___idf_espressif__esp_lcd_ili9341", "prefix": "idf", "dir": "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_lcd_ili9341", "type": "LIBRARY", "lib": "__idf_espressif__esp_lcd_ili9341", "reqs": ["driver", "esp_lcd"], "priv_reqs": ["espressif__cmake_utilities"], "managed_reqs": [], "managed_priv_reqs": ["espressif__cmake_utilities"], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/espressif__esp_lcd_ili9341/libespressif__esp_lcd_ili9341.a", "sources": ["/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_lcd_ili9341/esp_lcd_ili9341.c"], "include_dirs": ["include"]}, "espressif__esp_lcd_panel_io_additions": {"alias": "idf::espressif__esp_lcd_panel_io_additions", "target": "___idf_espressif__esp_lcd_panel_io_additions", "prefix": "idf", "dir": "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_lcd_panel_io_additions", "type": "LIBRARY", "lib": "__idf_espressif__esp_lcd_panel_io_additions", "reqs": ["esp_lcd", "espressif__esp_io_expander"], "priv_reqs": ["driver", "espressif__cmake_utilities"], "managed_reqs": ["espressif__esp_io_expander"], "managed_priv_reqs": ["espressif__cmake_utilities"], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/espressif__esp_lcd_panel_io_additions/libespressif__esp_lcd_panel_io_additions.a", "sources": ["/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_lcd_panel_io_additions/esp_lcd_panel_io_3wire_spi.c"], "include_dirs": ["include"]}, "espressif__esp_lcd_spd2010": {"alias": "idf::espressif__esp_lcd_spd2010", "target": "___idf_espressif__esp_lcd_spd2010", "prefix": "idf", "dir": "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_lcd_spd2010", "type": "LIBRARY", "lib": "__idf_espressif__esp_lcd_spd2010", "reqs": ["esp_lcd"], "priv_reqs": ["driver", "espressif__cmake_utilities"], "managed_reqs": [], "managed_priv_reqs": ["espressif__cmake_utilities"], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/espressif__esp_lcd_spd2010/libespressif__esp_lcd_spd2010.a", "sources": ["/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_lcd_spd2010/esp_lcd_spd2010.c"], "include_dirs": ["include"]}, "espressif__esp_lcd_st77916": {"alias": "idf::espressif__esp_lcd_st77916", "target": "___idf_espressif__esp_lcd_st77916", "prefix": "idf", "dir": "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_lcd_st77916", "type": "LIBRARY", "lib": "__idf_espressif__esp_lcd_st77916", "reqs": ["esp_lcd"], "priv_reqs": ["driver", "espressif__cmake_utilities"], "managed_reqs": [], "managed_priv_reqs": ["espressif__cmake_utilities"], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/espressif__esp_lcd_st77916/libespressif__esp_lcd_st77916.a", "sources": ["/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_lcd_st77916/esp_lcd_st77916.c"], "include_dirs": ["include"]}, "espressif__esp_lcd_st7796": {"alias": "idf::espressif__esp_lcd_st7796", "target": "___idf_espressif__esp_lcd_st7796", "prefix": "idf", "dir": "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_lcd_st7796", "type": "LIBRARY", "lib": "__idf_espressif__esp_lcd_st7796", "reqs": ["esp_lcd"], "priv_reqs": ["driver", "espressif__cmake_utilities"], "managed_reqs": [], "managed_priv_reqs": ["espressif__cmake_utilities"], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/espressif__esp_lcd_st7796/libespressif__esp_lcd_st7796.a", "sources": ["/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_lcd_st7796/esp_lcd_st7796.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_lcd_st7796/esp_lcd_st7796_general.c"], "include_dirs": ["include"]}, "espressif__esp_lcd_touch": {"alias": "idf::espressif__esp_lcd_touch", "target": "___idf_espressif__esp_lcd_touch", "prefix": "idf", "dir": "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_lcd_touch", "type": "LIBRARY", "lib": "__idf_espressif__esp_lcd_touch", "reqs": ["driver", "esp_lcd"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/espressif__esp_lcd_touch/libespressif__esp_lcd_touch.a", "sources": ["/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_lcd_touch/esp_lcd_touch.c"], "include_dirs": ["include"]}, "espressif__esp_lcd_touch_cst816s": {"alias": "idf::espressif__esp_lcd_touch_cst816s", "target": "___idf_espressif__esp_lcd_touch_cst816s", "prefix": "idf", "dir": "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_lcd_touch_cst816s", "type": "LIBRARY", "lib": "__idf_espressif__esp_lcd_touch_cst816s", "reqs": ["esp_lcd", "espressif__esp_lcd_touch"], "priv_reqs": [], "managed_reqs": ["espressif__esp_lcd_touch"], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/espressif__esp_lcd_touch_cst816s/libespressif__esp_lcd_touch_cst816s.a", "sources": ["/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_lcd_touch_cst816s/esp_lcd_touch_cst816s.c"], "include_dirs": ["include"]}, "espressif__esp_lcd_touch_ft5x06": {"alias": "idf::espressif__esp_lcd_touch_ft5x06", "target": "___idf_espressif__esp_lcd_touch_ft5x06", "prefix": "idf", "dir": "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_lcd_touch_ft5x06", "type": "LIBRARY", "lib": "__idf_espressif__esp_lcd_touch_ft5x06", "reqs": ["esp_lcd", "espressif__esp_lcd_touch"], "priv_reqs": [], "managed_reqs": ["espressif__esp_lcd_touch"], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/espressif__esp_lcd_touch_ft5x06/libespressif__esp_lcd_touch_ft5x06.a", "sources": ["/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_lcd_touch_ft5x06/esp_lcd_touch_ft5x06.c"], "include_dirs": ["include"]}, "espressif__esp_lcd_touch_gt911": {"alias": "idf::espressif__esp_lcd_touch_gt911", "target": "___idf_espressif__esp_lcd_touch_gt911", "prefix": "idf", "dir": "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_lcd_touch_gt911", "type": "LIBRARY", "lib": "__idf_espressif__esp_lcd_touch_gt911", "reqs": ["esp_lcd", "espressif__esp_lcd_touch"], "priv_reqs": [], "managed_reqs": ["espressif__esp_lcd_touch"], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/espressif__esp_lcd_touch_gt911/libespressif__esp_lcd_touch_gt911.a", "sources": ["/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_lcd_touch_gt911/esp_lcd_touch_gt911.c"], "include_dirs": ["include"]}, "espressif__esp_lvgl_port": {"alias": "idf::espressif__esp_lvgl_port", "target": "___idf_espressif__esp_lvgl_port", "prefix": "idf", "dir": "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_lvgl_port", "type": "CONFIG_ONLY", "lib": "__idf_espressif__esp_lvgl_port", "reqs": ["esp_lcd", "lvgl__lvgl"], "priv_reqs": [], "managed_reqs": ["lvgl__lvgl"], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["include"]}, "espressif__esp_mmap_assets": {"alias": "idf::espressif__esp_mmap_assets", "target": "___idf_espressif__esp_mmap_assets", "prefix": "idf", "dir": "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_mmap_assets", "type": "LIBRARY", "lib": "__idf_espressif__esp_mmap_assets", "reqs": [], "priv_reqs": ["spi_flash", "esp_partition", "app_update", "espressif__cmake_utilities"], "managed_reqs": [], "managed_priv_reqs": ["espressif__cmake_utilities"], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/espressif__esp_mmap_assets/libespressif__esp_mmap_assets.a", "sources": ["/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_mmap_assets/esp_mmap_assets.c"], "include_dirs": ["include"]}, "espressif__knob": {"alias": "idf::espressif__knob", "target": "___idf_espressif__knob", "prefix": "idf", "dir": "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__knob", "type": "LIBRARY", "lib": "__idf_espressif__knob", "reqs": ["driver"], "priv_reqs": ["esp_timer", "espressif__cmake_utilities"], "managed_reqs": [], "managed_priv_reqs": ["espressif__cmake_utilities"], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/espressif__knob/libespressif__knob.a", "sources": ["/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__knob/iot_knob.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__knob/knob_gpio.c"], "include_dirs": ["include"]}, "espressif__led_strip": {"alias": "idf::espressif__led_strip", "target": "___idf_espressif__led_strip", "prefix": "idf", "dir": "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__led_strip", "type": "LIBRARY", "lib": "__idf_espressif__led_strip", "reqs": ["esp_driver_rmt", "esp_driver_spi"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/espressif__led_strip/libespressif__led_strip.a", "sources": ["/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__led_strip/src/led_strip_api.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__led_strip/src/led_strip_rmt_dev.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__led_strip/src/led_strip_rmt_encoder.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__led_strip/src/led_strip_spi_dev.c"], "include_dirs": ["include", "interface"]}, "esptool_py": {"alias": "idf::esptool_py", "target": "___idf_esptool_py", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esptool_py", "type": "CONFIG_ONLY", "lib": "__idf_esptool_py", "reqs": ["bootloader"], "priv_reqs": ["partition_table"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": []}, "fatfs": {"alias": "idf::fatfs", "target": "___idf_fatfs", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/fatfs", "type": "LIBRARY", "lib": "__idf_fatfs", "reqs": ["wear_levelling", "sdmmc", "esp_driver_sdmmc", "esp_driver_sdspi"], "priv_reqs": ["vfs", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/fatfs/libfatfs.a", "sources": ["/home/<USER>/esp32/esp-idf/components/fatfs/diskio/diskio.c", "/home/<USER>/esp32/esp-idf/components/fatfs/diskio/diskio_rawflash.c", "/home/<USER>/esp32/esp-idf/components/fatfs/diskio/diskio_wl.c", "/home/<USER>/esp32/esp-idf/components/fatfs/src/ff.c", "/home/<USER>/esp32/esp-idf/components/fatfs/src/ffunicode.c", "/home/<USER>/esp32/esp-idf/components/fatfs/port/freertos/ffsystem.c", "/home/<USER>/esp32/esp-idf/components/fatfs/diskio/diskio_sdmmc.c", "/home/<USER>/esp32/esp-idf/components/fatfs/vfs/vfs_fat.c", "/home/<USER>/esp32/esp-idf/components/fatfs/vfs/vfs_fat_sdmmc.c", "/home/<USER>/esp32/esp-idf/components/fatfs/vfs/vfs_fat_spiflash.c"], "include_dirs": ["diskio", "src", "vfs"]}, "freertos": {"alias": "idf::freertos", "target": "___idf_freertos", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/freertos", "type": "LIBRARY", "lib": "__idf_freertos", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/freertos/libfreertos.a", "sources": ["/home/<USER>/esp32/esp-idf/components/freertos/heap_idf.c", "/home/<USER>/esp32/esp-idf/components/freertos/app_startup.c", "/home/<USER>/esp32/esp-idf/components/freertos/port_common.c", "/home/<USER>/esp32/esp-idf/components/freertos/port_systick.c", "/home/<USER>/esp32/esp-idf/components/freertos/FreeRTOS-Kernel/list.c", "/home/<USER>/esp32/esp-idf/components/freertos/FreeRTOS-Kernel/queue.c", "/home/<USER>/esp32/esp-idf/components/freertos/FreeRTOS-Kernel/tasks.c", "/home/<USER>/esp32/esp-idf/components/freertos/FreeRTOS-Kernel/timers.c", "/home/<USER>/esp32/esp-idf/components/freertos/FreeRTOS-Kernel/event_groups.c", "/home/<USER>/esp32/esp-idf/components/freertos/FreeRTOS-Kernel/stream_buffer.c", "/home/<USER>/esp32/esp-idf/components/freertos/FreeRTOS-Kernel/portable/xtensa/port.c", "/home/<USER>/esp32/esp-idf/components/freertos/FreeRTOS-Kernel/portable/xtensa/portasm.S", "/home/<USER>/esp32/esp-idf/components/freertos/FreeRTOS-Kernel/portable/xtensa/xtensa_init.c", "/home/<USER>/esp32/esp-idf/components/freertos/FreeRTOS-Kernel/portable/xtensa/xtensa_overlay_os_hook.c", "/home/<USER>/esp32/esp-idf/components/freertos/esp_additions/freertos_compatibility.c", "/home/<USER>/esp32/esp-idf/components/freertos/esp_additions/idf_additions_event_groups.c", "/home/<USER>/esp32/esp-idf/components/freertos/esp_additions/idf_additions.c"], "include_dirs": ["config/include", "config/include/freertos", "config/xtensa/include", "FreeRTOS-Kernel/include", "FreeRTOS-Kernel/portable/xtensa/include", "FreeRTOS-Kernel/portable/xtensa/include/freertos", "esp_additions/include"]}, "hal": {"alias": "idf::hal", "target": "___idf_hal", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/hal", "type": "LIBRARY", "lib": "__idf_hal", "reqs": ["soc", "esp_rom"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/hal/libhal.a", "sources": ["/home/<USER>/esp32/esp-idf/components/hal/hal_utils.c", "/home/<USER>/esp32/esp-idf/components/hal/mpu_hal.c", "/home/<USER>/esp32/esp-idf/components/hal/efuse_hal.c", "/home/<USER>/esp32/esp-idf/components/hal/esp32s3/efuse_hal.c", "/home/<USER>/esp32/esp-idf/components/hal/mmu_hal.c", "/home/<USER>/esp32/esp-idf/components/hal/cache_hal.c", "/home/<USER>/esp32/esp-idf/components/hal/color_hal.c", "/home/<USER>/esp32/esp-idf/components/hal/spi_flash_hal.c", "/home/<USER>/esp32/esp-idf/components/hal/spi_flash_hal_iram.c", "/home/<USER>/esp32/esp-idf/components/hal/spi_flash_encrypt_hal_iram.c", "/home/<USER>/esp32/esp-idf/components/hal/esp32s3/clk_tree_hal.c", "/home/<USER>/esp32/esp-idf/components/hal/systimer_hal.c", "/home/<USER>/esp32/esp-idf/components/hal/uart_hal.c", "/home/<USER>/esp32/esp-idf/components/hal/uart_hal_iram.c", "/home/<USER>/esp32/esp-idf/components/hal/gpio_hal.c", "/home/<USER>/esp32/esp-idf/components/hal/rtc_io_hal.c", "/home/<USER>/esp32/esp-idf/components/hal/timer_hal.c", "/home/<USER>/esp32/esp-idf/components/hal/ledc_hal.c", "/home/<USER>/esp32/esp-idf/components/hal/ledc_hal_iram.c", "/home/<USER>/esp32/esp-idf/components/hal/i2c_hal.c", "/home/<USER>/esp32/esp-idf/components/hal/i2c_hal_iram.c", "/home/<USER>/esp32/esp-idf/components/hal/rmt_hal.c", "/home/<USER>/esp32/esp-idf/components/hal/pcnt_hal.c", "/home/<USER>/esp32/esp-idf/components/hal/mcpwm_hal.c", "/home/<USER>/esp32/esp-idf/components/hal/twai_hal.c", "/home/<USER>/esp32/esp-idf/components/hal/twai_hal_iram.c", "/home/<USER>/esp32/esp-idf/components/hal/gdma_hal_top.c", "/home/<USER>/esp32/esp-idf/components/hal/gdma_hal_ahb_v1.c", "/home/<USER>/esp32/esp-idf/components/hal/i2s_hal.c", "/home/<USER>/esp32/esp-idf/components/hal/sdm_hal.c", "/home/<USER>/esp32/esp-idf/components/hal/sdmmc_hal.c", "/home/<USER>/esp32/esp-idf/components/hal/adc_hal_common.c", "/home/<USER>/esp32/esp-idf/components/hal/adc_oneshot_hal.c", "/home/<USER>/esp32/esp-idf/components/hal/adc_hal.c", "/home/<USER>/esp32/esp-idf/components/hal/lcd_hal.c", "/home/<USER>/esp32/esp-idf/components/hal/mpi_hal.c", "/home/<USER>/esp32/esp-idf/components/hal/sha_hal.c", "/home/<USER>/esp32/esp-idf/components/hal/aes_hal.c", "/home/<USER>/esp32/esp-idf/components/hal/brownout_hal.c", "/home/<USER>/esp32/esp-idf/components/hal/spi_hal.c", "/home/<USER>/esp32/esp-idf/components/hal/spi_hal_iram.c", "/home/<USER>/esp32/esp-idf/components/hal/spi_slave_hal.c", "/home/<USER>/esp32/esp-idf/components/hal/spi_slave_hal_iram.c", "/home/<USER>/esp32/esp-idf/components/hal/spi_slave_hd_hal.c", "/home/<USER>/esp32/esp-idf/components/hal/spi_flash_hal_gpspi.c", "/home/<USER>/esp32/esp-idf/components/hal/hmac_hal.c", "/home/<USER>/esp32/esp-idf/components/hal/ds_hal.c", "/home/<USER>/esp32/esp-idf/components/hal/usb_serial_jtag_hal.c", "/home/<USER>/esp32/esp-idf/components/hal/usb_dwc_hal.c", "/home/<USER>/esp32/esp-idf/components/hal/usb_wrap_hal.c", "/home/<USER>/esp32/esp-idf/components/hal/esp32s3/touch_sensor_hal.c", "/home/<USER>/esp32/esp-idf/components/hal/touch_sensor_hal.c", "/home/<USER>/esp32/esp-idf/components/hal/xt_wdt_hal.c", "/home/<USER>/esp32/esp-idf/components/hal/esp32s3/rtc_cntl_hal.c"], "include_dirs": ["platform_port/include", "esp32s3/include", "include"]}, "heap": {"alias": "idf::heap", "target": "___idf_heap", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/heap", "type": "LIBRARY", "lib": "__idf_heap", "reqs": [], "priv_reqs": ["soc"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/heap/libheap.a", "sources": ["/home/<USER>/esp32/esp-idf/components/heap/heap_caps_base.c", "/home/<USER>/esp32/esp-idf/components/heap/heap_caps.c", "/home/<USER>/esp32/esp-idf/components/heap/heap_caps_init.c", "/home/<USER>/esp32/esp-idf/components/heap/multi_heap.c", "/home/<USER>/esp32/esp-idf/components/heap/tlsf/tlsf.c", "/home/<USER>/esp32/esp-idf/components/heap/port/memory_layout_utils.c", "/home/<USER>/esp32/esp-idf/components/heap/port/esp32s3/memory_layout.c"], "include_dirs": ["include", "tlsf"]}, "http_parser": {"alias": "idf::http_parser", "target": "___idf_http_parser", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/http_parser", "type": "LIBRARY", "lib": "__idf_http_parser", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/http_parser/libhttp_parser.a", "sources": ["/home/<USER>/esp32/esp-idf/components/http_parser/http_parser.c"], "include_dirs": ["."]}, "idf_test": {"alias": "idf::idf_test", "target": "___idf_idf_test", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/idf_test", "type": "CONFIG_ONLY", "lib": "__idf_idf_test", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["include", "include/esp32s3"]}, "ieee802154": {"alias": "idf::ieee802154", "target": "___idf_ieee802154", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/ieee802154", "type": "CONFIG_ONLY", "lib": "__idf_ieee802154", "reqs": ["esp_coex"], "priv_reqs": ["esp_phy", "driver", "esp_timer", "soc", "hal"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["include"]}, "json": {"alias": "idf::json", "target": "___idf_json", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/json", "type": "LIBRARY", "lib": "__idf_json", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/json/libjson.a", "sources": ["/home/<USER>/esp32/esp-idf/components/json/cJSON/cJSON.c", "/home/<USER>/esp32/esp-idf/components/json/cJSON/cJSON_Utils.c"], "include_dirs": ["cJSON"]}, "log": {"alias": "idf::log", "target": "___idf_log", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/log", "type": "LIBRARY", "lib": "__idf_log", "reqs": [], "priv_reqs": ["soc", "hal", "esp_hw_support"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/log/liblog.a", "sources": ["/home/<USER>/esp32/esp-idf/components/log/src/os/log_timestamp.c", "/home/<USER>/esp32/esp-idf/components/log/src/log_timestamp_common.c", "/home/<USER>/esp32/esp-idf/components/log/src/os/log_lock.c", "/home/<USER>/esp32/esp-idf/components/log/src/os/log_write.c", "/home/<USER>/esp32/esp-idf/components/log/src/buffer/log_buffers.c", "/home/<USER>/esp32/esp-idf/components/log/src/util.c", "/home/<USER>/esp32/esp-idf/components/log/src/log_level/log_level.c", "/home/<USER>/esp32/esp-idf/components/log/src/log_level/tag_log_level/tag_log_level.c", "/home/<USER>/esp32/esp-idf/components/log/src/log_level/tag_log_level/linked_list/log_linked_list.c", "/home/<USER>/esp32/esp-idf/components/log/src/log_level/tag_log_level/cache/log_binary_heap.c"], "include_dirs": ["include"]}, "lvgl__lvgl": {"alias": "idf::lvgl__lvgl", "target": "___idf_lvgl__lvgl", "prefix": "idf", "dir": "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl", "type": "LIBRARY", "lib": "__idf_lvgl__lvgl", "reqs": ["esp_timer"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/lvgl__lvgl/liblvgl__lvgl.a", "sources": ["/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/core/lv_group.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/core/lv_obj.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/core/lv_obj_class.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/core/lv_obj_draw.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/core/lv_obj_event.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/core/lv_obj_id_builtin.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/core/lv_obj_pos.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/core/lv_obj_property.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/core/lv_obj_scroll.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/core/lv_obj_style.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/core/lv_obj_style_gen.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/core/lv_obj_tree.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/core/lv_refr.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/display/lv_display.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/draw/lv_draw.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/draw/lv_draw_arc.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/draw/lv_draw_buf.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/draw/lv_draw_image.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/draw/lv_draw_label.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/draw/lv_draw_line.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/draw/lv_draw_mask.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/draw/lv_draw_rect.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/draw/lv_draw_triangle.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/draw/lv_draw_vector.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/draw/lv_image_decoder.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/draw/nxp/pxp/lv_draw_buf_pxp.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/draw/nxp/pxp/lv_draw_pxp.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/draw/nxp/pxp/lv_draw_pxp_fill.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/draw/nxp/pxp/lv_draw_pxp_img.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/draw/nxp/pxp/lv_draw_pxp_layer.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/draw/nxp/pxp/lv_pxp_cfg.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/draw/nxp/pxp/lv_pxp_osa.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/draw/nxp/pxp/lv_pxp_utils.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/draw/nxp/vglite/lv_draw_buf_vglite.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/draw/nxp/vglite/lv_draw_vglite.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/draw/nxp/vglite/lv_draw_vglite_arc.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/draw/nxp/vglite/lv_draw_vglite_border.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/draw/nxp/vglite/lv_draw_vglite_fill.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/draw/nxp/vglite/lv_draw_vglite_img.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/draw/nxp/vglite/lv_draw_vglite_label.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/draw/nxp/vglite/lv_draw_vglite_layer.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/draw/nxp/vglite/lv_draw_vglite_line.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/draw/nxp/vglite/lv_draw_vglite_triangle.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/draw/nxp/vglite/lv_vglite_buf.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/draw/nxp/vglite/lv_vglite_matrix.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/draw/nxp/vglite/lv_vglite_path.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/draw/nxp/vglite/lv_vglite_utils.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/draw/renesas/dave2d/lv_draw_dave2d.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/draw/renesas/dave2d/lv_draw_dave2d_arc.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/draw/renesas/dave2d/lv_draw_dave2d_border.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/draw/renesas/dave2d/lv_draw_dave2d_fill.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/draw/renesas/dave2d/lv_draw_dave2d_image.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/draw/renesas/dave2d/lv_draw_dave2d_label.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/draw/renesas/dave2d/lv_draw_dave2d_line.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/draw/renesas/dave2d/lv_draw_dave2d_mask_rectangle.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/draw/renesas/dave2d/lv_draw_dave2d_triangle.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/draw/renesas/dave2d/lv_draw_dave2d_utils.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/draw/sdl/lv_draw_sdl.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/draw/sw/blend/lv_draw_sw_blend.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/draw/sw/blend/lv_draw_sw_blend_to_al88.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/draw/sw/blend/lv_draw_sw_blend_to_argb8888.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/draw/sw/blend/lv_draw_sw_blend_to_i1.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/draw/sw/blend/lv_draw_sw_blend_to_l8.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/draw/sw/blend/lv_draw_sw_blend_to_rgb565.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/draw/sw/blend/lv_draw_sw_blend_to_rgb888.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/draw/sw/lv_draw_sw.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/draw/sw/lv_draw_sw_arc.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/draw/sw/lv_draw_sw_border.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/draw/sw/lv_draw_sw_box_shadow.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/draw/sw/lv_draw_sw_fill.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/draw/sw/lv_draw_sw_gradient.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/draw/sw/lv_draw_sw_img.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/draw/sw/lv_draw_sw_letter.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/draw/sw/lv_draw_sw_line.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/draw/sw/lv_draw_sw_mask.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/draw/sw/lv_draw_sw_mask_rect.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/draw/sw/lv_draw_sw_transform.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/draw/sw/lv_draw_sw_triangle.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/draw/sw/lv_draw_sw_vector.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/draw/vg_lite/lv_draw_buf_vg_lite.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/draw/vg_lite/lv_draw_vg_lite.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/draw/vg_lite/lv_draw_vg_lite_arc.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/draw/vg_lite/lv_draw_vg_lite_border.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/draw/vg_lite/lv_draw_vg_lite_box_shadow.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/draw/vg_lite/lv_draw_vg_lite_fill.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/draw/vg_lite/lv_draw_vg_lite_img.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/draw/vg_lite/lv_draw_vg_lite_label.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/draw/vg_lite/lv_draw_vg_lite_layer.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/draw/vg_lite/lv_draw_vg_lite_line.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/draw/vg_lite/lv_draw_vg_lite_mask_rect.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/draw/vg_lite/lv_draw_vg_lite_triangle.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/draw/vg_lite/lv_draw_vg_lite_vector.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/draw/vg_lite/lv_vg_lite_decoder.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/draw/vg_lite/lv_vg_lite_grad.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/draw/vg_lite/lv_vg_lite_math.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/draw/vg_lite/lv_vg_lite_path.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/draw/vg_lite/lv_vg_lite_pending.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/draw/vg_lite/lv_vg_lite_stroke.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/draw/vg_lite/lv_vg_lite_utils.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/drivers/display/drm/lv_linux_drm.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/drivers/display/fb/lv_linux_fbdev.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/drivers/display/ili9341/lv_ili9341.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/drivers/display/lcd/lv_lcd_generic_mipi.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/drivers/display/renesas_glcdc/lv_renesas_glcdc.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/drivers/display/st7735/lv_st7735.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/drivers/display/st7789/lv_st7789.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/drivers/display/st7796/lv_st7796.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/drivers/display/tft_espi/lv_tft_espi.cpp", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/drivers/evdev/lv_evdev.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/drivers/glfw/lv_glfw_window.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/drivers/glfw/lv_opengles_debug.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/drivers/glfw/lv_opengles_driver.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/drivers/glfw/lv_opengles_texture.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/drivers/libinput/lv_libinput.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/drivers/libinput/lv_xkb.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/drivers/nuttx/lv_nuttx_cache.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/drivers/nuttx/lv_nuttx_entry.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/drivers/nuttx/lv_nuttx_fbdev.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/drivers/nuttx/lv_nuttx_image_cache.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/drivers/nuttx/lv_nuttx_lcd.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/drivers/nuttx/lv_nuttx_libuv.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/drivers/nuttx/lv_nuttx_profiler.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/drivers/nuttx/lv_nuttx_touchscreen.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/drivers/qnx/lv_qnx.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/drivers/sdl/lv_sdl_keyboard.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/drivers/sdl/lv_sdl_mouse.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/drivers/sdl/lv_sdl_mousewheel.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/drivers/sdl/lv_sdl_window.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/drivers/wayland/lv_wayland.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/drivers/wayland/lv_wayland_smm.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/drivers/windows/lv_windows_context.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/drivers/windows/lv_windows_display.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/drivers/windows/lv_windows_input.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/drivers/x11/lv_x11_display.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/drivers/x11/lv_x11_input.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/font/lv_binfont_loader.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/font/lv_font.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/font/lv_font_dejavu_16_persian_hebrew.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/font/lv_font_fmt_txt.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/font/lv_font_montserrat_10.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/font/lv_font_montserrat_12.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/font/lv_font_montserrat_14.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/font/lv_font_montserrat_16.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/font/lv_font_montserrat_18.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/font/lv_font_montserrat_20.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/font/lv_font_montserrat_22.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/font/lv_font_montserrat_24.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/font/lv_font_montserrat_26.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/font/lv_font_montserrat_28.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/font/lv_font_montserrat_28_compressed.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/font/lv_font_montserrat_30.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/font/lv_font_montserrat_32.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/font/lv_font_montserrat_34.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/font/lv_font_montserrat_36.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/font/lv_font_montserrat_38.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/font/lv_font_montserrat_40.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/font/lv_font_montserrat_42.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/font/lv_font_montserrat_44.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/font/lv_font_montserrat_46.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/font/lv_font_montserrat_48.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/font/lv_font_montserrat_8.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/font/lv_font_simsun_14_cjk.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/font/lv_font_simsun_16_cjk.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/font/lv_font_unscii_16.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/font/lv_font_unscii_8.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/indev/lv_indev.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/indev/lv_indev_scroll.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/layouts/flex/lv_flex.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/layouts/grid/lv_grid.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/layouts/lv_layout.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/libs/barcode/code128.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/libs/barcode/lv_barcode.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/libs/bin_decoder/lv_bin_decoder.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/libs/bmp/lv_bmp.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/libs/ffmpeg/lv_ffmpeg.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/libs/freetype/lv_freetype.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/libs/freetype/lv_freetype_glyph.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/libs/freetype/lv_freetype_image.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/libs/freetype/lv_freetype_outline.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/libs/freetype/lv_ftsystem.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/libs/fsdrv/lv_fs_arduino_esp_littlefs.cpp", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/libs/fsdrv/lv_fs_arduino_sd.cpp", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/libs/fsdrv/lv_fs_cbfs.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/libs/fsdrv/lv_fs_fatfs.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/libs/fsdrv/lv_fs_littlefs.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/libs/fsdrv/lv_fs_memfs.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/libs/fsdrv/lv_fs_posix.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/libs/fsdrv/lv_fs_stdio.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/libs/fsdrv/lv_fs_win32.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/libs/gif/gifdec.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/libs/gif/lv_gif.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/libs/libjpeg_turbo/lv_libjpeg_turbo.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/libs/libpng/lv_libpng.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/libs/lodepng/lodepng.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/libs/lodepng/lv_lodepng.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/libs/lz4/lz4.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/libs/qrcode/lv_qrcode.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/libs/qrcode/qrcodegen.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/libs/rle/lv_rle.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/libs/rlottie/lv_rlottie.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/libs/thorvg/tvgAccessor.cpp", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/libs/thorvg/tvgAnimation.cpp", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/libs/thorvg/tvgCanvas.cpp", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/libs/thorvg/tvgCapi.cpp", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/libs/thorvg/tvgCompressor.cpp", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/libs/thorvg/tvgFill.cpp", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/libs/thorvg/tvgGlCanvas.cpp", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/libs/thorvg/tvgInitializer.cpp", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/libs/thorvg/tvgLines.cpp", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/libs/thorvg/tvgLoader.cpp", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/libs/thorvg/tvgLottieAnimation.cpp", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/libs/thorvg/tvgLottieBuilder.cpp", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/libs/thorvg/tvgLottieExpressions.cpp", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/libs/thorvg/tvgLottieInterpolator.cpp", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/libs/thorvg/tvgLottieLoader.cpp", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/libs/thorvg/tvgLottieModel.cpp", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/libs/thorvg/tvgLottieParser.cpp", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/libs/thorvg/tvgLottieParserHandler.cpp", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/libs/thorvg/tvgMath.cpp", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/libs/thorvg/tvgPaint.cpp", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/libs/thorvg/tvgPicture.cpp", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/libs/thorvg/tvgRawLoader.cpp", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/libs/thorvg/tvgRender.cpp", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/libs/thorvg/tvgSaver.cpp", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/libs/thorvg/tvgScene.cpp", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/libs/thorvg/tvgShape.cpp", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/libs/thorvg/tvgStr.cpp", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/libs/thorvg/tvgSvgCssStyle.cpp", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/libs/thorvg/tvgSvgLoader.cpp", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/libs/thorvg/tvgSvgPath.cpp", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/libs/thorvg/tvgSvgSceneBuilder.cpp", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/libs/thorvg/tvgSvgUtil.cpp", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/libs/thorvg/tvgSwCanvas.cpp", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/libs/thorvg/tvgSwFill.cpp", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/libs/thorvg/tvgSwImage.cpp", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/libs/thorvg/tvgSwMath.cpp", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/libs/thorvg/tvgSwMemPool.cpp", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/libs/thorvg/tvgSwRaster.cpp", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/libs/thorvg/tvgSwRenderer.cpp", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/libs/thorvg/tvgSwRle.cpp", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/libs/thorvg/tvgSwShape.cpp", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/libs/thorvg/tvgSwStroke.cpp", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/libs/thorvg/tvgTaskScheduler.cpp", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/libs/thorvg/tvgText.cpp", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/libs/thorvg/tvgWgCanvas.cpp", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/libs/thorvg/tvgXmlParser.cpp", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/libs/tiny_ttf/lv_tiny_ttf.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/libs/tjpgd/lv_tjpgd.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/libs/tjpgd/tjpgd.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/lv_init.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/misc/cache/lv_cache.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/misc/cache/lv_cache_entry.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/misc/cache/lv_cache_lru_rb.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/misc/cache/lv_image_cache.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/misc/cache/lv_image_header_cache.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/misc/lv_anim.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/misc/lv_anim_timeline.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/misc/lv_area.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/misc/lv_array.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/misc/lv_async.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/misc/lv_bidi.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/misc/lv_color.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/misc/lv_color_op.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/misc/lv_event.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/misc/lv_fs.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/misc/lv_ll.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/misc/lv_log.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/misc/lv_lru.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/misc/lv_math.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/misc/lv_matrix.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/misc/lv_palette.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/misc/lv_profiler_builtin.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/misc/lv_rb.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/misc/lv_style.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/misc/lv_style_gen.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/misc/lv_templ.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/misc/lv_text.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/misc/lv_text_ap.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/misc/lv_timer.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/misc/lv_utils.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/osal/lv_cmsis_rtos2.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/osal/lv_freertos.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/osal/lv_mqx.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/osal/lv_os.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/osal/lv_os_none.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/osal/lv_pthread.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/osal/lv_rtthread.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/osal/lv_windows.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/others/file_explorer/lv_file_explorer.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/others/fragment/lv_fragment.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/others/fragment/lv_fragment_manager.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/others/gridnav/lv_gridnav.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/others/ime/lv_ime_pinyin.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/others/imgfont/lv_imgfont.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/others/monkey/lv_monkey.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/others/observer/lv_observer.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/others/snapshot/lv_snapshot.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/others/sysmon/lv_sysmon.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/others/vg_lite_tvg/vg_lite_matrix.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/others/vg_lite_tvg/vg_lite_tvg.cpp", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/stdlib/builtin/lv_mem_core_builtin.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/stdlib/builtin/lv_sprintf_builtin.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/stdlib/builtin/lv_string_builtin.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/stdlib/builtin/lv_tlsf.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/stdlib/clib/lv_mem_core_clib.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/stdlib/clib/lv_sprintf_clib.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/stdlib/clib/lv_string_clib.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/stdlib/lv_mem.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/stdlib/micropython/lv_mem_core_micropython.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/stdlib/rtthread/lv_mem_core_rtthread.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/stdlib/rtthread/lv_sprintf_rtthread.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/stdlib/rtthread/lv_string_rtthread.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/themes/default/lv_theme_default.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/themes/lv_theme.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/themes/mono/lv_theme_mono.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/themes/simple/lv_theme_simple.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/tick/lv_tick.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/widgets/animimage/lv_animimage.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/widgets/arc/lv_arc.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/widgets/bar/lv_bar.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/widgets/button/lv_button.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/widgets/buttonmatrix/lv_buttonmatrix.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/widgets/calendar/lv_calendar.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/widgets/calendar/lv_calendar_chinese.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/widgets/calendar/lv_calendar_header_arrow.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/widgets/calendar/lv_calendar_header_dropdown.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/widgets/canvas/lv_canvas.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/widgets/chart/lv_chart.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/widgets/checkbox/lv_checkbox.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/widgets/dropdown/lv_dropdown.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/widgets/image/lv_image.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/widgets/imagebutton/lv_imagebutton.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/widgets/keyboard/lv_keyboard.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/widgets/label/lv_label.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/widgets/led/lv_led.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/widgets/line/lv_line.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/widgets/list/lv_list.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/widgets/lottie/lv_lottie.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/widgets/menu/lv_menu.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/widgets/msgbox/lv_msgbox.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/widgets/objx_templ/lv_objx_templ.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/widgets/property/lv_dropdown_properties.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/widgets/property/lv_image_properties.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/widgets/property/lv_keyboard_properties.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/widgets/property/lv_label_properties.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/widgets/property/lv_obj_properties.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/widgets/property/lv_roller_properties.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/widgets/property/lv_style_properties.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/widgets/property/lv_textarea_properties.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/widgets/roller/lv_roller.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/widgets/scale/lv_scale.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/widgets/slider/lv_slider.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/widgets/span/lv_span.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/widgets/spinbox/lv_spinbox.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/widgets/spinner/lv_spinner.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/widgets/switch/lv_switch.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/widgets/table/lv_table.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/widgets/tabview/lv_tabview.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/widgets/textarea/lv_textarea.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/widgets/tileview/lv_tileview.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src/widgets/win/lv_win.c"], "include_dirs": ["/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/../", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/examples", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/demos"]}, "lwip": {"alias": "idf::lwip", "target": "___idf_lwip", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/lwip", "type": "LIBRARY", "lib": "__idf_lwip", "reqs": [], "priv_reqs": ["vfs"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/lwip/liblwip.a", "sources": ["/home/<USER>/esp32/esp-idf/components/lwip/apps/sntp/sntp.c", "/home/<USER>/esp32/esp-idf/components/lwip/lwip/src/api/api_lib.c", "/home/<USER>/esp32/esp-idf/components/lwip/lwip/src/api/api_msg.c", "/home/<USER>/esp32/esp-idf/components/lwip/lwip/src/api/err.c", "/home/<USER>/esp32/esp-idf/components/lwip/lwip/src/api/if_api.c", "/home/<USER>/esp32/esp-idf/components/lwip/lwip/src/api/netbuf.c", "/home/<USER>/esp32/esp-idf/components/lwip/lwip/src/api/netdb.c", "/home/<USER>/esp32/esp-idf/components/lwip/lwip/src/api/netifapi.c", "/home/<USER>/esp32/esp-idf/components/lwip/lwip/src/api/sockets.c", "/home/<USER>/esp32/esp-idf/components/lwip/lwip/src/api/tcpip.c", "/home/<USER>/esp32/esp-idf/components/lwip/lwip/src/apps/sntp/sntp.c", "/home/<USER>/esp32/esp-idf/components/lwip/lwip/src/apps/netbiosns/netbiosns.c", "/home/<USER>/esp32/esp-idf/components/lwip/lwip/src/core/def.c", "/home/<USER>/esp32/esp-idf/components/lwip/lwip/src/core/dns.c", "/home/<USER>/esp32/esp-idf/components/lwip/lwip/src/core/inet_chksum.c", "/home/<USER>/esp32/esp-idf/components/lwip/lwip/src/core/init.c", "/home/<USER>/esp32/esp-idf/components/lwip/lwip/src/core/ip.c", "/home/<USER>/esp32/esp-idf/components/lwip/lwip/src/core/mem.c", "/home/<USER>/esp32/esp-idf/components/lwip/lwip/src/core/memp.c", "/home/<USER>/esp32/esp-idf/components/lwip/lwip/src/core/netif.c", "/home/<USER>/esp32/esp-idf/components/lwip/lwip/src/core/pbuf.c", "/home/<USER>/esp32/esp-idf/components/lwip/lwip/src/core/raw.c", "/home/<USER>/esp32/esp-idf/components/lwip/lwip/src/core/stats.c", "/home/<USER>/esp32/esp-idf/components/lwip/lwip/src/core/sys.c", "/home/<USER>/esp32/esp-idf/components/lwip/lwip/src/core/tcp.c", "/home/<USER>/esp32/esp-idf/components/lwip/lwip/src/core/tcp_in.c", "/home/<USER>/esp32/esp-idf/components/lwip/lwip/src/core/tcp_out.c", "/home/<USER>/esp32/esp-idf/components/lwip/lwip/src/core/timeouts.c", "/home/<USER>/esp32/esp-idf/components/lwip/lwip/src/core/udp.c", "/home/<USER>/esp32/esp-idf/components/lwip/lwip/src/core/ipv4/autoip.c", "/home/<USER>/esp32/esp-idf/components/lwip/lwip/src/core/ipv4/dhcp.c", "/home/<USER>/esp32/esp-idf/components/lwip/lwip/src/core/ipv4/etharp.c", "/home/<USER>/esp32/esp-idf/components/lwip/lwip/src/core/ipv4/icmp.c", "/home/<USER>/esp32/esp-idf/components/lwip/lwip/src/core/ipv4/igmp.c", "/home/<USER>/esp32/esp-idf/components/lwip/lwip/src/core/ipv4/ip4.c", "/home/<USER>/esp32/esp-idf/components/lwip/lwip/src/core/ipv4/ip4_napt.c", "/home/<USER>/esp32/esp-idf/components/lwip/lwip/src/core/ipv4/ip4_addr.c", "/home/<USER>/esp32/esp-idf/components/lwip/lwip/src/core/ipv4/ip4_frag.c", "/home/<USER>/esp32/esp-idf/components/lwip/lwip/src/core/ipv6/dhcp6.c", "/home/<USER>/esp32/esp-idf/components/lwip/lwip/src/core/ipv6/ethip6.c", "/home/<USER>/esp32/esp-idf/components/lwip/lwip/src/core/ipv6/icmp6.c", "/home/<USER>/esp32/esp-idf/components/lwip/lwip/src/core/ipv6/inet6.c", "/home/<USER>/esp32/esp-idf/components/lwip/lwip/src/core/ipv6/ip6.c", "/home/<USER>/esp32/esp-idf/components/lwip/lwip/src/core/ipv6/ip6_addr.c", "/home/<USER>/esp32/esp-idf/components/lwip/lwip/src/core/ipv6/ip6_frag.c", "/home/<USER>/esp32/esp-idf/components/lwip/lwip/src/core/ipv6/mld6.c", "/home/<USER>/esp32/esp-idf/components/lwip/lwip/src/core/ipv6/nd6.c", "/home/<USER>/esp32/esp-idf/components/lwip/lwip/src/netif/ethernet.c", "/home/<USER>/esp32/esp-idf/components/lwip/lwip/src/netif/bridgeif.c", "/home/<USER>/esp32/esp-idf/components/lwip/lwip/src/netif/bridgeif_fdb.c", "/home/<USER>/esp32/esp-idf/components/lwip/lwip/src/netif/slipif.c", "/home/<USER>/esp32/esp-idf/components/lwip/lwip/src/netif/ppp/auth.c", "/home/<USER>/esp32/esp-idf/components/lwip/lwip/src/netif/ppp/ccp.c", "/home/<USER>/esp32/esp-idf/components/lwip/lwip/src/netif/ppp/chap-md5.c", "/home/<USER>/esp32/esp-idf/components/lwip/lwip/src/netif/ppp/chap-new.c", "/home/<USER>/esp32/esp-idf/components/lwip/lwip/src/netif/ppp/chap_ms.c", "/home/<USER>/esp32/esp-idf/components/lwip/lwip/src/netif/ppp/demand.c", "/home/<USER>/esp32/esp-idf/components/lwip/lwip/src/netif/ppp/eap.c", "/home/<USER>/esp32/esp-idf/components/lwip/lwip/src/netif/ppp/ecp.c", "/home/<USER>/esp32/esp-idf/components/lwip/lwip/src/netif/ppp/eui64.c", "/home/<USER>/esp32/esp-idf/components/lwip/lwip/src/netif/ppp/fsm.c", "/home/<USER>/esp32/esp-idf/components/lwip/lwip/src/netif/ppp/ipcp.c", "/home/<USER>/esp32/esp-idf/components/lwip/lwip/src/netif/ppp/ipv6cp.c", "/home/<USER>/esp32/esp-idf/components/lwip/lwip/src/netif/ppp/lcp.c", "/home/<USER>/esp32/esp-idf/components/lwip/lwip/src/netif/ppp/magic.c", "/home/<USER>/esp32/esp-idf/components/lwip/lwip/src/netif/ppp/mppe.c", "/home/<USER>/esp32/esp-idf/components/lwip/lwip/src/netif/ppp/multilink.c", "/home/<USER>/esp32/esp-idf/components/lwip/lwip/src/netif/ppp/ppp.c", "/home/<USER>/esp32/esp-idf/components/lwip/lwip/src/netif/ppp/pppapi.c", "/home/<USER>/esp32/esp-idf/components/lwip/lwip/src/netif/ppp/pppcrypt.c", "/home/<USER>/esp32/esp-idf/components/lwip/lwip/src/netif/ppp/pppoe.c", "/home/<USER>/esp32/esp-idf/components/lwip/lwip/src/netif/ppp/pppol2tp.c", "/home/<USER>/esp32/esp-idf/components/lwip/lwip/src/netif/ppp/pppos.c", "/home/<USER>/esp32/esp-idf/components/lwip/lwip/src/netif/ppp/upap.c", "/home/<USER>/esp32/esp-idf/components/lwip/lwip/src/netif/ppp/utils.c", "/home/<USER>/esp32/esp-idf/components/lwip/lwip/src/netif/ppp/vj.c", "/home/<USER>/esp32/esp-idf/components/lwip/port/hooks/tcp_isn_default.c", "/home/<USER>/esp32/esp-idf/components/lwip/port/hooks/lwip_default_hooks.c", "/home/<USER>/esp32/esp-idf/components/lwip/port/debug/lwip_debug.c", "/home/<USER>/esp32/esp-idf/components/lwip/port/sockets_ext.c", "/home/<USER>/esp32/esp-idf/components/lwip/port/freertos/sys_arch.c", "/home/<USER>/esp32/esp-idf/components/lwip/port/acd_dhcp_check.c", "/home/<USER>/esp32/esp-idf/components/lwip/port/esp32xx/vfs_lwip.c", "/home/<USER>/esp32/esp-idf/components/lwip/apps/ping/esp_ping.c", "/home/<USER>/esp32/esp-idf/components/lwip/apps/ping/ping.c", "/home/<USER>/esp32/esp-idf/components/lwip/apps/ping/ping_sock.c", "/home/<USER>/esp32/esp-idf/components/lwip/lwip/src/netif/ppp/polarssl/arc4.c", "/home/<USER>/esp32/esp-idf/components/lwip/lwip/src/netif/ppp/polarssl/des.c", "/home/<USER>/esp32/esp-idf/components/lwip/lwip/src/netif/ppp/polarssl/md4.c", "/home/<USER>/esp32/esp-idf/components/lwip/lwip/src/netif/ppp/polarssl/md5.c", "/home/<USER>/esp32/esp-idf/components/lwip/lwip/src/netif/ppp/polarssl/sha1.c", "/home/<USER>/esp32/esp-idf/components/lwip/apps/dhcpserver/dhcpserver.c"], "include_dirs": ["include", "include/apps", "include/apps/sntp", "lwip/src/include", "port/include", "port/freertos/include/", "port/esp32xx/include", "port/esp32xx/include/arch", "port/esp32xx/include/sys"]}, "main": {"alias": "idf::main", "target": "___idf_main", "prefix": "idf", "dir": "/home/<USER>/bysx/xiaozhi-esp32/main", "type": "LIBRARY", "lib": "__idf_main", "reqs": [], "priv_reqs": ["78__esp-ml307", "78__esp-opus-encoder", "78__esp-wifi-connect", "78__esp_lcd_nv3023", "78__xia<PERSON><PERSON>-fonts", "espressif__adc_battery_estimation", "espressif__adc_mic", "espressif__button", "espressif__esp-sr", "espressif__esp32-camera", "espressif__esp_codec_dev", "espressif__esp_io_expander_tca9554", "espressif__esp_io_expander_tca95xx_16bit", "espressif__esp_lcd_axs15231b", "espressif__esp_lcd_gc9a01", "espressif__esp_lcd_ili9341", "espressif__esp_lcd_panel_io_additions", "espressif__esp_lcd_spd2010", "espressif__esp_lcd_st77916", "espressif__esp_lcd_st7796", "espressif__esp_lcd_touch_cst816s", "espressif__esp_lcd_touch_ft5x06", "espressif__esp_lcd_touch_gt911", "espressif__esp_lvgl_port", "espressif__esp_mmap_assets", "espressif__knob", "espressif__led_strip", "espressif2022__image_player", "lvgl__lvgl", "tny-robotics__sh1106-esp-idf", "txp666__otto-emoji-gif-component", "waveshare__esp_lcd_sh8601", "waveshare__esp_lcd_touch_cst9217", "wvirgil123__esp_jpeg_simd", "wvirgil123__sscma_client"], "managed_reqs": [], "managed_priv_reqs": ["78__esp-ml307", "78__esp-opus-encoder", "78__esp-wifi-connect", "78__esp_lcd_nv3023", "78__xia<PERSON><PERSON>-fonts", "espressif__adc_battery_estimation", "espressif__adc_mic", "espressif__button", "espressif__esp-sr", "espressif__esp32-camera", "espressif__esp_codec_dev", "espressif__esp_io_expander_tca9554", "espressif__esp_io_expander_tca95xx_16bit", "espressif__esp_lcd_axs15231b", "espressif__esp_lcd_gc9a01", "espressif__esp_lcd_ili9341", "espressif__esp_lcd_panel_io_additions", "espressif__esp_lcd_spd2010", "espressif__esp_lcd_st77916", "espressif__esp_lcd_st7796", "espressif__esp_lcd_touch_cst816s", "espressif__esp_lcd_touch_ft5x06", "espressif__esp_lcd_touch_gt911", "espressif__esp_lvgl_port", "espressif__esp_mmap_assets", "espressif__knob", "espressif__led_strip", "espressif2022__image_player", "lvgl__lvgl", "tny-robotics__sh1106-esp-idf", "txp666__otto-emoji-gif-component", "waveshare__esp_lcd_sh8601", "waveshare__esp_lcd_touch_cst9217", "wvirgil123__esp_jpeg_simd", "wvirgil123__sscma_client"], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/main/libmain.a", "sources": ["/home/<USER>/bysx/xiaozhi-esp32/main/audio/audio_codec.cc", "/home/<USER>/bysx/xiaozhi-esp32/main/audio/audio_service.cc", "/home/<USER>/bysx/xiaozhi-esp32/main/audio/codecs/no_audio_codec.cc", "/home/<USER>/bysx/xiaozhi-esp32/main/audio/codecs/box_audio_codec.cc", "/home/<USER>/bysx/xiaozhi-esp32/main/audio/codecs/es8311_audio_codec.cc", "/home/<USER>/bysx/xiaozhi-esp32/main/audio/codecs/es8374_audio_codec.cc", "/home/<USER>/bysx/xiaozhi-esp32/main/audio/codecs/es8388_audio_codec.cc", "/home/<USER>/bysx/xiaozhi-esp32/main/audio/codecs/es8389_audio_codec.cc", "/home/<USER>/bysx/xiaozhi-esp32/main/audio/codecs/dummy_audio_codec.cc", "/home/<USER>/bysx/xiaozhi-esp32/main/audio/processors/audio_debugger.cc", "/home/<USER>/bysx/xiaozhi-esp32/main/led/single_led.cc", "/home/<USER>/bysx/xiaozhi-esp32/main/led/circular_strip.cc", "/home/<USER>/bysx/xiaozhi-esp32/main/led/gpio_led.cc", "/home/<USER>/bysx/xiaozhi-esp32/main/display/display.cc", "/home/<USER>/bysx/xiaozhi-esp32/main/display/lcd_display.cc", "/home/<USER>/bysx/xiaozhi-esp32/main/display/oled_display.cc", "/home/<USER>/bysx/xiaozhi-esp32/main/protocols/protocol.cc", "/home/<USER>/bysx/xiaozhi-esp32/main/protocols/mqtt_protocol.cc", "/home/<USER>/bysx/xiaozhi-esp32/main/protocols/websocket_protocol.cc", "/home/<USER>/bysx/xiaozhi-esp32/main/mcp_server.cc", "/home/<USER>/bysx/xiaozhi-esp32/main/system_info.cc", "/home/<USER>/bysx/xiaozhi-esp32/main/application.cc", "/home/<USER>/bysx/xiaozhi-esp32/main/ota.cc", "/home/<USER>/bysx/xiaozhi-esp32/main/settings.cc", "/home/<USER>/bysx/xiaozhi-esp32/main/device_state_event.cc", "/home/<USER>/bysx/xiaozhi-esp32/main/main.cc", "/home/<USER>/bysx/xiaozhi-esp32/main/boards/common/adc_battery_monitor.cc", "/home/<USER>/bysx/xiaozhi-esp32/main/boards/common/afsk_demod.cc", "/home/<USER>/bysx/xiaozhi-esp32/main/boards/common/axp2101.cc", "/home/<USER>/bysx/xiaozhi-esp32/main/boards/common/backlight.cc", "/home/<USER>/bysx/xiaozhi-esp32/main/boards/common/board.cc", "/home/<USER>/bysx/xiaozhi-esp32/main/boards/common/button.cc", "/home/<USER>/bysx/xiaozhi-esp32/main/boards/common/dual_network_board.cc", "/home/<USER>/bysx/xiaozhi-esp32/main/boards/common/esp32_camera.cc", "/home/<USER>/bysx/xiaozhi-esp32/main/boards/common/i2c_device.cc", "/home/<USER>/bysx/xiaozhi-esp32/main/boards/common/knob.cc", "/home/<USER>/bysx/xiaozhi-esp32/main/boards/common/ml307_board.cc", "/home/<USER>/bysx/xiaozhi-esp32/main/boards/common/power_save_timer.cc", "/home/<USER>/bysx/xiaozhi-esp32/main/boards/common/sleep_timer.cc", "/home/<USER>/bysx/xiaozhi-esp32/main/boards/common/sy6970.cc", "/home/<USER>/bysx/xiaozhi-esp32/main/boards/common/system_reset.cc", "/home/<USER>/bysx/xiaozhi-esp32/main/boards/common/wifi_board.cc", "/home/<USER>/bysx/xiaozhi-esp32/main/boards/bread-compact-wifi/compact_wifi_board.cc", "/home/<USER>/bysx/xiaozhi-esp32/main/audio/processors/afe_audio_processor.cc", "/home/<USER>/bysx/xiaozhi-esp32/main/audio/wake_words/afe_wake_word.cc", "/home/<USER>/bysx/xiaozhi-esp32/build/0.p3.S", "/home/<USER>/bysx/xiaozhi-esp32/build/1.p3.S", "/home/<USER>/bysx/xiaozhi-esp32/build/2.p3.S", "/home/<USER>/bysx/xiaozhi-esp32/build/3.p3.S", "/home/<USER>/bysx/xiaozhi-esp32/build/4.p3.S", "/home/<USER>/bysx/xiaozhi-esp32/build/5.p3.S", "/home/<USER>/bysx/xiaozhi-esp32/build/6.p3.S", "/home/<USER>/bysx/xiaozhi-esp32/build/7.p3.S", "/home/<USER>/bysx/xiaozhi-esp32/build/8.p3.S", "/home/<USER>/bysx/xiaozhi-esp32/build/9.p3.S", "/home/<USER>/bysx/xiaozhi-esp32/build/activation.p3.S", "/home/<USER>/bysx/xiaozhi-esp32/build/err_pin.p3.S", "/home/<USER>/bysx/xiaozhi-esp32/build/err_reg.p3.S", "/home/<USER>/bysx/xiaozhi-esp32/build/upgrade.p3.S", "/home/<USER>/bysx/xiaozhi-esp32/build/welcome.p3.S", "/home/<USER>/bysx/xiaozhi-esp32/build/wificonfig.p3.S", "/home/<USER>/bysx/xiaozhi-esp32/build/exclamation.p3.S", "/home/<USER>/bysx/xiaozhi-esp32/build/low_battery.p3.S", "/home/<USER>/bysx/xiaozhi-esp32/build/popup.p3.S", "/home/<USER>/bysx/xiaozhi-esp32/build/success.p3.S", "/home/<USER>/bysx/xiaozhi-esp32/build/vibration.p3.S"], "include_dirs": [".", "display", "audio", "protocols", "/home/<USER>/bysx/xiaozhi-esp32/main/boards/common"]}, "mbedtls": {"alias": "idf::mbedtls", "target": "___idf_mbedtls", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/mbedtls", "type": "LIBRARY", "lib": "__idf_mbedtls", "reqs": [], "priv_reqs": ["soc", "esp_hw_support", "esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/mbedtls/libmbedtls.a", "sources": ["/home/<USER>/esp32/esp-idf/components/mbedtls/esp_crt_bundle/esp_crt_bundle.c", "/home/<USER>/bysx/xiaozhi-esp32/build/x509_crt_bundle.S"], "include_dirs": ["port/include", "mbedtls/include", "mbedtls/library", "esp_crt_bundle/include"]}, "mqtt": {"alias": "idf::mqtt", "target": "___idf_mqtt", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/mqtt", "type": "LIBRARY", "lib": "__idf_mqtt", "reqs": ["esp_event", "tcp_transport"], "priv_reqs": ["esp_timer", "http_parser", "esp_hw_support", "heap"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/mqtt/libmqtt.a", "sources": ["/home/<USER>/esp32/esp-idf/components/mqtt/esp-mqtt/mqtt_client.c", "/home/<USER>/esp32/esp-idf/components/mqtt/esp-mqtt/lib/mqtt_msg.c", "/home/<USER>/esp32/esp-idf/components/mqtt/esp-mqtt/lib/mqtt_outbox.c", "/home/<USER>/esp32/esp-idf/components/mqtt/esp-mqtt/lib/platform_esp32_idf.c"], "include_dirs": ["/home/<USER>/esp32/esp-idf/components/mqtt/esp-mqtt/include"]}, "newlib": {"alias": "idf::newlib", "target": "___idf_newlib", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/newlib", "type": "LIBRARY", "lib": "__idf_newlib", "reqs": [], "priv_reqs": ["soc", "spi_flash"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/newlib/libnewlib.a", "sources": ["/home/<USER>/esp32/esp-idf/components/newlib/abort.c", "/home/<USER>/esp32/esp-idf/components/newlib/assert.c", "/home/<USER>/esp32/esp-idf/components/newlib/heap.c", "/home/<USER>/esp32/esp-idf/components/newlib/flockfile.c", "/home/<USER>/esp32/esp-idf/components/newlib/locks.c", "/home/<USER>/esp32/esp-idf/components/newlib/poll.c", "/home/<USER>/esp32/esp-idf/components/newlib/pthread.c", "/home/<USER>/esp32/esp-idf/components/newlib/random.c", "/home/<USER>/esp32/esp-idf/components/newlib/getentropy.c", "/home/<USER>/esp32/esp-idf/components/newlib/reent_init.c", "/home/<USER>/esp32/esp-idf/components/newlib/newlib_init.c", "/home/<USER>/esp32/esp-idf/components/newlib/syscalls.c", "/home/<USER>/esp32/esp-idf/components/newlib/termios.c", "/home/<USER>/esp32/esp-idf/components/newlib/stdatomic.c", "/home/<USER>/esp32/esp-idf/components/newlib/time.c", "/home/<USER>/esp32/esp-idf/components/newlib/sysconf.c", "/home/<USER>/esp32/esp-idf/components/newlib/realpath.c", "/home/<USER>/esp32/esp-idf/components/newlib/scandir.c", "/home/<USER>/esp32/esp-idf/components/newlib/port/xtensa/stdatomic_s32c1i.c", "/home/<USER>/esp32/esp-idf/components/newlib/port/esp_time_impl.c"], "include_dirs": ["platform_include"]}, "nvs_flash": {"alias": "idf::nvs_flash", "target": "___idf_nvs_flash", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/nvs_flash", "type": "LIBRARY", "lib": "__idf_nvs_flash", "reqs": ["esp_partition"], "priv_reqs": ["spi_flash", "newlib"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/nvs_flash/libnvs_flash.a", "sources": ["/home/<USER>/esp32/esp-idf/components/nvs_flash/src/nvs_api.cpp", "/home/<USER>/esp32/esp-idf/components/nvs_flash/src/nvs_cxx_api.cpp", "/home/<USER>/esp32/esp-idf/components/nvs_flash/src/nvs_item_hash_list.cpp", "/home/<USER>/esp32/esp-idf/components/nvs_flash/src/nvs_page.cpp", "/home/<USER>/esp32/esp-idf/components/nvs_flash/src/nvs_pagemanager.cpp", "/home/<USER>/esp32/esp-idf/components/nvs_flash/src/nvs_storage.cpp", "/home/<USER>/esp32/esp-idf/components/nvs_flash/src/nvs_handle_simple.cpp", "/home/<USER>/esp32/esp-idf/components/nvs_flash/src/nvs_handle_locked.cpp", "/home/<USER>/esp32/esp-idf/components/nvs_flash/src/nvs_partition.cpp", "/home/<USER>/esp32/esp-idf/components/nvs_flash/src/nvs_partition_lookup.cpp", "/home/<USER>/esp32/esp-idf/components/nvs_flash/src/nvs_partition_manager.cpp", "/home/<USER>/esp32/esp-idf/components/nvs_flash/src/nvs_types.cpp", "/home/<USER>/esp32/esp-idf/components/nvs_flash/src/nvs_platform.cpp", "/home/<USER>/esp32/esp-idf/components/nvs_flash/src/nvs_bootloader.c", "/home/<USER>/esp32/esp-idf/components/nvs_flash/src/nvs_encrypted_partition.cpp"], "include_dirs": ["include"]}, "nvs_sec_provider": {"alias": "idf::nvs_sec_provider", "target": "___idf_nvs_sec_provider", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/nvs_sec_provider", "type": "LIBRARY", "lib": "__idf_nvs_sec_provider", "reqs": [], "priv_reqs": ["bootloader_support", "efuse", "esp_partition", "nvs_flash"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/nvs_sec_provider/libnvs_sec_provider.a", "sources": ["/home/<USER>/esp32/esp-idf/components/nvs_sec_provider/nvs_sec_provider.c"], "include_dirs": ["include"]}, "openthread": {"alias": "idf::openthread", "target": "___idf_openthread", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/openthread", "type": "CONFIG_ONLY", "lib": "__idf_openthread", "reqs": ["esp_netif", "lwip", "esp_driver_uart", "driver"], "priv_reqs": ["console", "esp_coex", "esp_event", "esp_partition", "esp_timer", "ieee802154", "mbedtls", "nvs_flash"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": []}, "partition_table": {"alias": "idf::partition_table", "target": "___idf_partition_table", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/partition_table", "type": "CONFIG_ONLY", "lib": "__idf_partition_table", "reqs": [], "priv_reqs": ["esptool_py"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": []}, "perfmon": {"alias": "idf::perfmon", "target": "___idf_perfmon", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/perfmon", "type": "LIBRARY", "lib": "__idf_perfmon", "reqs": ["xtensa"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/perfmon/libperfmon.a", "sources": ["/home/<USER>/esp32/esp-idf/components/perfmon/xtensa_perfmon_access.c", "/home/<USER>/esp32/esp-idf/components/perfmon/xtensa_perfmon_apis.c", "/home/<USER>/esp32/esp-idf/components/perfmon/xtensa_perfmon_masks.c"], "include_dirs": ["include"]}, "protobuf-c": {"alias": "idf::protobuf-c", "target": "___idf_protobuf-c", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/protobuf-c", "type": "LIBRARY", "lib": "__idf_protobuf-c", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/protobuf-c/libprotobuf-c.a", "sources": ["/home/<USER>/esp32/esp-idf/components/protobuf-c/protobuf-c/protobuf-c/protobuf-c.c"], "include_dirs": ["protobuf-c"]}, "protocomm": {"alias": "idf::protocomm", "target": "___idf_protocomm", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/protocomm", "type": "LIBRARY", "lib": "__idf_protocomm", "reqs": ["bt"], "priv_reqs": ["protobuf-c", "mbedtls", "console", "esp_http_server", "driver"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/protocomm/libprotocomm.a", "sources": ["/home/<USER>/esp32/esp-idf/components/protocomm/src/common/protocomm.c", "/home/<USER>/esp32/esp-idf/components/protocomm/proto-c/constants.pb-c.c", "/home/<USER>/esp32/esp-idf/components/protocomm/proto-c/sec0.pb-c.c", "/home/<USER>/esp32/esp-idf/components/protocomm/proto-c/sec1.pb-c.c", "/home/<USER>/esp32/esp-idf/components/protocomm/proto-c/sec2.pb-c.c", "/home/<USER>/esp32/esp-idf/components/protocomm/proto-c/session.pb-c.c", "/home/<USER>/esp32/esp-idf/components/protocomm/src/transports/protocomm_console.c", "/home/<USER>/esp32/esp-idf/components/protocomm/src/transports/protocomm_httpd.c", "/home/<USER>/esp32/esp-idf/components/protocomm/src/security/security0.c", "/home/<USER>/esp32/esp-idf/components/protocomm/src/security/security1.c", "/home/<USER>/esp32/esp-idf/components/protocomm/src/security/security2.c", "/home/<USER>/esp32/esp-idf/components/protocomm/src/crypto/srp6a/esp_srp.c", "/home/<USER>/esp32/esp-idf/components/protocomm/src/crypto/srp6a/esp_srp_mpi.c"], "include_dirs": ["include/common", "include/security", "include/transports", "include/crypto/srp6a", "proto-c"]}, "pthread": {"alias": "idf::pthread", "target": "___idf_pthread", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/pthread", "type": "LIBRARY", "lib": "__idf_pthread", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/pthread/libpthread.a", "sources": ["/home/<USER>/esp32/esp-idf/components/pthread/pthread.c", "/home/<USER>/esp32/esp-idf/components/pthread/pthread_cond_var.c", "/home/<USER>/esp32/esp-idf/components/pthread/pthread_local_storage.c", "/home/<USER>/esp32/esp-idf/components/pthread/pthread_rwlock.c", "/home/<USER>/esp32/esp-idf/components/pthread/pthread_semaphore.c"], "include_dirs": ["include"]}, "rt": {"alias": "idf::rt", "target": "___idf_rt", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/rt", "type": "LIBRARY", "lib": "__idf_rt", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/rt/librt.a", "sources": ["/home/<USER>/esp32/esp-idf/components/rt/FreeRTOS_POSIX_mqueue.c", "/home/<USER>/esp32/esp-idf/components/rt/FreeRTOS_POSIX_utils.c"], "include_dirs": ["include"]}, "sdmmc": {"alias": "idf::sdmmc", "target": "___idf_sdmmc", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/sdmmc", "type": "LIBRARY", "lib": "__idf_sdmmc", "reqs": [], "priv_reqs": ["soc", "esp_timer", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/sdmmc/libsdmmc.a", "sources": ["/home/<USER>/esp32/esp-idf/components/sdmmc/sdmmc_cmd.c", "/home/<USER>/esp32/esp-idf/components/sdmmc/sdmmc_common.c", "/home/<USER>/esp32/esp-idf/components/sdmmc/sdmmc_init.c", "/home/<USER>/esp32/esp-idf/components/sdmmc/sdmmc_io.c", "/home/<USER>/esp32/esp-idf/components/sdmmc/sdmmc_mmc.c", "/home/<USER>/esp32/esp-idf/components/sdmmc/sdmmc_sd.c", "/home/<USER>/esp32/esp-idf/components/sdmmc/sd_pwr_ctrl/sd_pwr_ctrl.c"], "include_dirs": ["include"]}, "soc": {"alias": "idf::soc", "target": "___idf_soc", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/soc", "type": "LIBRARY", "lib": "__idf_soc", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/soc/libsoc.a", "sources": ["/home/<USER>/esp32/esp-idf/components/soc/lldesc.c", "/home/<USER>/esp32/esp-idf/components/soc/dport_access_common.c", "/home/<USER>/esp32/esp-idf/components/soc/esp32s3/interrupts.c", "/home/<USER>/esp32/esp-idf/components/soc/esp32s3/gpio_periph.c", "/home/<USER>/esp32/esp-idf/components/soc/esp32s3/uart_periph.c", "/home/<USER>/esp32/esp-idf/components/soc/esp32s3/adc_periph.c", "/home/<USER>/esp32/esp-idf/components/soc/esp32s3/dedic_gpio_periph.c", "/home/<USER>/esp32/esp-idf/components/soc/esp32s3/gdma_periph.c", "/home/<USER>/esp32/esp-idf/components/soc/esp32s3/spi_periph.c", "/home/<USER>/esp32/esp-idf/components/soc/esp32s3/ledc_periph.c", "/home/<USER>/esp32/esp-idf/components/soc/esp32s3/pcnt_periph.c", "/home/<USER>/esp32/esp-idf/components/soc/esp32s3/rmt_periph.c", "/home/<USER>/esp32/esp-idf/components/soc/esp32s3/sdm_periph.c", "/home/<USER>/esp32/esp-idf/components/soc/esp32s3/i2s_periph.c", "/home/<USER>/esp32/esp-idf/components/soc/esp32s3/i2c_periph.c", "/home/<USER>/esp32/esp-idf/components/soc/esp32s3/temperature_sensor_periph.c", "/home/<USER>/esp32/esp-idf/components/soc/esp32s3/timer_periph.c", "/home/<USER>/esp32/esp-idf/components/soc/esp32s3/lcd_periph.c", "/home/<USER>/esp32/esp-idf/components/soc/esp32s3/mcpwm_periph.c", "/home/<USER>/esp32/esp-idf/components/soc/esp32s3/mpi_periph.c", "/home/<USER>/esp32/esp-idf/components/soc/esp32s3/sdmmc_periph.c", "/home/<USER>/esp32/esp-idf/components/soc/esp32s3/touch_sensor_periph.c", "/home/<USER>/esp32/esp-idf/components/soc/esp32s3/twai_periph.c", "/home/<USER>/esp32/esp-idf/components/soc/esp32s3/wdt_periph.c", "/home/<USER>/esp32/esp-idf/components/soc/esp32s3/usb_dwc_periph.c", "/home/<USER>/esp32/esp-idf/components/soc/esp32s3/rtc_io_periph.c"], "include_dirs": ["include", "esp32s3", "esp32s3/include", "esp32s3/register"]}, "spi_flash": {"alias": "idf::spi_flash", "target": "___idf_spi_flash", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/spi_flash", "type": "LIBRARY", "lib": "__idf_spi_flash", "reqs": ["hal"], "priv_reqs": ["bootloader_support", "app_update", "soc", "esp_mm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/spi_flash/libspi_flash.a", "sources": ["/home/<USER>/esp32/esp-idf/components/spi_flash/flash_brownout_hook.c", "/home/<USER>/esp32/esp-idf/components/spi_flash/esp32s3/spi_flash_oct_flash_init.c", "/home/<USER>/esp32/esp-idf/components/spi_flash/spi_flash_hpm_enable.c", "/home/<USER>/esp32/esp-idf/components/spi_flash/spi_flash_chip_drivers.c", "/home/<USER>/esp32/esp-idf/components/spi_flash/spi_flash_chip_generic.c", "/home/<USER>/esp32/esp-idf/components/spi_flash/spi_flash_chip_issi.c", "/home/<USER>/esp32/esp-idf/components/spi_flash/spi_flash_chip_mxic.c", "/home/<USER>/esp32/esp-idf/components/spi_flash/spi_flash_chip_gd.c", "/home/<USER>/esp32/esp-idf/components/spi_flash/spi_flash_chip_winbond.c", "/home/<USER>/esp32/esp-idf/components/spi_flash/spi_flash_chip_boya.c", "/home/<USER>/esp32/esp-idf/components/spi_flash/spi_flash_chip_mxic_opi.c", "/home/<USER>/esp32/esp-idf/components/spi_flash/spi_flash_chip_th.c", "/home/<USER>/esp32/esp-idf/components/spi_flash/memspi_host_driver.c", "/home/<USER>/esp32/esp-idf/components/spi_flash/cache_utils.c", "/home/<USER>/esp32/esp-idf/components/spi_flash/flash_mmap.c", "/home/<USER>/esp32/esp-idf/components/spi_flash/flash_ops.c", "/home/<USER>/esp32/esp-idf/components/spi_flash/spi_flash_wrap.c", "/home/<USER>/esp32/esp-idf/components/spi_flash/esp_flash_api.c", "/home/<USER>/esp32/esp-idf/components/spi_flash/esp_flash_spi_init.c", "/home/<USER>/esp32/esp-idf/components/spi_flash/spi_flash_os_func_app.c", "/home/<USER>/esp32/esp-idf/components/spi_flash/spi_flash_os_func_noos.c"], "include_dirs": ["include"]}, "spiffs": {"alias": "idf::spiffs", "target": "___idf_spiffs", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/spiffs", "type": "LIBRARY", "lib": "__idf_spiffs", "reqs": ["esp_partition"], "priv_reqs": ["bootloader_support", "esptool_py", "vfs"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/spiffs/libspiffs.a", "sources": ["/home/<USER>/esp32/esp-idf/components/spiffs/spiffs_api.c", "/home/<USER>/esp32/esp-idf/components/spiffs/spiffs/src/spiffs_cache.c", "/home/<USER>/esp32/esp-idf/components/spiffs/spiffs/src/spiffs_check.c", "/home/<USER>/esp32/esp-idf/components/spiffs/spiffs/src/spiffs_gc.c", "/home/<USER>/esp32/esp-idf/components/spiffs/spiffs/src/spiffs_hydrogen.c", "/home/<USER>/esp32/esp-idf/components/spiffs/spiffs/src/spiffs_nucleus.c", "/home/<USER>/esp32/esp-idf/components/spiffs/esp_spiffs.c"], "include_dirs": ["include"]}, "tcp_transport": {"alias": "idf::tcp_transport", "target": "___idf_tcp_transport", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/tcp_transport", "type": "LIBRARY", "lib": "__idf_tcp_transport", "reqs": ["esp-tls", "lwip", "esp_timer"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/tcp_transport/libtcp_transport.a", "sources": ["/home/<USER>/esp32/esp-idf/components/tcp_transport/transport.c", "/home/<USER>/esp32/esp-idf/components/tcp_transport/transport_ssl.c", "/home/<USER>/esp32/esp-idf/components/tcp_transport/transport_internal.c", "/home/<USER>/esp32/esp-idf/components/tcp_transport/transport_socks_proxy.c", "/home/<USER>/esp32/esp-idf/components/tcp_transport/transport_ws.c"], "include_dirs": ["include"]}, "tny-robotics__sh1106-esp-idf": {"alias": "idf::tny-robotics__sh1106-esp-idf", "target": "___idf_tny-robotics__sh1106-esp-idf", "prefix": "idf", "dir": "/home/<USER>/bysx/xiaozhi-esp32/managed_components/tny-robotics__sh1106-esp-idf", "type": "LIBRARY", "lib": "__idf_tny-robotics__sh1106-esp-idf", "reqs": ["driver", "esp_lcd"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/tny-robotics__sh1106-esp-idf/libtny-robotics__sh1106-esp-idf.a", "sources": ["/home/<USER>/bysx/xiaozhi-esp32/managed_components/tny-robotics__sh1106-esp-idf/esp_lcd_panel_sh1106.c"], "include_dirs": ["include"]}, "touch_element": {"alias": "idf::touch_element", "target": "___idf_touch_element", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/touch_element", "type": "LIBRARY", "lib": "__idf_touch_element", "reqs": ["driver"], "priv_reqs": ["esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/touch_element/libtouch_element.a", "sources": ["/home/<USER>/esp32/esp-idf/components/touch_element/touch_element.c", "/home/<USER>/esp32/esp-idf/components/touch_element/touch_button.c", "/home/<USER>/esp32/esp-idf/components/touch_element/touch_slider.c", "/home/<USER>/esp32/esp-idf/components/touch_element/touch_matrix.c"], "include_dirs": ["include"]}, "txp666__otto-emoji-gif-component": {"alias": "idf::txp666__otto-emoji-gif-component", "target": "___idf_txp666__otto-emoji-gif-component", "prefix": "idf", "dir": "/home/<USER>/bysx/xiaozhi-esp32/managed_components/txp666__otto-emoji-gif-component", "type": "LIBRARY", "lib": "__idf_txp666__otto-emoji-gif-component", "reqs": ["lvgl__lvgl"], "priv_reqs": ["lvgl__lvgl"], "managed_reqs": [], "managed_priv_reqs": ["lvgl__lvgl"], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/txp666__otto-emoji-gif-component/libtxp666__otto-emoji-gif-component.a", "sources": ["/home/<USER>/bysx/xiaozhi-esp32/managed_components/txp666__otto-emoji-gif-component/src/anger.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/txp666__otto-emoji-gif-component/src/buxue.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/txp666__otto-emoji-gif-component/src/happy.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/txp666__otto-emoji-gif-component/src/otto_emoji_gif_utils.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/txp666__otto-emoji-gif-component/src/sad.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/txp666__otto-emoji-gif-component/src/scare.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/txp666__otto-emoji-gif-component/src/staticstate.c"], "include_dirs": ["include"]}, "ulp": {"alias": "idf::ulp", "target": "___idf_ulp", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/ulp", "type": "CONFIG_ONLY", "lib": "__idf_ulp", "reqs": ["driver", "esp_adc"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": []}, "unity": {"alias": "idf::unity", "target": "___idf_unity", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/unity", "type": "LIBRARY", "lib": "__idf_unity", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/unity/libunity.a", "sources": ["/home/<USER>/esp32/esp-idf/components/unity/unity/src/unity.c", "/home/<USER>/esp32/esp-idf/components/unity/unity_compat.c", "/home/<USER>/esp32/esp-idf/components/unity/unity_runner.c", "/home/<USER>/esp32/esp-idf/components/unity/unity_utils_freertos.c", "/home/<USER>/esp32/esp-idf/components/unity/unity_utils_cache.c", "/home/<USER>/esp32/esp-idf/components/unity/unity_utils_memory.c", "/home/<USER>/esp32/esp-idf/components/unity/unity_port_esp32.c", "/home/<USER>/esp32/esp-idf/components/unity/port/esp/unity_utils_memory_esp.c"], "include_dirs": ["include", "unity/src"]}, "usb": {"alias": "idf::usb", "target": "___idf_usb", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/usb", "type": "LIBRARY", "lib": "__idf_usb", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/usb/libusb.a", "sources": ["/home/<USER>/esp32/esp-idf/components/usb/hcd_dwc.c", "/home/<USER>/esp32/esp-idf/components/usb/enum.c", "/home/<USER>/esp32/esp-idf/components/usb/hub.c", "/home/<USER>/esp32/esp-idf/components/usb/usb_helpers.c", "/home/<USER>/esp32/esp-idf/components/usb/usb_host.c", "/home/<USER>/esp32/esp-idf/components/usb/usb_private.c", "/home/<USER>/esp32/esp-idf/components/usb/usbh.c", "/home/<USER>/esp32/esp-idf/components/usb/usb_phy.c"], "include_dirs": ["include"]}, "vfs": {"alias": "idf::vfs", "target": "___idf_vfs", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/vfs", "type": "LIBRARY", "lib": "__idf_vfs", "reqs": [], "priv_reqs": ["esp_timer", "esp_driver_uart", "esp_driver_usb_serial_jtag", "esp_vfs_console"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/vfs/libvfs.a", "sources": ["/home/<USER>/esp32/esp-idf/components/vfs/vfs.c", "/home/<USER>/esp32/esp-idf/components/vfs/vfs_eventfd.c", "/home/<USER>/esp32/esp-idf/components/vfs/vfs_semihost.c", "/home/<USER>/esp32/esp-idf/components/vfs/nullfs.c"], "include_dirs": ["include"]}, "waveshare__esp_lcd_sh8601": {"alias": "idf::waveshare__esp_lcd_sh8601", "target": "___idf_waveshare__esp_lcd_sh8601", "prefix": "idf", "dir": "/home/<USER>/bysx/xiaozhi-esp32/managed_components/waveshare__esp_lcd_sh8601", "type": "LIBRARY", "lib": "__idf_waveshare__esp_lcd_sh8601", "reqs": ["esp_lcd"], "priv_reqs": ["driver", "espressif__cmake_utilities"], "managed_reqs": [], "managed_priv_reqs": ["espressif__cmake_utilities"], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/waveshare__esp_lcd_sh8601/libwaveshare__esp_lcd_sh8601.a", "sources": ["/home/<USER>/bysx/xiaozhi-esp32/managed_components/waveshare__esp_lcd_sh8601/esp_lcd_sh8601.c"], "include_dirs": ["include"]}, "waveshare__esp_lcd_touch_cst9217": {"alias": "idf::waveshare__esp_lcd_touch_cst9217", "target": "___idf_waveshare__esp_lcd_touch_cst9217", "prefix": "idf", "dir": "/home/<USER>/bysx/xiaozhi-esp32/managed_components/waveshare__esp_lcd_touch_cst9217", "type": "LIBRARY", "lib": "__idf_waveshare__esp_lcd_touch_cst9217", "reqs": ["esp_lcd", "espressif__esp_lcd_touch"], "priv_reqs": [], "managed_reqs": ["espressif__esp_lcd_touch"], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/waveshare__esp_lcd_touch_cst9217/libwaveshare__esp_lcd_touch_cst9217.a", "sources": ["/home/<USER>/bysx/xiaozhi-esp32/managed_components/waveshare__esp_lcd_touch_cst9217/esp_lcd_touch_cst9217.c"], "include_dirs": ["include"]}, "wear_levelling": {"alias": "idf::wear_levelling", "target": "___idf_wear_levelling", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/wear_levelling", "type": "LIBRARY", "lib": "__idf_wear_levelling", "reqs": ["esp_partition"], "priv_reqs": ["spi_flash"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/wear_levelling/libwear_levelling.a", "sources": ["/home/<USER>/esp32/esp-idf/components/wear_levelling/Partition.cpp", "/home/<USER>/esp32/esp-idf/components/wear_levelling/SPI_Flash.cpp", "/home/<USER>/esp32/esp-idf/components/wear_levelling/WL_Ext_Perf.cpp", "/home/<USER>/esp32/esp-idf/components/wear_levelling/WL_Ext_Safe.cpp", "/home/<USER>/esp32/esp-idf/components/wear_levelling/WL_Flash.cpp", "/home/<USER>/esp32/esp-idf/components/wear_levelling/crc32.cpp", "/home/<USER>/esp32/esp-idf/components/wear_levelling/wear_levelling.cpp"], "include_dirs": ["include"]}, "wifi_provisioning": {"alias": "idf::wifi_provisioning", "target": "___idf_wifi_provisioning", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/wifi_provisioning", "type": "LIBRARY", "lib": "__idf_wifi_provisioning", "reqs": ["lwip", "protocomm"], "priv_reqs": ["protobuf-c", "bt", "json", "esp_timer", "esp_wifi"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/wifi_provisioning/libwifi_provisioning.a", "sources": ["/home/<USER>/esp32/esp-idf/components/wifi_provisioning/src/wifi_config.c", "/home/<USER>/esp32/esp-idf/components/wifi_provisioning/src/wifi_scan.c", "/home/<USER>/esp32/esp-idf/components/wifi_provisioning/src/wifi_ctrl.c", "/home/<USER>/esp32/esp-idf/components/wifi_provisioning/src/manager.c", "/home/<USER>/esp32/esp-idf/components/wifi_provisioning/src/handlers.c", "/home/<USER>/esp32/esp-idf/components/wifi_provisioning/src/scheme_console.c", "/home/<USER>/esp32/esp-idf/components/wifi_provisioning/proto-c/wifi_config.pb-c.c", "/home/<USER>/esp32/esp-idf/components/wifi_provisioning/proto-c/wifi_scan.pb-c.c", "/home/<USER>/esp32/esp-idf/components/wifi_provisioning/proto-c/wifi_ctrl.pb-c.c", "/home/<USER>/esp32/esp-idf/components/wifi_provisioning/proto-c/wifi_constants.pb-c.c", "/home/<USER>/esp32/esp-idf/components/wifi_provisioning/src/scheme_softap.c"], "include_dirs": ["include"]}, "wpa_supplicant": {"alias": "idf::wpa_supplicant", "target": "___idf_wpa_supplicant", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/wpa_supplicant", "type": "LIBRARY", "lib": "__idf_wpa_supplicant", "reqs": [], "priv_reqs": ["mbedtls", "esp_timer", "esp_wifi"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/wpa_supplicant/libwpa_supplicant.a", "sources": ["/home/<USER>/esp32/esp-idf/components/wpa_supplicant/port/os_xtensa.c", "/home/<USER>/esp32/esp-idf/components/wpa_supplicant/port/eloop.c", "/home/<USER>/esp32/esp-idf/components/wpa_supplicant/src/ap/ap_config.c", "/home/<USER>/esp32/esp-idf/components/wpa_supplicant/src/ap/ieee802_1x.c", "/home/<USER>/esp32/esp-idf/components/wpa_supplicant/src/ap/wpa_auth.c", "/home/<USER>/esp32/esp-idf/components/wpa_supplicant/src/ap/wpa_auth_ie.c", "/home/<USER>/esp32/esp-idf/components/wpa_supplicant/src/ap/pmksa_cache_auth.c", "/home/<USER>/esp32/esp-idf/components/wpa_supplicant/src/ap/sta_info.c", "/home/<USER>/esp32/esp-idf/components/wpa_supplicant/src/ap/ieee802_11.c", "/home/<USER>/esp32/esp-idf/components/wpa_supplicant/src/ap/comeback_token.c", "/home/<USER>/esp32/esp-idf/components/wpa_supplicant/src/common/sae.c", "/home/<USER>/esp32/esp-idf/components/wpa_supplicant/src/common/dragonfly.c", "/home/<USER>/esp32/esp-idf/components/wpa_supplicant/src/common/wpa_common.c", "/home/<USER>/esp32/esp-idf/components/wpa_supplicant/src/utils/bitfield.c", "/home/<USER>/esp32/esp-idf/components/wpa_supplicant/src/crypto/aes-siv.c", "/home/<USER>/esp32/esp-idf/components/wpa_supplicant/src/crypto/sha256-kdf.c", "/home/<USER>/esp32/esp-idf/components/wpa_supplicant/src/crypto/ccmp.c", "/home/<USER>/esp32/esp-idf/components/wpa_supplicant/src/crypto/aes-gcm.c", "/home/<USER>/esp32/esp-idf/components/wpa_supplicant/src/crypto/crypto_ops.c", "/home/<USER>/esp32/esp-idf/components/wpa_supplicant/src/crypto/dh_group5.c", "/home/<USER>/esp32/esp-idf/components/wpa_supplicant/src/crypto/dh_groups.c", "/home/<USER>/esp32/esp-idf/components/wpa_supplicant/src/crypto/ms_funcs.c", "/home/<USER>/esp32/esp-idf/components/wpa_supplicant/src/crypto/sha1-tlsprf.c", "/home/<USER>/esp32/esp-idf/components/wpa_supplicant/src/crypto/sha256-tlsprf.c", "/home/<USER>/esp32/esp-idf/components/wpa_supplicant/src/crypto/sha384-tlsprf.c", "/home/<USER>/esp32/esp-idf/components/wpa_supplicant/src/crypto/sha256-prf.c", "/home/<USER>/esp32/esp-idf/components/wpa_supplicant/src/crypto/sha1-prf.c", "/home/<USER>/esp32/esp-idf/components/wpa_supplicant/src/crypto/sha384-prf.c", "/home/<USER>/esp32/esp-idf/components/wpa_supplicant/src/crypto/md4-internal.c", "/home/<USER>/esp32/esp-idf/components/wpa_supplicant/src/crypto/sha1-tprf.c", "/home/<USER>/esp32/esp-idf/components/wpa_supplicant/src/eap_common/eap_wsc_common.c", "/home/<USER>/esp32/esp-idf/components/wpa_supplicant/src/common/ieee802_11_common.c", "/home/<USER>/esp32/esp-idf/components/wpa_supplicant/src/eap_peer/chap.c", "/home/<USER>/esp32/esp-idf/components/wpa_supplicant/src/eap_peer/eap.c", "/home/<USER>/esp32/esp-idf/components/wpa_supplicant/src/eap_peer/eap_common.c", "/home/<USER>/esp32/esp-idf/components/wpa_supplicant/src/eap_peer/eap_mschapv2.c", "/home/<USER>/esp32/esp-idf/components/wpa_supplicant/src/eap_peer/eap_peap.c", "/home/<USER>/esp32/esp-idf/components/wpa_supplicant/src/eap_peer/eap_peap_common.c", "/home/<USER>/esp32/esp-idf/components/wpa_supplicant/src/eap_peer/eap_tls.c", "/home/<USER>/esp32/esp-idf/components/wpa_supplicant/src/eap_peer/eap_tls_common.c", "/home/<USER>/esp32/esp-idf/components/wpa_supplicant/src/eap_peer/eap_ttls.c", "/home/<USER>/esp32/esp-idf/components/wpa_supplicant/src/eap_peer/mschapv2.c", "/home/<USER>/esp32/esp-idf/components/wpa_supplicant/src/eap_peer/eap_fast.c", "/home/<USER>/esp32/esp-idf/components/wpa_supplicant/src/eap_peer/eap_fast_common.c", "/home/<USER>/esp32/esp-idf/components/wpa_supplicant/src/eap_peer/eap_fast_pac.c", "/home/<USER>/esp32/esp-idf/components/wpa_supplicant/src/rsn_supp/pmksa_cache.c", "/home/<USER>/esp32/esp-idf/components/wpa_supplicant/src/rsn_supp/wpa.c", "/home/<USER>/esp32/esp-idf/components/wpa_supplicant/src/rsn_supp/wpa_ie.c", "/home/<USER>/esp32/esp-idf/components/wpa_supplicant/src/utils/base64.c", "/home/<USER>/esp32/esp-idf/components/wpa_supplicant/src/utils/common.c", "/home/<USER>/esp32/esp-idf/components/wpa_supplicant/src/utils/ext_password.c", "/home/<USER>/esp32/esp-idf/components/wpa_supplicant/src/utils/uuid.c", "/home/<USER>/esp32/esp-idf/components/wpa_supplicant/src/utils/wpabuf.c", "/home/<USER>/esp32/esp-idf/components/wpa_supplicant/src/utils/wpa_debug.c", "/home/<USER>/esp32/esp-idf/components/wpa_supplicant/src/utils/json.c", "/home/<USER>/esp32/esp-idf/components/wpa_supplicant/src/wps/wps.c", "/home/<USER>/esp32/esp-idf/components/wpa_supplicant/src/wps/wps_attr_build.c", "/home/<USER>/esp32/esp-idf/components/wpa_supplicant/src/wps/wps_attr_parse.c", "/home/<USER>/esp32/esp-idf/components/wpa_supplicant/src/wps/wps_attr_process.c", "/home/<USER>/esp32/esp-idf/components/wpa_supplicant/src/wps/wps_common.c", "/home/<USER>/esp32/esp-idf/components/wpa_supplicant/src/wps/wps_dev_attr.c", "/home/<USER>/esp32/esp-idf/components/wpa_supplicant/src/wps/wps_enrollee.c", "/home/<USER>/esp32/esp-idf/components/wpa_supplicant/src/common/sae_pk.c", "/home/<USER>/esp32/esp-idf/components/wpa_supplicant/esp_supplicant/src/esp_eap_client.c", "/home/<USER>/esp32/esp-idf/components/wpa_supplicant/esp_supplicant/src/esp_wpa2_api_port.c", "/home/<USER>/esp32/esp-idf/components/wpa_supplicant/esp_supplicant/src/esp_wpa_main.c", "/home/<USER>/esp32/esp-idf/components/wpa_supplicant/esp_supplicant/src/esp_wpas_glue.c", "/home/<USER>/esp32/esp-idf/components/wpa_supplicant/esp_supplicant/src/esp_common.c", "/home/<USER>/esp32/esp-idf/components/wpa_supplicant/esp_supplicant/src/esp_wps.c", "/home/<USER>/esp32/esp-idf/components/wpa_supplicant/esp_supplicant/src/esp_wpa3.c", "/home/<USER>/esp32/esp-idf/components/wpa_supplicant/esp_supplicant/src/esp_owe.c", "/home/<USER>/esp32/esp-idf/components/wpa_supplicant/esp_supplicant/src/esp_hostap.c", "/home/<USER>/esp32/esp-idf/components/wpa_supplicant/src/tls/asn1.c", "/home/<USER>/esp32/esp-idf/components/wpa_supplicant/src/tls/bignum.c", "/home/<USER>/esp32/esp-idf/components/wpa_supplicant/src/tls/pkcs1.c", "/home/<USER>/esp32/esp-idf/components/wpa_supplicant/src/tls/pkcs5.c", "/home/<USER>/esp32/esp-idf/components/wpa_supplicant/src/tls/pkcs8.c", "/home/<USER>/esp32/esp-idf/components/wpa_supplicant/src/tls/rsa.c", "/home/<USER>/esp32/esp-idf/components/wpa_supplicant/src/crypto/tls_internal.c", "/home/<USER>/esp32/esp-idf/components/wpa_supplicant/src/tls/tlsv1_client.c", "/home/<USER>/esp32/esp-idf/components/wpa_supplicant/src/tls/tlsv1_client_read.c", "/home/<USER>/esp32/esp-idf/components/wpa_supplicant/src/tls/tlsv1_client_write.c", "/home/<USER>/esp32/esp-idf/components/wpa_supplicant/src/tls/tlsv1_common.c", "/home/<USER>/esp32/esp-idf/components/wpa_supplicant/src/tls/tlsv1_cred.c", "/home/<USER>/esp32/esp-idf/components/wpa_supplicant/src/tls/tlsv1_record.c", "/home/<USER>/esp32/esp-idf/components/wpa_supplicant/src/tls/tlsv1_client_ocsp.c", "/home/<USER>/esp32/esp-idf/components/wpa_supplicant/src/tls/x509v3.c", "/home/<USER>/esp32/esp-idf/components/wpa_supplicant/esp_supplicant/src/crypto/fastpbkdf2.c", "/home/<USER>/esp32/esp-idf/components/wpa_supplicant/esp_supplicant/src/crypto/crypto_mbedtls.c", "/home/<USER>/esp32/esp-idf/components/wpa_supplicant/esp_supplicant/src/crypto/crypto_mbedtls-bignum.c", "/home/<USER>/esp32/esp-idf/components/wpa_supplicant/esp_supplicant/src/crypto/crypto_mbedtls-rsa.c", "/home/<USER>/esp32/esp-idf/components/wpa_supplicant/esp_supplicant/src/crypto/crypto_mbedtls-ec.c", "/home/<USER>/esp32/esp-idf/components/wpa_supplicant/src/crypto/rc4.c", "/home/<USER>/esp32/esp-idf/components/wpa_supplicant/src/crypto/des-internal.c", "/home/<USER>/esp32/esp-idf/components/wpa_supplicant/src/crypto/aes-wrap.c", "/home/<USER>/esp32/esp-idf/components/wpa_supplicant/src/crypto/aes-unwrap.c", "/home/<USER>/esp32/esp-idf/components/wpa_supplicant/src/crypto/aes-ccm.c"], "include_dirs": ["include", "port/include", "esp_supplicant/include"]}, "wvirgil123__esp_jpeg_simd": {"alias": "idf::wvirgil123__esp_jpeg_simd", "target": "___idf_wvirgil123__esp_jpeg_simd", "prefix": "idf", "dir": "/home/<USER>/bysx/xiaozhi-esp32/managed_components/wvirgil123__esp_jpeg_simd", "type": "LIBRARY", "lib": "__idf_wvirgil123__esp_jpeg_simd", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/wvirgil123__esp_jpeg_simd/libwvirgil123__esp_jpeg_simd.a", "sources": ["/home/<USER>/bysx/xiaozhi-esp32/managed_components/wvirgil123__esp_jpeg_simd/src/audio_malloc.c"], "include_dirs": ["include"]}, "wvirgil123__sscma_client": {"alias": "idf::wvirgil123__sscma_client", "target": "___idf_wvirgil123__sscma_client", "prefix": "idf", "dir": "/home/<USER>/bysx/xiaozhi-esp32/managed_components/wvirgil123__sscma_client", "type": "LIBRARY", "lib": "__idf_wvirgil123__sscma_client", "reqs": ["json", "mbedtls", "esp_timer"], "priv_reqs": ["driver", "espressif__esp_io_expander"], "managed_reqs": [], "managed_priv_reqs": ["espressif__esp_io_expander"], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/wvirgil123__sscma_client/libwvirgil123__sscma_client.a", "sources": ["/home/<USER>/bysx/xiaozhi-esp32/managed_components/wvirgil123__sscma_client/src/sscma_client_ops.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/wvirgil123__sscma_client/src/sscma_client_io.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/wvirgil123__sscma_client/src/sscma_client_io_i2c.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/wvirgil123__sscma_client/src/sscma_client_io_spi.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/wvirgil123__sscma_client/src/sscma_client_io_uart.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/wvirgil123__sscma_client/src/sscma_client_flasher.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/wvirgil123__sscma_client/src/sscma_client_flasher_we2_uart.c", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/wvirgil123__sscma_client/src/sscma_client_flasher_we2_spi.c"], "include_dirs": ["include", "interface"]}, "xtensa": {"alias": "idf::xtensa", "target": "___idf_xtensa", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/xtensa", "type": "LIBRARY", "lib": "__idf_xtensa", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/bysx/xiaozhi-esp32/build/esp-idf/xtensa/libxtensa.a", "sources": ["/home/<USER>/esp32/esp-idf/components/xtensa/eri.c", "/home/<USER>/esp32/esp-idf/components/xtensa/xt_trax.c", "/home/<USER>/esp32/esp-idf/components/xtensa/xtensa_context.S", "/home/<USER>/esp32/esp-idf/components/xtensa/xtensa_intr_asm.S", "/home/<USER>/esp32/esp-idf/components/xtensa/xtensa_intr.c", "/home/<USER>/esp32/esp-idf/components/xtensa/xtensa_vectors.S"], "include_dirs": ["esp32s3/include", "include", "deprecated_include"]}}, "all_component_info": {"app_trace": {"alias": "idf::app_trace", "target": "___idf_app_trace", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/app_trace", "lib": "__idf_app_trace", "reqs": ["esp_timer"], "priv_reqs": ["esp_driver_gptimer", "esp_driver_gpio", "esp_driver_uart"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "app_update": {"alias": "idf::app_update", "target": "___idf_app_update", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/app_update", "lib": "__idf_app_update", "reqs": ["partition_table", "bootloader_support", "esp_app_format", "esp_bootloader_format", "esp_partition"], "priv_reqs": ["esptool_py", "efuse", "spi_flash"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "bootloader": {"alias": "idf::bootloader", "target": "___idf_bootloader", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/bootloader", "lib": "__idf_bootloader", "reqs": [], "priv_reqs": ["partition_table", "esptool_py"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "bootloader_support": {"alias": "idf::bootloader_support", "target": "___idf_bootloader_support", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/bootloader_support", "lib": "__idf_bootloader_support", "reqs": ["soc"], "priv_reqs": ["spi_flash", "mbedtls", "efuse", "heap", "esp_bootloader_format", "esp_app_format"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "bootloader_flash/include"]}, "bt": {"alias": "idf::bt", "target": "___idf_bt", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/bt", "lib": "__idf_bt", "reqs": ["esp_timer", "esp_wifi"], "priv_reqs": ["nvs_flash", "soc", "esp_pm", "esp_phy", "esp_coex", "mbedtls", "esp_driver_uart", "vfs", "esp_ringbuf", "esp_driver_spi", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "cmock": {"alias": "idf::cmock", "target": "___idf_cmock", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/cmock", "lib": "__idf_cmock", "reqs": ["unity"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["CMock/src"]}, "console": {"alias": "idf::console", "target": "___idf_console", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/console", "lib": "__idf_console", "reqs": ["vfs", "esp_vfs_console"], "priv_reqs": ["esp_driver_uart", "esp_driver_usb_serial_jtag"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["/home/<USER>/esp32/esp-idf/components/console"]}, "cxx": {"alias": "idf::cxx", "target": "___idf_cxx", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/cxx", "lib": "__idf_cxx", "reqs": [], "priv_reqs": ["pthread", "esp_system"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "driver": {"alias": "idf::driver", "target": "___idf_driver", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/driver", "lib": "__idf_driver", "reqs": ["esp_pm", "esp_ringbuf", "freertos", "soc", "hal", "esp_hw_support", "esp_driver_gpio", "esp_driver_pcnt", "esp_driver_gptimer", "esp_driver_spi", "esp_driver_mcpwm", "esp_driver_ana_cmpr", "esp_driver_i2s", "esp_driver_sdmmc", "esp_driver_sdspi", "esp_driver_sdio", "esp_driver_dac", "esp_driver_rmt", "esp_driver_tsens", "esp_driver_sdm", "esp_driver_i2c", "esp_driver_uart", "esp_driver_ledc", "esp_driver_parlio", "esp_driver_usb_serial_jtag"], "priv_reqs": ["efuse", "esp_timer", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["deprecated", "i2c/include", "touch_sensor/include", "twai/include", "touch_sensor/esp32s3/include"]}, "efuse": {"alias": "idf::efuse", "target": "___idf_efuse", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/efuse", "lib": "__idf_efuse", "reqs": [], "priv_reqs": ["bootloader_support", "soc", "spi_flash", "esp_system", "esp_partition", "esp_app_format"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "esp32s3/include"]}, "esp-tls": {"alias": "idf::esp-tls", "target": "___idf_esp-tls", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp-tls", "lib": "__idf_esp-tls", "reqs": ["mbedtls"], "priv_reqs": ["http_parser", "esp_timer", "lwip"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["/home/<USER>/esp32/esp-idf/components/esp-tls", "esp-tls-crypto"]}, "esp_adc": {"alias": "idf::esp_adc", "target": "___idf_esp_adc", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_adc", "lib": "__idf_esp_adc", "reqs": [], "priv_reqs": ["driver", "esp_driver_gpio", "efuse", "esp_pm", "esp_ringbuf", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "interface", "esp32s3/include", "deprecated/include"]}, "esp_app_format": {"alias": "idf::esp_app_format", "target": "___idf_esp_app_format", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_app_format", "lib": "__idf_esp_app_format", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_bootloader_format": {"alias": "idf::esp_bootloader_format", "target": "___idf_esp_bootloader_format", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_bootloader_format", "lib": "__idf_esp_bootloader_format", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_coex": {"alias": "idf::esp_coex", "target": "___idf_esp_coex", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_coex", "lib": "__idf_esp_coex", "reqs": [], "priv_reqs": ["esp_timer", "driver", "esp_event"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_common": {"alias": "idf::esp_common", "target": "___idf_esp_common", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_common", "lib": "__idf_esp_common", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_ana_cmpr": {"alias": "idf::esp_driver_ana_cmpr", "target": "___idf_esp_driver_ana_cmpr", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_driver_ana_cmpr", "lib": "__idf_esp_driver_ana_cmpr", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_cam": {"alias": "idf::esp_driver_cam", "target": "___idf_esp_driver_cam", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_driver_cam", "lib": "__idf_esp_driver_cam", "reqs": ["esp_driver_isp", "esp_mm"], "priv_reqs": ["esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "interface"]}, "esp_driver_dac": {"alias": "idf::esp_driver_dac", "target": "___idf_esp_driver_dac", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_driver_dac", "lib": "__idf_esp_driver_dac", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["./include"]}, "esp_driver_gpio": {"alias": "idf::esp_driver_gpio", "target": "___idf_esp_driver_gpio", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_driver_gpio", "lib": "__idf_esp_driver_gpio", "reqs": [], "priv_reqs": ["esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_gptimer": {"alias": "idf::esp_driver_gptimer", "target": "___idf_esp_driver_gptimer", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_driver_gptimer", "lib": "__idf_esp_driver_gptimer", "reqs": ["esp_pm"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_i2c": {"alias": "idf::esp_driver_i2c", "target": "___idf_esp_driver_i2c", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_driver_i2c", "lib": "__idf_esp_driver_i2c", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_pm", "esp_ringbuf"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_i2s": {"alias": "idf::esp_driver_i2s", "target": "___idf_esp_driver_i2s", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_driver_i2s", "lib": "__idf_esp_driver_i2s", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_pm", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_isp": {"alias": "idf::esp_driver_isp", "target": "___idf_esp_driver_isp", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_driver_isp", "lib": "__idf_esp_driver_isp", "reqs": ["esp_mm"], "priv_reqs": ["esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_jpeg": {"alias": "idf::esp_driver_jpeg", "target": "___idf_esp_driver_jpeg", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_driver_jpeg", "lib": "__idf_esp_driver_jpeg", "reqs": [], "priv_reqs": ["esp_mm", "esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_ledc": {"alias": "idf::esp_driver_ledc", "target": "___idf_esp_driver_ledc", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_driver_ledc", "lib": "__idf_esp_driver_ledc", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_mcpwm": {"alias": "idf::esp_driver_mcpwm", "target": "___idf_esp_driver_mcpwm", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_driver_mcpwm", "lib": "__idf_esp_driver_mcpwm", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_parlio": {"alias": "idf::esp_driver_parlio", "target": "___idf_esp_driver_parlio", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_driver_parlio", "lib": "__idf_esp_driver_parlio", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_pcnt": {"alias": "idf::esp_driver_pcnt", "target": "___idf_esp_driver_pcnt", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_driver_pcnt", "lib": "__idf_esp_driver_pcnt", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_ppa": {"alias": "idf::esp_driver_ppa", "target": "___idf_esp_driver_ppa", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_driver_ppa", "lib": "__idf_esp_driver_ppa", "reqs": [], "priv_reqs": ["esp_mm", "esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_rmt": {"alias": "idf::esp_driver_rmt", "target": "___idf_esp_driver_rmt", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_driver_rmt", "lib": "__idf_esp_driver_rmt", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_sdio": {"alias": "idf::esp_driver_sdio", "target": "___idf_esp_driver_sdio", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_driver_sdio", "lib": "__idf_esp_driver_sdio", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_ringbuf"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_sdm": {"alias": "idf::esp_driver_sdm", "target": "___idf_esp_driver_sdm", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_driver_sdm", "lib": "__idf_esp_driver_sdm", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_sdmmc": {"alias": "idf::esp_driver_sdmmc", "target": "___idf_esp_driver_sdmmc", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_driver_sdmmc", "lib": "__idf_esp_driver_sdmmc", "reqs": ["sdmmc", "esp_driver_gpio"], "priv_reqs": ["esp_timer", "esp_pm", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_sdspi": {"alias": "idf::esp_driver_sdspi", "target": "___idf_esp_driver_sdspi", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_driver_sdspi", "lib": "__idf_esp_driver_sdspi", "reqs": ["sdmmc", "esp_driver_spi", "esp_driver_gpio"], "priv_reqs": ["esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_spi": {"alias": "idf::esp_driver_spi", "target": "___idf_esp_driver_spi", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_driver_spi", "lib": "__idf_esp_driver_spi", "reqs": ["esp_pm"], "priv_reqs": ["esp_timer", "esp_mm", "esp_driver_gpio", "esp_ringbuf"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_touch_sens": {"alias": "idf::esp_driver_touch_sens", "target": "___idf_esp_driver_touch_sens", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_driver_touch_sens", "lib": "__idf_esp_driver_touch_sens", "reqs": [], "priv_reqs": ["esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "esp_driver_tsens": {"alias": "idf::esp_driver_tsens", "target": "___idf_esp_driver_tsens", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_driver_tsens", "lib": "__idf_esp_driver_tsens", "reqs": [], "priv_reqs": ["efuse"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_uart": {"alias": "idf::esp_driver_uart", "target": "___idf_esp_driver_uart", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_driver_uart", "lib": "__idf_esp_driver_uart", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio", "esp_ringbuf"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_usb_serial_jtag": {"alias": "idf::esp_driver_usb_serial_jtag", "target": "___idf_esp_driver_usb_serial_jtag", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_driver_usb_serial_jtag", "lib": "__idf_esp_driver_usb_serial_jtag", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_ringbuf", "esp_pm", "esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_eth": {"alias": "idf::esp_eth", "target": "___idf_esp_eth", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_eth", "lib": "__idf_esp_eth", "reqs": ["esp_event"], "priv_reqs": ["log", "esp_timer", "esp_driver_spi", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_event": {"alias": "idf::esp_event", "target": "___idf_esp_event", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_event", "lib": "__idf_esp_event", "reqs": ["log", "esp_common", "freertos"], "priv_reqs": ["esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_gdbstub": {"alias": "idf::esp_gdbstub", "target": "___idf_esp_gdbstub", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_gdbstub", "lib": "__idf_esp_gdbstub", "reqs": ["freertos"], "priv_reqs": ["soc", "esp_rom", "esp_system"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_hid": {"alias": "idf::esp_hid", "target": "___idf_esp_hid", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_hid", "lib": "__idf_esp_hid", "reqs": ["esp_event", "bt"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_http_client": {"alias": "idf::esp_http_client", "target": "___idf_esp_http_client", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_http_client", "lib": "__idf_esp_http_client", "reqs": ["lwip", "esp_event"], "priv_reqs": ["tcp_transport", "http_parser"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_http_server": {"alias": "idf::esp_http_server", "target": "___idf_esp_http_server", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_http_server", "lib": "__idf_esp_http_server", "reqs": ["http_parser", "esp_event"], "priv_reqs": ["mbedtls", "lwip", "esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_https_ota": {"alias": "idf::esp_https_ota", "target": "___idf_esp_https_ota", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_https_ota", "lib": "__idf_esp_https_ota", "reqs": ["esp_http_client", "bootloader_support", "esp_app_format", "esp_event"], "priv_reqs": ["log", "app_update"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_https_server": {"alias": "idf::esp_https_server", "target": "___idf_esp_https_server", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_https_server", "lib": "__idf_esp_https_server", "reqs": ["esp_http_server", "esp-tls", "esp_event"], "priv_reqs": ["lwip"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_hw_support": {"alias": "idf::esp_hw_support", "target": "___idf_esp_hw_support", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_hw_support", "lib": "__idf_esp_hw_support", "reqs": ["soc"], "priv_reqs": ["efuse", "spi_flash", "bootloader_support", "esp_security", "esp_driver_gpio", "esp_timer", "esp_pm", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "include/soc", "include/soc/esp32s3", "dma/include", "ldo/include", "debug_probe/include"]}, "esp_lcd": {"alias": "idf::esp_lcd", "target": "___idf_esp_lcd", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_lcd", "lib": "__idf_esp_lcd", "reqs": ["driver", "esp_driver_gpio", "esp_driver_i2c", "esp_driver_spi"], "priv_reqs": ["esp_mm", "esp_psram", "esp_pm", "esp_driver_i2s"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "interface", "rgb/include"]}, "esp_local_ctrl": {"alias": "idf::esp_local_ctrl", "target": "___idf_esp_local_ctrl", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_local_ctrl", "lib": "__idf_esp_local_ctrl", "reqs": ["protocomm", "esp_https_server"], "priv_reqs": ["protobuf-c"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_mm": {"alias": "idf::esp_mm", "target": "___idf_esp_mm", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_mm", "lib": "__idf_esp_mm", "reqs": [], "priv_reqs": ["heap", "spi_flash"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_netif": {"alias": "idf::esp_netif", "target": "___idf_esp_netif", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_netif", "lib": "__idf_esp_netif", "reqs": ["esp_event"], "priv_reqs": ["esp_netif_stack"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_netif_stack": {"alias": "idf::esp_netif_stack", "target": "___idf_esp_netif_stack", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_netif_stack", "lib": "__idf_esp_netif_stack", "reqs": ["lwip"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "esp_partition": {"alias": "idf::esp_partition", "target": "___idf_esp_partition", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_partition", "lib": "__idf_esp_partition", "reqs": [], "priv_reqs": ["esp_system", "spi_flash", "partition_table", "bootloader_support", "app_update"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_phy": {"alias": "idf::esp_phy", "target": "___idf_esp_phy", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_phy", "lib": "__idf_esp_phy", "reqs": [], "priv_reqs": ["nvs_flash", "driver", "efuse", "esp_timer", "esp_wifi"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "esp32s3/include"]}, "esp_pm": {"alias": "idf::esp_pm", "target": "___idf_esp_pm", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_pm", "lib": "__idf_esp_pm", "reqs": [], "priv_reqs": ["esp_system", "esp_driver_gpio", "esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_psram": {"alias": "idf::esp_psram", "target": "___idf_esp_psram", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_psram", "lib": "__idf_esp_psram", "reqs": [], "priv_reqs": ["heap", "spi_flash", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_ringbuf": {"alias": "idf::esp_ringbuf", "target": "___idf_esp_ringbuf", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_ringbuf", "lib": "__idf_esp_ringbuf", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_rom": {"alias": "idf::esp_rom", "target": "___idf_esp_rom", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_rom", "lib": "__idf_esp_rom", "reqs": [], "priv_reqs": ["soc", "hal"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "esp32s3/include", "esp32s3/include/esp32s3", "esp32s3"]}, "esp_security": {"alias": "idf::esp_security", "target": "___idf_esp_security", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_security", "lib": "__idf_esp_security", "reqs": [], "priv_reqs": ["efuse", "esp_hw_support", "esp_system", "esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_system": {"alias": "idf::esp_system", "target": "___idf_esp_system", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_system", "lib": "__idf_esp_system", "reqs": [], "priv_reqs": ["spi_flash", "esp_timer", "esp_mm", "bootloader_support", "esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_timer": {"alias": "idf::esp_timer", "target": "___idf_esp_timer", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_timer", "lib": "__idf_esp_timer", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_vfs_console": {"alias": "idf::esp_vfs_console", "target": "___idf_esp_vfs_console", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_vfs_console", "lib": "__idf_esp_vfs_console", "reqs": [], "priv_reqs": ["vfs", "esp_driver_uart", "esp_driver_usb_serial_jtag"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_wifi": {"alias": "idf::esp_wifi", "target": "___idf_esp_wifi", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esp_wifi", "lib": "__idf_esp_wifi", "reqs": ["esp_event", "esp_phy", "esp_netif"], "priv_reqs": ["driver", "esptool_py", "esp_pm", "esp_timer", "nvs_flash", "wpa_supplicant", "hal", "lwip", "esp_coex"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "include/local", "wifi_apps/include", "wifi_apps/nan_app/include"]}, "espcoredump": {"alias": "idf::espcoredump", "target": "___idf_espcoredump", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/espcoredump", "lib": "__idf_espcoredump", "reqs": [], "priv_reqs": ["esp_partition", "spi_flash", "bootloader_support", "mbedtls", "esp_rom", "soc", "esp_system", "esp_driver_gpio", "driver"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "include/port/xtensa"]}, "esptool_py": {"alias": "idf::esptool_py", "target": "___idf_esptool_py", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/esptool_py", "lib": "__idf_esptool_py", "reqs": ["bootloader"], "priv_reqs": ["partition_table"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "fatfs": {"alias": "idf::fatfs", "target": "___idf_fatfs", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/fatfs", "lib": "__idf_fatfs", "reqs": ["wear_levelling", "sdmmc", "esp_driver_sdmmc", "esp_driver_sdspi"], "priv_reqs": ["vfs", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["diskio", "src", "vfs"]}, "freertos": {"alias": "idf::freertos", "target": "___idf_freertos", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/freertos", "lib": "__idf_freertos", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["config/include", "config/include/freertos", "config/xtensa/include", "FreeRTOS-Kernel/include", "FreeRTOS-Kernel/portable/xtensa/include", "FreeRTOS-Kernel/portable/xtensa/include/freertos", "esp_additions/include"]}, "hal": {"alias": "idf::hal", "target": "___idf_hal", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/hal", "lib": "__idf_hal", "reqs": ["soc", "esp_rom"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["platform_port/include", "esp32s3/include", "include"]}, "heap": {"alias": "idf::heap", "target": "___idf_heap", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/heap", "lib": "__idf_heap", "reqs": [], "priv_reqs": ["soc"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "tlsf"]}, "http_parser": {"alias": "idf::http_parser", "target": "___idf_http_parser", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/http_parser", "lib": "__idf_http_parser", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["."]}, "idf_test": {"alias": "idf::idf_test", "target": "___idf_idf_test", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/idf_test", "lib": "__idf_idf_test", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "include/esp32s3"]}, "ieee802154": {"alias": "idf::ieee802154", "target": "___idf_ieee802154", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/ieee802154", "lib": "__idf_ieee802154", "reqs": ["esp_coex"], "priv_reqs": ["esp_phy", "driver", "esp_timer", "soc", "hal"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "json": {"alias": "idf::json", "target": "___idf_json", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/json", "lib": "__idf_json", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["cJSON"]}, "linux": {"alias": "idf::linux", "target": "___idf_linux", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/linux", "lib": "__idf_linux", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["cJSON"]}, "log": {"alias": "idf::log", "target": "___idf_log", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/log", "lib": "__idf_log", "reqs": [], "priv_reqs": ["soc", "hal", "esp_hw_support"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "lwip": {"alias": "idf::lwip", "target": "___idf_lwip", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/lwip", "lib": "__idf_lwip", "reqs": [], "priv_reqs": ["vfs"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "include/apps", "include/apps/sntp", "lwip/src/include", "port/include", "port/freertos/include/", "port/esp32xx/include", "port/esp32xx/include/arch", "port/esp32xx/include/sys"]}, "mbedtls": {"alias": "idf::mbedtls", "target": "___idf_mbedtls", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/mbedtls", "lib": "__idf_mbedtls", "reqs": [], "priv_reqs": ["soc", "esp_hw_support", "esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["port/include", "mbedtls/include", "mbedtls/library", "esp_crt_bundle/include"]}, "mqtt": {"alias": "idf::mqtt", "target": "___idf_mqtt", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/mqtt", "lib": "__idf_mqtt", "reqs": ["esp_event", "tcp_transport"], "priv_reqs": ["esp_timer", "http_parser", "esp_hw_support", "heap"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["/home/<USER>/esp32/esp-idf/components/mqtt/esp-mqtt/include"]}, "newlib": {"alias": "idf::newlib", "target": "___idf_newlib", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/newlib", "lib": "__idf_newlib", "reqs": [], "priv_reqs": ["soc", "spi_flash"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["platform_include"]}, "nvs_flash": {"alias": "idf::nvs_flash", "target": "___idf_nvs_flash", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/nvs_flash", "lib": "__idf_nvs_flash", "reqs": ["esp_partition"], "priv_reqs": ["spi_flash", "newlib"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "nvs_sec_provider": {"alias": "idf::nvs_sec_provider", "target": "___idf_nvs_sec_provider", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/nvs_sec_provider", "lib": "__idf_nvs_sec_provider", "reqs": [], "priv_reqs": ["bootloader_support", "efuse", "esp_partition", "nvs_flash"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "openthread": {"alias": "idf::openthread", "target": "___idf_openthread", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/openthread", "lib": "__idf_openthread", "reqs": ["esp_netif", "lwip", "esp_driver_uart", "driver"], "priv_reqs": ["console", "esp_coex", "esp_event", "esp_partition", "esp_timer", "ieee802154", "mbedtls", "nvs_flash"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "partition_table": {"alias": "idf::partition_table", "target": "___idf_partition_table", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/partition_table", "lib": "__idf_partition_table", "reqs": [], "priv_reqs": ["esptool_py"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "perfmon": {"alias": "idf::perfmon", "target": "___idf_perfmon", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/perfmon", "lib": "__idf_perfmon", "reqs": ["xtensa"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "protobuf-c": {"alias": "idf::protobuf-c", "target": "___idf_protobuf-c", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/protobuf-c", "lib": "__idf_protobuf-c", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["protobuf-c"]}, "protocomm": {"alias": "idf::protocomm", "target": "___idf_protocomm", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/protocomm", "lib": "__idf_protocomm", "reqs": ["bt"], "priv_reqs": ["protobuf-c", "mbedtls", "console", "esp_http_server", "driver"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include/common", "include/security", "include/transports", "include/crypto/srp6a", "proto-c"]}, "pthread": {"alias": "idf::pthread", "target": "___idf_pthread", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/pthread", "lib": "__idf_pthread", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "riscv": {"alias": "idf::riscv", "target": "___idf_riscv", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/riscv", "lib": "__idf_riscv", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "rt": {"alias": "idf::rt", "target": "___idf_rt", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/rt", "lib": "__idf_rt", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "sdmmc": {"alias": "idf::sdmmc", "target": "___idf_sdmmc", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/sdmmc", "lib": "__idf_sdmmc", "reqs": [], "priv_reqs": ["soc", "esp_timer", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "soc": {"alias": "idf::soc", "target": "___idf_soc", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/soc", "lib": "__idf_soc", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "esp32s3", "esp32s3/include", "esp32s3/register"]}, "spi_flash": {"alias": "idf::spi_flash", "target": "___idf_spi_flash", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/spi_flash", "lib": "__idf_spi_flash", "reqs": ["hal"], "priv_reqs": ["bootloader_support", "app_update", "soc", "esp_mm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "spiffs": {"alias": "idf::spiffs", "target": "___idf_spiffs", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/spiffs", "lib": "__idf_spiffs", "reqs": ["esp_partition"], "priv_reqs": ["bootloader_support", "esptool_py", "vfs"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "tcp_transport": {"alias": "idf::tcp_transport", "target": "___idf_tcp_transport", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/tcp_transport", "lib": "__idf_tcp_transport", "reqs": ["esp-tls", "lwip", "esp_timer"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "touch_element": {"alias": "idf::touch_element", "target": "___idf_touch_element", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/touch_element", "lib": "__idf_touch_element", "reqs": ["driver"], "priv_reqs": ["esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "ulp": {"alias": "idf::ulp", "target": "___idf_ulp", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/ulp", "lib": "__idf_ulp", "reqs": ["driver", "esp_adc"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "unity": {"alias": "idf::unity", "target": "___idf_unity", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/unity", "lib": "__idf_unity", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "unity/src"]}, "usb": {"alias": "idf::usb", "target": "___idf_usb", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/usb", "lib": "__idf_usb", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "vfs": {"alias": "idf::vfs", "target": "___idf_vfs", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/vfs", "lib": "__idf_vfs", "reqs": [], "priv_reqs": ["esp_timer", "esp_driver_uart", "esp_driver_usb_serial_jtag", "esp_vfs_console"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "wear_levelling": {"alias": "idf::wear_levelling", "target": "___idf_wear_levelling", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/wear_levelling", "lib": "__idf_wear_levelling", "reqs": ["esp_partition"], "priv_reqs": ["spi_flash"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "wifi_provisioning": {"alias": "idf::wifi_provisioning", "target": "___idf_wifi_provisioning", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/wifi_provisioning", "lib": "__idf_wifi_provisioning", "reqs": ["lwip", "protocomm"], "priv_reqs": ["protobuf-c", "bt", "json", "esp_timer", "esp_wifi"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "wpa_supplicant": {"alias": "idf::wpa_supplicant", "target": "___idf_wpa_supplicant", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/wpa_supplicant", "lib": "__idf_wpa_supplicant", "reqs": [], "priv_reqs": ["mbedtls", "esp_timer", "esp_wifi"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "port/include", "esp_supplicant/include"]}, "xtensa": {"alias": "idf::xtensa", "target": "___idf_xtensa", "prefix": "idf", "dir": "/home/<USER>/esp32/esp-idf/components/xtensa", "lib": "__idf_xtensa", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["esp32s3/include", "include", "deprecated_include"]}, "main": {"alias": "idf::main", "target": "___idf_main", "prefix": "idf", "dir": "/home/<USER>/bysx/xiaozhi-esp32/main", "lib": "__idf_main", "reqs": [], "priv_reqs": ["78__esp-ml307", "78__esp-opus-encoder", "78__esp-wifi-connect", "78__esp_lcd_nv3023", "78__xia<PERSON><PERSON>-fonts", "espressif__adc_battery_estimation", "espressif__adc_mic", "espressif__button", "espressif__esp-sr", "espressif__esp32-camera", "espressif__esp_codec_dev", "espressif__esp_io_expander_tca9554", "espressif__esp_io_expander_tca95xx_16bit", "espressif__esp_lcd_axs15231b", "espressif__esp_lcd_gc9a01", "espressif__esp_lcd_ili9341", "espressif__esp_lcd_panel_io_additions", "espressif__esp_lcd_spd2010", "espressif__esp_lcd_st77916", "espressif__esp_lcd_st7796", "espressif__esp_lcd_touch_cst816s", "espressif__esp_lcd_touch_ft5x06", "espressif__esp_lcd_touch_gt911", "espressif__esp_lvgl_port", "espressif__esp_mmap_assets", "espressif__knob", "espressif__led_strip", "espressif2022__image_player", "lvgl__lvgl", "tny-robotics__sh1106-esp-idf", "txp666__otto-emoji-gif-component", "waveshare__esp_lcd_sh8601", "waveshare__esp_lcd_touch_cst9217", "wvirgil123__esp_jpeg_simd", "wvirgil123__sscma_client"], "managed_reqs": [], "managed_priv_reqs": ["78__esp-ml307", "78__esp-opus-encoder", "78__esp-wifi-connect", "78__esp_lcd_nv3023", "78__xia<PERSON><PERSON>-fonts", "espressif__adc_battery_estimation", "espressif__adc_mic", "espressif__button", "espressif__esp-sr", "espressif__esp32-camera", "espressif__esp_codec_dev", "espressif__esp_io_expander_tca9554", "espressif__esp_io_expander_tca95xx_16bit", "espressif__esp_lcd_axs15231b", "espressif__esp_lcd_gc9a01", "espressif__esp_lcd_ili9341", "espressif__esp_lcd_panel_io_additions", "espressif__esp_lcd_spd2010", "espressif__esp_lcd_st77916", "espressif__esp_lcd_st7796", "espressif__esp_lcd_touch_cst816s", "espressif__esp_lcd_touch_ft5x06", "espressif__esp_lcd_touch_gt911", "espressif__esp_lvgl_port", "espressif__esp_mmap_assets", "espressif__knob", "espressif__led_strip", "espressif2022__image_player", "lvgl__lvgl", "tny-robotics__sh1106-esp-idf", "txp666__otto-emoji-gif-component", "waveshare__esp_lcd_sh8601", "waveshare__esp_lcd_touch_cst9217", "wvirgil123__esp_jpeg_simd", "wvirgil123__sscma_client"], "include_dirs": [".", "display", "audio", "protocols", "/home/<USER>/bysx/xiaozhi-esp32/main/boards/common"]}, "78__esp-ml307": {"alias": "idf::78__esp-ml307", "target": "___idf_78__esp-ml307", "prefix": "idf", "dir": "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-ml307", "lib": "__idf_78__esp-ml307", "reqs": ["esp_driver_gpio", "esp_driver_uart", "esp-tls", "pthread", "mqtt"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "78__esp-opus": {"alias": "idf::78__esp-opus", "target": "___idf_78__esp-opus", "prefix": "idf", "dir": "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus", "lib": "__idf_78__esp-opus", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "78__esp-opus-encoder": {"alias": "idf::78__esp-opus-encoder", "target": "___idf_78__esp-opus-encoder", "prefix": "idf", "dir": "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-opus-encoder", "lib": "__idf_78__esp-opus-encoder", "reqs": [], "priv_reqs": ["78__esp-opus"], "managed_reqs": [], "managed_priv_reqs": ["78__esp-opus"], "include_dirs": ["include"]}, "78__esp-wifi-connect": {"alias": "idf::78__esp-wifi-connect", "target": "___idf_78__esp-wifi-connect", "prefix": "idf", "dir": "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp-wifi-connect", "lib": "__idf_78__esp-wifi-connect", "reqs": ["esp_timer", "esp_http_server", "esp_wifi", "nvs_flash", "json"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "78__esp_lcd_nv3023": {"alias": "idf::78__esp_lcd_nv3023", "target": "___idf_78__esp_lcd_nv3023", "prefix": "idf", "dir": "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__esp_lcd_nv3023", "lib": "__idf_78__esp_lcd_nv3023", "reqs": ["esp_lcd"], "priv_reqs": ["driver", "espressif__cmake_utilities"], "managed_reqs": [], "managed_priv_reqs": ["espressif__cmake_utilities"], "include_dirs": ["include"]}, "78__xiaozhi-fonts": {"alias": "idf::78__xiaozhi-fonts", "target": "___idf_78__xiaozhi-fonts", "prefix": "idf", "dir": "/home/<USER>/bysx/xiaozhi-esp32/managed_components/78__xiaozhi-fonts", "lib": "__idf_78__xiaozhi-fonts", "reqs": [], "priv_reqs": ["lvgl__lvgl"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "espressif2022__image_player": {"alias": "idf::espressif2022__image_player", "target": "___idf_espressif2022__image_player", "prefix": "idf", "dir": "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif2022__image_player", "lib": "__idf_espressif2022__image_player", "reqs": [], "priv_reqs": ["espressif__cmake_utilities"], "managed_reqs": [], "managed_priv_reqs": ["espressif__cmake_utilities"], "include_dirs": ["include"]}, "espressif__adc_battery_estimation": {"alias": "idf::espressif__adc_battery_estimation", "target": "___idf_espressif__adc_battery_estimation", "prefix": "idf", "dir": "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__adc_battery_estimation", "lib": "__idf_espressif__adc_battery_estimation", "reqs": ["esp_adc", "esp_timer"], "priv_reqs": ["espressif__cmake_utilities"], "managed_reqs": [], "managed_priv_reqs": ["espressif__cmake_utilities"], "include_dirs": ["include"]}, "espressif__adc_mic": {"alias": "idf::espressif__adc_mic", "target": "___idf_espressif__adc_mic", "prefix": "idf", "dir": "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__adc_mic", "lib": "__idf_espressif__adc_mic", "reqs": ["esp_adc"], "priv_reqs": ["espressif__cmake_utilities", "espressif__esp_codec_dev"], "managed_reqs": [], "managed_priv_reqs": ["espressif__cmake_utilities", "espressif__esp_codec_dev"], "include_dirs": ["."]}, "espressif__button": {"alias": "idf::espressif__button", "target": "___idf_espressif__button", "prefix": "idf", "dir": "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__button", "lib": "__idf_espressif__button", "reqs": ["driver", "esp_adc"], "priv_reqs": ["esp_timer", "espressif__cmake_utilities"], "managed_reqs": [], "managed_priv_reqs": ["espressif__cmake_utilities"], "include_dirs": ["include", "interface"]}, "espressif__cmake_utilities": {"alias": "idf::espressif__cmake_utilities", "target": "___idf_espressif__cmake_utilities", "prefix": "idf", "dir": "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__cmake_utilities", "lib": "__idf_espressif__cmake_utilities", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "espressif__dl_fft": {"alias": "idf::espressif__dl_fft", "target": "___idf_espressif__dl_fft", "prefix": "idf", "dir": "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__dl_fft", "lib": "__idf_espressif__dl_fft", "reqs": ["espressif__esp-dsp"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": [".", "base", "base/isa"]}, "espressif__esp-dsp": {"alias": "idf::espressif__esp-dsp", "target": "___idf_espressif__esp-dsp", "prefix": "idf", "dir": "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-dsp", "lib": "__idf_espressif__esp-dsp", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["modules/dotprod/include", "modules/support/include", "modules/support/mem/include", "modules/windows/include", "modules/windows/hann/include", "modules/windows/blackman/include", "modules/windows/blackman_harris/include", "modules/windows/blackman_nuttall/include", "modules/windows/nuttall/include", "modules/windows/flat_top/include", "modules/iir/include", "modules/fir/include", "modules/math/include", "modules/math/add/include", "modules/math/sub/include", "modules/math/mul/include", "modules/math/addc/include", "modules/math/mulc/include", "modules/math/sqrt/include", "modules/matrix/mul/include", "modules/matrix/add/include", "modules/matrix/addc/include", "modules/matrix/mulc/include", "modules/matrix/sub/include", "modules/matrix/include", "modules/fft/include", "modules/dct/include", "modules/conv/include", "modules/common/include", "modules/matrix/mul/test/include", "modules/kalman/ekf/include", "modules/kalman/ekf_imu13states/include"]}, "espressif__esp-sr": {"alias": "idf::espressif__esp-sr", "target": "___idf_espressif__esp-sr", "prefix": "idf", "dir": "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp-sr", "lib": "__idf_espressif__esp-sr", "reqs": ["json", "spiffs", "esp_partition"], "priv_reqs": ["spi_flash", "espressif__dl_fft", "espressif__esp-dsp"], "managed_reqs": [], "managed_priv_reqs": ["espressif__dl_fft", "espressif__esp-dsp"], "include_dirs": ["esp-tts/esp_tts_chinese/include", "include/esp32s3", "src/include"]}, "espressif__esp32-camera": {"alias": "idf::espressif__esp32-camera", "target": "___idf_espressif__esp32-camera", "prefix": "idf", "dir": "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp32-camera", "lib": "__idf_espressif__esp32-camera", "reqs": ["driver", "espressif__esp_jpeg"], "priv_reqs": ["freertos", "nvs_flash", "esp_timer"], "managed_reqs": ["espressif__esp_jpeg"], "managed_priv_reqs": [], "include_dirs": ["driver/include", "conversions/include"]}, "espressif__esp_codec_dev": {"alias": "idf::espressif__esp_codec_dev", "target": "___idf_espressif__esp_codec_dev", "prefix": "idf", "dir": "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_codec_dev", "lib": "__idf_espressif__esp_codec_dev", "reqs": ["driver"], "priv_reqs": ["freertos"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "interface", "device/include"]}, "espressif__esp_io_expander": {"alias": "idf::espressif__esp_io_expander", "target": "___idf_espressif__esp_io_expander", "prefix": "idf", "dir": "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_io_expander", "lib": "__idf_espressif__esp_io_expander", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "espressif__esp_io_expander_tca9554": {"alias": "idf::espressif__esp_io_expander_tca9554", "target": "___idf_espressif__esp_io_expander_tca9554", "prefix": "idf", "dir": "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_io_expander_tca9554", "lib": "__idf_espressif__esp_io_expander_tca9554", "reqs": ["driver", "espressif__esp_io_expander"], "priv_reqs": [], "managed_reqs": ["espressif__esp_io_expander"], "managed_priv_reqs": [], "include_dirs": ["include"]}, "espressif__esp_io_expander_tca95xx_16bit": {"alias": "idf::espressif__esp_io_expander_tca95xx_16bit", "target": "___idf_espressif__esp_io_expander_tca95xx_16bit", "prefix": "idf", "dir": "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_io_expander_tca95xx_16bit", "lib": "__idf_espressif__esp_io_expander_tca95xx_16bit", "reqs": ["driver", "espressif__esp_io_expander"], "priv_reqs": [], "managed_reqs": ["espressif__esp_io_expander"], "managed_priv_reqs": [], "include_dirs": ["include"]}, "espressif__esp_jpeg": {"alias": "idf::espressif__esp_jpeg", "target": "___idf_espressif__esp_jpeg", "prefix": "idf", "dir": "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_jpeg", "lib": "__idf_espressif__esp_jpeg", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "espressif__esp_lcd_axs15231b": {"alias": "idf::espressif__esp_lcd_axs15231b", "target": "___idf_espressif__esp_lcd_axs15231b", "prefix": "idf", "dir": "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_lcd_axs15231b", "lib": "__idf_espressif__esp_lcd_axs15231b", "reqs": ["driver", "esp_lcd", "espressif__esp_lcd_touch"], "priv_reqs": ["espressif__cmake_utilities"], "managed_reqs": ["espressif__esp_lcd_touch"], "managed_priv_reqs": ["espressif__cmake_utilities"], "include_dirs": ["include"]}, "espressif__esp_lcd_gc9a01": {"alias": "idf::espressif__esp_lcd_gc9a01", "target": "___idf_espressif__esp_lcd_gc9a01", "prefix": "idf", "dir": "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_lcd_gc9a01", "lib": "__idf_espressif__esp_lcd_gc9a01", "reqs": ["esp_lcd"], "priv_reqs": ["driver", "espressif__cmake_utilities"], "managed_reqs": [], "managed_priv_reqs": ["espressif__cmake_utilities"], "include_dirs": ["include"]}, "espressif__esp_lcd_ili9341": {"alias": "idf::espressif__esp_lcd_ili9341", "target": "___idf_espressif__esp_lcd_ili9341", "prefix": "idf", "dir": "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_lcd_ili9341", "lib": "__idf_espressif__esp_lcd_ili9341", "reqs": ["driver", "esp_lcd"], "priv_reqs": ["espressif__cmake_utilities"], "managed_reqs": [], "managed_priv_reqs": ["espressif__cmake_utilities"], "include_dirs": ["include"]}, "espressif__esp_lcd_panel_io_additions": {"alias": "idf::espressif__esp_lcd_panel_io_additions", "target": "___idf_espressif__esp_lcd_panel_io_additions", "prefix": "idf", "dir": "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_lcd_panel_io_additions", "lib": "__idf_espressif__esp_lcd_panel_io_additions", "reqs": ["esp_lcd", "espressif__esp_io_expander"], "priv_reqs": ["driver", "espressif__cmake_utilities"], "managed_reqs": ["espressif__esp_io_expander"], "managed_priv_reqs": ["espressif__cmake_utilities"], "include_dirs": ["include"]}, "espressif__esp_lcd_spd2010": {"alias": "idf::espressif__esp_lcd_spd2010", "target": "___idf_espressif__esp_lcd_spd2010", "prefix": "idf", "dir": "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_lcd_spd2010", "lib": "__idf_espressif__esp_lcd_spd2010", "reqs": ["esp_lcd"], "priv_reqs": ["driver", "espressif__cmake_utilities"], "managed_reqs": [], "managed_priv_reqs": ["espressif__cmake_utilities"], "include_dirs": ["include"]}, "espressif__esp_lcd_st77916": {"alias": "idf::espressif__esp_lcd_st77916", "target": "___idf_espressif__esp_lcd_st77916", "prefix": "idf", "dir": "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_lcd_st77916", "lib": "__idf_espressif__esp_lcd_st77916", "reqs": ["esp_lcd"], "priv_reqs": ["driver", "espressif__cmake_utilities"], "managed_reqs": [], "managed_priv_reqs": ["espressif__cmake_utilities"], "include_dirs": ["include"]}, "espressif__esp_lcd_st7796": {"alias": "idf::espressif__esp_lcd_st7796", "target": "___idf_espressif__esp_lcd_st7796", "prefix": "idf", "dir": "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_lcd_st7796", "lib": "__idf_espressif__esp_lcd_st7796", "reqs": ["esp_lcd"], "priv_reqs": ["driver", "espressif__cmake_utilities"], "managed_reqs": [], "managed_priv_reqs": ["espressif__cmake_utilities"], "include_dirs": ["include"]}, "espressif__esp_lcd_touch": {"alias": "idf::espressif__esp_lcd_touch", "target": "___idf_espressif__esp_lcd_touch", "prefix": "idf", "dir": "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_lcd_touch", "lib": "__idf_espressif__esp_lcd_touch", "reqs": ["driver", "esp_lcd"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "espressif__esp_lcd_touch_cst816s": {"alias": "idf::espressif__esp_lcd_touch_cst816s", "target": "___idf_espressif__esp_lcd_touch_cst816s", "prefix": "idf", "dir": "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_lcd_touch_cst816s", "lib": "__idf_espressif__esp_lcd_touch_cst816s", "reqs": ["esp_lcd", "espressif__esp_lcd_touch"], "priv_reqs": [], "managed_reqs": ["espressif__esp_lcd_touch"], "managed_priv_reqs": [], "include_dirs": ["include"]}, "espressif__esp_lcd_touch_ft5x06": {"alias": "idf::espressif__esp_lcd_touch_ft5x06", "target": "___idf_espressif__esp_lcd_touch_ft5x06", "prefix": "idf", "dir": "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_lcd_touch_ft5x06", "lib": "__idf_espressif__esp_lcd_touch_ft5x06", "reqs": ["esp_lcd", "espressif__esp_lcd_touch"], "priv_reqs": [], "managed_reqs": ["espressif__esp_lcd_touch"], "managed_priv_reqs": [], "include_dirs": ["include"]}, "espressif__esp_lcd_touch_gt911": {"alias": "idf::espressif__esp_lcd_touch_gt911", "target": "___idf_espressif__esp_lcd_touch_gt911", "prefix": "idf", "dir": "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_lcd_touch_gt911", "lib": "__idf_espressif__esp_lcd_touch_gt911", "reqs": ["esp_lcd", "espressif__esp_lcd_touch"], "priv_reqs": [], "managed_reqs": ["espressif__esp_lcd_touch"], "managed_priv_reqs": [], "include_dirs": ["include"]}, "espressif__esp_lvgl_port": {"alias": "idf::espressif__esp_lvgl_port", "target": "___idf_espressif__esp_lvgl_port", "prefix": "idf", "dir": "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_lvgl_port", "lib": "__idf_espressif__esp_lvgl_port", "reqs": ["esp_lcd", "lvgl__lvgl"], "priv_reqs": [], "managed_reqs": ["lvgl__lvgl"], "managed_priv_reqs": [], "include_dirs": ["include"]}, "espressif__esp_mmap_assets": {"alias": "idf::espressif__esp_mmap_assets", "target": "___idf_espressif__esp_mmap_assets", "prefix": "idf", "dir": "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__esp_mmap_assets", "lib": "__idf_espressif__esp_mmap_assets", "reqs": [], "priv_reqs": ["spi_flash", "esp_partition", "app_update", "espressif__cmake_utilities"], "managed_reqs": [], "managed_priv_reqs": ["espressif__cmake_utilities"], "include_dirs": ["include"]}, "espressif__knob": {"alias": "idf::espressif__knob", "target": "___idf_espressif__knob", "prefix": "idf", "dir": "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__knob", "lib": "__idf_espressif__knob", "reqs": ["driver"], "priv_reqs": ["esp_timer", "espressif__cmake_utilities"], "managed_reqs": [], "managed_priv_reqs": ["espressif__cmake_utilities"], "include_dirs": ["include"]}, "espressif__led_strip": {"alias": "idf::espressif__led_strip", "target": "___idf_espressif__led_strip", "prefix": "idf", "dir": "/home/<USER>/bysx/xiaozhi-esp32/managed_components/espressif__led_strip", "lib": "__idf_espressif__led_strip", "reqs": ["esp_driver_rmt", "esp_driver_spi"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "interface"]}, "lvgl__lvgl": {"alias": "idf::lvgl__lvgl", "target": "___idf_lvgl__lvgl", "prefix": "idf", "dir": "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl", "lib": "__idf_lvgl__lvgl", "reqs": ["esp_timer"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/src", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/../", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/examples", "/home/<USER>/bysx/xiaozhi-esp32/managed_components/lvgl__lvgl/demos"]}, "tny-robotics__sh1106-esp-idf": {"alias": "idf::tny-robotics__sh1106-esp-idf", "target": "___idf_tny-robotics__sh1106-esp-idf", "prefix": "idf", "dir": "/home/<USER>/bysx/xiaozhi-esp32/managed_components/tny-robotics__sh1106-esp-idf", "lib": "__idf_tny-robotics__sh1106-esp-idf", "reqs": ["driver", "esp_lcd"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "txp666__otto-emoji-gif-component": {"alias": "idf::txp666__otto-emoji-gif-component", "target": "___idf_txp666__otto-emoji-gif-component", "prefix": "idf", "dir": "/home/<USER>/bysx/xiaozhi-esp32/managed_components/txp666__otto-emoji-gif-component", "lib": "__idf_txp666__otto-emoji-gif-component", "reqs": ["lvgl__lvgl"], "priv_reqs": ["lvgl__lvgl"], "managed_reqs": [], "managed_priv_reqs": ["lvgl__lvgl"], "include_dirs": ["include"]}, "waveshare__esp_lcd_sh8601": {"alias": "idf::waveshare__esp_lcd_sh8601", "target": "___idf_waveshare__esp_lcd_sh8601", "prefix": "idf", "dir": "/home/<USER>/bysx/xiaozhi-esp32/managed_components/waveshare__esp_lcd_sh8601", "lib": "__idf_waveshare__esp_lcd_sh8601", "reqs": ["esp_lcd"], "priv_reqs": ["driver", "espressif__cmake_utilities"], "managed_reqs": [], "managed_priv_reqs": ["espressif__cmake_utilities"], "include_dirs": ["include"]}, "waveshare__esp_lcd_touch_cst9217": {"alias": "idf::waveshare__esp_lcd_touch_cst9217", "target": "___idf_waveshare__esp_lcd_touch_cst9217", "prefix": "idf", "dir": "/home/<USER>/bysx/xiaozhi-esp32/managed_components/waveshare__esp_lcd_touch_cst9217", "lib": "__idf_waveshare__esp_lcd_touch_cst9217", "reqs": ["esp_lcd", "espressif__esp_lcd_touch"], "priv_reqs": [], "managed_reqs": ["espressif__esp_lcd_touch"], "managed_priv_reqs": [], "include_dirs": ["include"]}, "wvirgil123__esp_jpeg_simd": {"alias": "idf::wvirgil123__esp_jpeg_simd", "target": "___idf_wvirgil123__esp_jpeg_simd", "prefix": "idf", "dir": "/home/<USER>/bysx/xiaozhi-esp32/managed_components/wvirgil123__esp_jpeg_simd", "lib": "__idf_wvirgil123__esp_jpeg_simd", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "wvirgil123__sscma_client": {"alias": "idf::wvirgil123__sscma_client", "target": "___idf_wvirgil123__sscma_client", "prefix": "idf", "dir": "/home/<USER>/bysx/xiaozhi-esp32/managed_components/wvirgil123__sscma_client", "lib": "__idf_wvirgil123__sscma_client", "reqs": ["json", "mbedtls", "esp_timer"], "priv_reqs": ["driver", "espressif__esp_io_expander"], "managed_reqs": [], "managed_priv_reqs": ["espressif__esp_io_expander"], "include_dirs": ["include", "interface"]}}, "debug_prefix_map_gdbinit": "", "gdbinit_files": {"01_symbols": "/home/<USER>/bysx/xiaozhi-esp32/build/gdbinit/symbols", "02_prefix_map": "/home/<USER>/bysx/xiaozhi-esp32/build/gdbinit/prefix_map", "03_py_extensions": "/home/<USER>/bysx/xiaozhi-esp32/build/gdbinit/py_extensions", "04_connect": "/home/<USER>/bysx/xiaozhi-esp32/build/gdbinit/connect"}, "debug_arguments_openocd": "-f board/esp32s3-builtin.cfg"}