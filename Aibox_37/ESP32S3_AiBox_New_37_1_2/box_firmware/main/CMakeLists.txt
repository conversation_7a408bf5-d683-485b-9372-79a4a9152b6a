set(SOURCES "audio_codecs/audio_codec.cc"
            "audio_codecs/no_audio_codec.cc"
            "audio_codecs/box_audio_codec.cc"
            "audio_codecs/es8311_audio_codec.cc"
            "audio_codecs/cores3_audio_codec.cc"
            "led/single_led.cc"
            "led/circular_strip.cc"
            "display/display.cc"
            "display/no_display.cc"
            "display/lcd_display.cc"
            "protocols/protocol.cc"
            "protocols/mqtt_protocol.cc"
            "protocols/websocket_protocol.cc"
            "iot/thing.cc"
            "iot/thing_manager.cc"
            "system_info.cc"
            "application.cc"
            "ota.cc"
            "settings.cc"
            "background_task.cc"
            "audio_decode_queue.cc"
            "${CMAKE_BINARY_DIR}/local_moaning_data.cc"
            "main.cc"
            "esp32_s3_szp.cc"
            )

set(INCLUDE_DIRS "." ".." "display" "audio_codecs" "protocols" "audio_processing")

# 添加 IOT 相关文件
file(GLOB IOT_SOURCES ${CMAKE_CURRENT_SOURCE_DIR}/iot/things/*.cc)
list(APPEND SOURCES ${IOT_SOURCES})

set(FONT_SOURCES
    fonts/font_awesome_14_1.c
    fonts/font_awesome_30_1.c
    fonts/font_puhui_14_1.c
)
list(APPEND SOURCES ${FONT_SOURCES})
list(APPEND INCLUDE_DIRS ${CMAKE_CURRENT_SOURCE_DIR}/fonts)

# 添加板级公共文件
file(GLOB BOARD_COMMON_SOURCES ${CMAKE_CURRENT_SOURCE_DIR}/boards/common/*.cc)
list(APPEND SOURCES ${BOARD_COMMON_SOURCES})
list(APPEND INCLUDE_DIRS ${CMAKE_CURRENT_SOURCE_DIR}/boards/common)


set(BOARD_TYPE "lichuang-dev")
file(GLOB BOARD_SOURCES ${CMAKE_CURRENT_SOURCE_DIR}/boards/${BOARD_TYPE}/*.cc)
list(APPEND SOURCES ${BOARD_SOURCES})





if(CONFIG_IDF_TARGET_ESP32S3)
    list(APPEND SOURCES "audio_processing/audio_processor.cc" "audio_processing/wake_word_detect.cc")
endif()
file(GLOB_RECURSE ALL_P3_FILE "assets/*.p3")
file(GLOB_RECURSE MOANING_ALL_P3_FILE "assets/moaning/*.p3")
file(GLOB_RECURSE OTHER_P3_FILE "assets/*.p3")
# list(REMOVE_ITEM OTHER_P3_FILE ${MOANING_ALL_P3_FILE})

file(WRITE ${CMAKE_BINARY_DIR}/local_moaning_data.cc "#include \"local_moaning_data.h\" \n\n")
foreach(VALUE ${ALL_P3_FILE})
    file(RELATIVE_PATH REL_FIL ${PROJECT_SOURCE_DIR} ${VALUE})
    get_filename_component(DIR ${REL_FIL} DIRECTORY)
    get_filename_component(FIL_WE ${REL_FIL} NAME_WE)
    file(APPEND ${CMAKE_BINARY_DIR}/local_moaning_data.cc "MoaningString(${FIL_WE});\n")
endforeach()

file(APPEND ${CMAKE_BINARY_DIR}/local_moaning_data.cc "std::map<std::string,std::pair<char*,size_t>> kMoaningStringIndex  {\n")
foreach(VALUE ${MOANING_ALL_P3_FILE})
    file(RELATIVE_PATH REL_FIL ${PROJECT_SOURCE_DIR} ${VALUE})
    get_filename_component(DIR ${REL_FIL} DIRECTORY)
    get_filename_component(FIL_WE ${REL_FIL} NAME_WE)
    file(APPEND ${CMAKE_BINARY_DIR}/local_moaning_data.cc "{\"${FIL_WE}\",{(char*)${FIL_WE}_start,${FIL_WE}_end-${FIL_WE}_start}},\n")
endforeach()
file(APPEND ${CMAKE_BINARY_DIR}/local_moaning_data.cc "};\n")
file(APPEND ${CMAKE_BINARY_DIR}/local_moaning_data.cc "std::map<std::string,std::pair<char*,size_t>> kP3StringIndex  {\n")
foreach(VALUE ${OTHER_P3_FILE})
    file(RELATIVE_PATH REL_FIL ${PROJECT_SOURCE_DIR} ${VALUE})
    get_filename_component(DIR ${REL_FIL} DIRECTORY)
    get_filename_component(FIL_WE ${REL_FIL} NAME_WE)
    file(APPEND ${CMAKE_BINARY_DIR}/local_moaning_data.cc "{\"${FIL_WE}\",{(char*)${FIL_WE}_start,${FIL_WE}_end-${FIL_WE}_start}},\n")
endforeach()


file(APPEND ${CMAKE_BINARY_DIR}/local_moaning_data.cc "};\n")
file(WRITE ${CMAKE_BINARY_DIR}/local_moaning_data.h "#pragma once\n\n#include <map>\n\n#include<string>\n\n
extern std::map<std::string,std::pair<char*,size_t>> kMoaningStringIndex;
extern std::map<std::string,std::pair<char*,size_t>> kP3StringIndex;
")

file(APPEND ${CMAKE_BINARY_DIR}/local_moaning_data.h "#define name(string) asm(#string)\n")
file(APPEND ${CMAKE_BINARY_DIR}/local_moaning_data.h "#define MoaningString(a) extern const char a##_start[]  name(_binary_##a##_p3_start); extern const char a##_end[] name(_binary_##a##_p3_end); \n")

# std::map<std::string,std::pair<char*,size_t>> kMoaningStringIndex = {
#     {
#       "p1_1_opus_binary",{p1_1_opus_binary_start,p1_1_opus_binary_start-p1_1_opus_binary_end}
#     },
    

idf_component_register(SRCS ${SOURCES}
                    EMBED_FILES ${ALL_P3_FILE}
                    INCLUDE_DIRS ${INCLUDE_DIRS}
                    WHOLE_ARCHIVE
                    )

# 使用 target_compile_definitions 来定义 BOARD_TYPE
target_compile_definitions(${COMPONENT_LIB}
                    PRIVATE BOARD_TYPE=\"${BOARD_TYPE}\"
                    )
