#include <esp_log.h>
#include <esp_err.h>
#include <nvs.h>
#include <nvs_flash.h>
#include <driver/gpio.h>
#include <esp_event.h>
#include "boards/common/button.h"
#include "application.h"
#include "system_info.h"

#include "boards/lichuang-dev/uart_433.h" 

#include "ssid_manager.h" 
#include "wifi_station.h"
#include "wifi_configuration_ap.h"
#include "project_config.h"


#define TAG "main"

extern "C" void app_main(void)
{
    // Initialize the default event loop
    ESP_ERROR_CHECK(esp_event_loop_create_default());

    // Initialize NVS flash for WiFi configuration
    esp_err_t ret = nvs_flash_init();
    if (ret == ESP_ERR_NVS_NO_FREE_PAGES || ret == ESP_ERR_NVS_NEW_VERSION_FOUND) {
        ESP_LOGW(TAG, "Erasing NVS flash to fix corruption");
        ESP_ERROR_CHECK(nvs_flash_erase());
        ret = nvs_flash_init();
    }
    ESP_ERROR_CHECK(ret);

    // ESP_LOGI("WIFI_MAC_ADDR", "------------------WIFI  MAC  ADDRESS-------------------");
    //   ESP_LOGI(TAG, "mac:id1--%s,id2--%s ,id3--%s",
    //              SystemInfo::GetMacAddress().c_str(),
    //              SystemInfo::GetMacAddressNoColon().c_str(),
    //              SystemInfo::GetMacAddressDecimal().c_str());
    //   //ESP_LOGI(TAG, "-------------Flash size : %.zu / MB------------",((SystemInfo::GetFlashSize())/(1<<20)));         
    // ESP_LOGI("WIFI_MAC_ADDR", "-------------------------------------------------------");
    
   


  #if USE_default_wifi_addr
    auto& ssid_manager = SsidManager::GetInstance();
    //ssid_manager.Clear(); // 清除之前的SSID列表(只能强制恢复出厂时使用)
    if (ssid_manager.GetSsidList().empty()) {
        ssid_manager.AddSsid(Default_WIFI_SSID,Default_WIFI_PWD);
    }
  #endif

    // Launch the application
    Application::GetInstance().Start();
    auto& app = Application::GetInstance();



    // Dump CPU usage every 10 second
    while (true) {
      vTaskDelay(10000 / portTICK_PERIOD_MS);
      // SystemInfo::PrintRealTimeStats(pdMS_TO_TICKS(1000));
      int free_sram = heap_caps_get_free_size(MALLOC_CAP_INTERNAL);
      int min_free_sram = heap_caps_get_minimum_free_size(MALLOC_CAP_INTERNAL);

      ESP_LOGI(TAG, "Free internal: %u minimal internal: %u", free_sram,
               min_free_sram);
      
      kPoserOffsetCount++;
      // 10分钟
      if (kPoserOffsetCount >= 6 * 10) {
        SetPowerOffset(0);
      }
      auto device_state = app.GetDeviceState();
      if (device_state != kDeviceStateIdle) {
        kPoserOffsetCount = 0;
      }
      if ((gpio_get_level(GPIO_NUM_48) == 1)) {
        kPoserOffsetCount = 0;
      }
    }


}
