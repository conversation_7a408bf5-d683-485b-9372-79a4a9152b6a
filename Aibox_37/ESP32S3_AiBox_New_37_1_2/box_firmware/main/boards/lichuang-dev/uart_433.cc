#include "uart_433.h"
#include "wifi_board.h"
#include "audio_codecs/box_audio_codec.h"
#include "application.h"
#include "button.h"
#include "config.h"
#include "i2c_device.h"
#include "iot/thing_manager.h"
#include "driver/adc.h"
#include "esp_adc_cal.h"
#include <esp_log.h>
#include <esp_lcd_panel_vendor.h>
#include <driver/i2c_master.h>
#include <driver/spi_common.h>
#include <wifi_station.h>
#include <driver/gpio.h>
#include "esp_adc/adc_oneshot.h"
#include "driver/uart.h"
#include "protocols/mqtt_protocol.h"
#include "freertos/queue.h"
#include "system_info.h"
#include "project_config.h"


#define USE_DMA_FOR_433_UART      1 


char button_value ='\0';
int button_value_int = 0;

bool key_433_press = false;             
static uint8_t Key_433_Count = 0;       
static uint8_t Key_433_Count_Now = 1;   



#if USE_DMA_FOR_433_UART == 1
void UART_433_Init(){

    ESP_LOGW("433_UART_INIT", "====------ UART 433 BEGIN INITing... -------===");

    uart_config_t uart_config;                          
    uart_config.baud_rate = 9600;                   
    uart_config.data_bits = UART_DATA_8_BITS;           
    uart_config.parity = UART_PARITY_DISABLE;           //无奇偶校验
    uart_config.stop_bits = UART_STOP_BITS_1;           //一个停止位
    uart_config.flow_ctrl = UART_HW_FLOWCTRL_DISABLE;  
    uart_config.source_clk = UART_SCLK_DEFAULT;             
    uart_config.rx_flow_ctrl_thresh = 0;              
    
    uart_param_config(UART_433_PIN_NUM, &uart_config);          //配置uart端口
 
    uart_set_pin(UART_433_PIN_NUM , UART_433_TX_PIN, UART_433_RX_PIN, -1, -1);
    uart_driver_install(UART_433_PIN_NUM, UART_433_RX_Buffer_Size, 0, 0, NULL, 0);

    ESP_LOGW("433_UART_INIT", "====------- UART 433 Initialized ------===");

}

#elif USE_DMA_FOR_433_UART == 0
void UART_433_Init(uint32_t baudrate){

    ESP_LOGW("433_UART_INIT", "====------ UART 433 BEGIN INITing (DMA mode)... -------===");
    
    uart_config_t uart_config = {
        .baud_rate = static_cast<int>(baudrate),
        .data_bits = UART_DATA_8_BITS,
        .parity    = UART_PARITY_DISABLE,
        .stop_bits = UART_STOP_BITS_1,
        .flow_ctrl = UART_HW_FLOWCTRL_DISABLE,
        .source_clk = UART_SCLK_APB,
    };


    // 配置UART参数
    ESP_ERROR_CHECK(uart_param_config(UART_433_PIN_NUM, &uart_config));

    // 设置引脚
    ESP_ERROR_CHECK(uart_set_pin(UART_433_PIN_NUM, UART_433_TX_PIN, UART_433_RX_PIN, UART_PIN_NO_CHANGE, UART_PIN_NO_CHANGE));

    ESP_ERROR_CHECK(uart_driver_install(UART_433_PIN_NUM,
                                        UART_433_RX_Buffer_Size,      // RX Buffer size (0 = 使用DMA)
                                        UART_433_RX_Buffer_Size,      // TX Buffer size (0 = 使用DMA)
                                        10,     // 事件队列大小
                                        NULL,   // 无事件队列
                                        UART_DRIVER_FLAG_USE_DMA)); // 中断分配标志，可设为0

    ESP_LOGW("433_UART_INIT", "====------- UART 433 Initialized in DMA mode ------===");
}

#endif

void UART_433_RX_DATA()
{
   size_t len = 0;
   uint8_t uart_433_data[UART_433_RX_Buffer_Size] = {0};
//获取数据缓冲区的数据长度
 uart_get_buffered_data_len(UART_433_PIN_NUM, &len); 

    if(len>0){
        Key_433_Count += 1;
        memset(uart_433_data, 0, UART_433_RX_Buffer_Size); 
        uart_read_bytes(UART_433_PIN_NUM, uart_433_data, len, 100 / portTICK_PERIOD_MS);
        
        //高频日志 --- 注释
        //ESP_LOGW("433_UART_RX", "====-------- UART GET 433 RX = %s --------===",uart_433_data);

        //串口数据回显
        //uart_write_bytes(UART_433_PIN_NUM, (const char*)uart_433_data, strlen((const char*)uart_433_data)); 

        char* frame_header = "LC:";  // 固定帧头
        char* frame_data = (char*)uart_433_data;
        char* header_position = strstr(frame_data, frame_header);  // 查找帧头位置

        if (header_position != NULL) {  
            // 提取按键值部分
            char* button_value_str = header_position + strlen(frame_header) + 5;  
            button_value = *button_value_str;  
            ESP_LOGI("433_UART_RX", "====--------433 Button Value: %c--------===", button_value); 

            if (button_value >= '0' && button_value <= '9') {
                button_value_int = button_value - '0';  // '0'~'9' 转 0~9
            } else if (button_value >= 'A' && button_value <= 'F') {
                button_value_int = button_value - 'A' + 10;  // 'A'~'F' 转 10~15
            } else {
                ESP_LOGW("UART", "Invalid button character: %c", button_value);
                //return;  // 错误处理
            }
            ESP_LOGI("UART", "hex_16_433key_value: %c   dec_10_433key_value: %d", button_value, button_value_int);


        } else {
            ESP_LOGW("433_UART_RX", "Frame header 'LC:' not found");
        }

        if( Key_433_Count_Now != Key_433_Count) {
        ESP_LOGW("UART_433_COUNT", "----------** Key_433_Count_Now= %d , Key_433_Count= %d***--------",Key_433_Count_Now,Key_433_Count);
                key_433_press = true;
                Key_433_Count_Now = Key_433_Count;
        }else{
                key_433_press = false;
        }
        ESP_LOGW("UART_433_COUNT", "----------** Key_433_Press=  %d ", key_433_press);

    }

}

void UART_433_TX_DATA(const char* data) {
    if (data == nullptr) return;
    uart_write_bytes(UART_433_PIN_NUM, data, strlen(data));
    uart_write_bytes(UART_433_PIN_NUM, "\r\n", 2); // 发送换行
    #if SEND_MAC_ADDR_DEBUG == 1
    ESP_LOGI("UART_433_TX", "UART_433 sent: %s", data);
    #endif
}


