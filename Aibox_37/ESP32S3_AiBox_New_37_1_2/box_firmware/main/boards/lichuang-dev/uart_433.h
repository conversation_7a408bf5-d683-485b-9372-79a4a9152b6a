
#ifndef __USART_433_H__
#define __USART_433_H__


#include "driver/uart.h"
#include "esp_log.h"
#include <functional>
#include <string.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "driver/uart_select.h"
#include "driver/gpio.h"
#include "freertos/queue.h"


 
#define UART_433_PIN_NUM           UART_NUM_2
#define UART_433_TX_PIN            GPIO_NUM_6
#define UART_433_RX_PIN            GPIO_NUM_7 
#define UART_433_TX_Buffer_Size    (1024)
#define UART_433_RX_Buffer_Size    (256)  



extern char button_value;

#if 0 
typedef struct {
    int button_value_int;
    bool key_press;
} uart_433_event_t;
#else 
extern int button_value_int;
extern bool key_433_press; 
#endif

// uart 433 事件队列定义
//extern QueueHandle_t uart_433_event_queue;


#ifdef __cplusplus
extern "C" {
#endif

void UART_433_Init();     //初始化
void UART_433_RX_DATA();  
void UART_433_TX_DATA(const char* data);   

#ifdef __cplusplus
}
#endif


 
#endif