#ifndef PROJECT_CONFIG_H
#define PROJECT_CONFIG_H

#define ON          1
#define OFF         0
#define WIRED       1
#define WIRELESS    0


//                                  >>>>> 全局项目配置选择 <<<<<
// 平台选择
//#define CONFIG_IDF_TARGET_ESP32S3


//==================================== 无线 五点式 配置 =========================================
#define Use_433_UART                        ON              //433



//====================================== 产品功能 选择 ============================================
#define FF_IMU_PARAM_IS_Wire                WIRELESS       //五点式版本
#define USE_default_wifi_addr               ON             // 默认wifi
#define WIFI_SIGNAL_CHECK_TONE              ON            //  wifi弱信号
#define WIFI_CONNECT_CHECK_TONE             ON            //  WIFI连接
#define LOCAL_MOAN_PLAYING                  OFF            //  moan 本地
#define BOOT_BEGAIN_OTA                     ON            //  开机OTA
#define SEND_MAC_ADDRESS_TASK               OFF          //MAC地址
#define SEND_MAC_ADDRESS_TIME               OFF         
#define SEND_MAC_ADDRESS_433                ON          



//======================================  DeBug 调试日志 ============================================
#define AUDIO_UPLOAD_DEBUG                  OFF            
#define AUDIO_RECEIVE_DEBUG                 OFF             //音频接收
#define SILENCE_COUNT_DEBUG                 OFF             //静音检测
#define SEND_MAC_ADDR_DEBUG                 ON             




//====================================== WIFI 配置 ===============================================
#define Default_WIFI_SSID                   "BYSX_FACTORY_TEST_01"     // 默认WiFi名
#define Default_WIFI_PWD                    "bysx8888"                 // 默认WiFi密码
#define USE_WIFI_Debug_Log                  OFF                      // WiFi 连接调试日志
#define USE_WIFI_11G_HT20                   ON                        






//====================================== MAC地址 配置 ============================================
#define GET_WIFI_MAC_ADDR_FLAG              "hello"           // 开始 发送WiFi_MAC地址的_标志(仅限字符串)






















/*
managed_components/ 及其他自定义组件
如果你有自定义组件（不是 ESP-IDF 官方组件），并且这些组件需要 include project_config.h，
你需要在它们各自的 CMakeLists.txt 里加上：

set(INCLUDE_DIRS "." "../..")
idf_component_register(SRCS ...
                      INCLUDE_DIRS ${INCLUDE_DIRS})

或

idf_component_register(SRCS ...
                      INCLUDE_DIRS "." "${CMAKE_SOURCE_DIR}")


--------------------------------
如果 tools/ 目录下有 C/C++ 代码需要 include，可以在其 CMakeLists.txt 里加
 include_directories(${CMAKE_SOURCE_DIR})。
Python 脚本不受此影响

 */


#endif // PROJECT_CONFIG_H