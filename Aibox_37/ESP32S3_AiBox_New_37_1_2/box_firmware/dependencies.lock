dependencies:
  78/esp-ml307:
    component_hash: efc6327ba41360346c55029b3604e848c68a599caf826d62fe6d2ca03a40aed8
    dependencies:
    - name: idf
      require: private
      version: ^5.3
    source:
      registry_url: https://components.espressif.com/
      type: service
    version: 1.7.1
  78/esp-opus:
    component_hash: c8a448f5ebca4e97e43e93c02ebb3940ff484ad573cb3348f917a88af094b260
    dependencies:
    - name: idf
      require: private
      version: '>=5.0'
    source:
      registry_url: https://components.espressif.com
      type: service
    version: 1.0.4
  78/esp-opus-encoder:
    component_hash: 1f327a04f9a1dad16d4d38ca8df260ef8f6849149005795427178aa81ce3fb09
    dependencies:
    - name: 78/esp-opus
      registry_url: https://components.espressif.com
      require: private
      version: ^1.0.4
    - name: idf
      require: private
      version: ^5.3
    source:
      registry_url: https://components.espressif.com/
      type: service
    version: 2.0.0
  78/esp-wifi-connect:
    component_hash: b30e773637c753cc8f476b3c50cd66dc918d694040a6f9bee4bdd14d5ceaf1aa
    dependencies:
    - name: idf
      require: private
      version: '>=5.3'
    source:
      registry_url: https://components.espressif.com/
      type: service
    version: 2.0.1
  78/esp_lcd_nv3023:
    component_hash: fa88abfc19a312eb5e6f2ffa187e0a9faf67e01e758bfb979d3f9d92561a494f
    dependencies:
    - name: espressif/cmake_utilities
      registry_url: https://components.espressif.com
      require: private
      version: 0.*
    - name: idf
      require: private
      version: '>=4.4'
    source:
      registry_url: https://components.espressif.com/
      type: service
    version: 1.0.0
  espressif/button:
    component_hash: 30a3f495c3862d505ce6e41adbbd218b2750e9723ab2151feff00e9fe685b326
    dependencies:
    - name: espressif/cmake_utilities
      registry_url: https://components.espressif.com
      require: private
      version: 0.*
    - name: idf
      require: private
      version: '>=4.0'
    source:
      registry_url: https://components.espressif.com/
      type: service
    version: 3.5.0
  espressif/cmake_utilities:
    component_hash: 351350613ceafba240b761b4ea991e0f231ac7a9f59a9ee901f751bddc0bb18f
    dependencies:
    - name: idf
      require: private
      version: '>=4.1'
    source:
      registry_url: https://components.espressif.com
      type: service
    version: 0.5.3
  espressif/esp-dsp:
    component_hash: 3e7bbd487f1357a1d4944d0c85966d049501ea281b8a4c7f93f7cfedd5b7f23d
    dependencies:
    - name: idf
      require: private
      version: '>=4.2'
    source:
      registry_url: https://components.espressif.com
      type: service
    version: 1.4.12
  espressif/esp-sr:
    component_hash: 9b41fd5ae5960c393bfd3559cd6e5fa2a95c0bf833915cebafe57fb8c4e4c396
    dependencies:
    - name: espressif/esp-dsp
      registry_url: https://components.espressif.com
      require: private
      version: <=1.5.0
    - name: idf
      require: private
      version: '>=5.0'
    source:
      registry_url: https://components.espressif.com/
      type: service
    version: 1.9.5
  espressif/esp_codec_dev:
    component_hash: 9f492cd70c85381c41dc92da48dea93dd43fbfc51532dbf748dc7d94f9846c59
    dependencies:
    - name: idf
      require: private
      version: '>=4.0'
    source:
      registry_url: https://components.espressif.com/
      type: service
    version: 1.3.3
  espressif/esp_lcd_gc9a01:
    component_hash: 09ddc8f5c9d718a659ffa30a4040e1ae75077e68492c38aec8c2beaaa7b3a9bc
    dependencies:
    - name: espressif/cmake_utilities
      registry_url: https://components.espressif.com
      require: private
      version: 0.*
    - name: idf
      require: private
      version: '>=4.4'
    source:
      registry_url: https://components.espressif.com/
      type: service
    version: 2.0.1
  espressif/esp_lcd_ili9341:
    component_hash: 31f1b793aa2110dd2ae071c21ccbff0a4eb20d9a4ee40b6294c0dc0ad9552c4e
    dependencies:
    - name: idf
      require: private
      version: '>=4.4'
    - name: espressif/cmake_utilities
      registry_url: https://components.espressif.com
      require: private
      version: 0.*
    source:
      registry_url: https://components.espressif.com/
      type: service
    version: 1.2.0
  espressif/esp_lvgl_port:
    component_hash: 41806031d90c91d50512d6f335f73978a1dd1aa1100ef5db40e189610be991ac
    dependencies:
    - name: idf
      require: private
      version: '>=4.4'
    - name: lvgl/lvgl
      registry_url: https://components.espressif.com
      require: public
      version: '>=8,<10'
    source:
      registry_url: https://components.espressif.com/
      type: service
    version: 2.4.3
  espressif/led_strip:
    component_hash: 28c6509a727ef74925b372ed404772aeedf11cce10b78c3f69b3c66799095e2d
    dependencies:
    - name: idf
      require: private
      version: '>=4.4'
    source:
      registry_url: https://components.espressif.com/
      type: service
    version: 2.5.5
  idf:
    source:
      type: idf
    version: 5.3.3
  lvgl/lvgl:
    component_hash: d7c1ac037ae6e85d94897f807d6e7ba0946a83e720074fc95a4f6241da9f9f53
    dependencies: []
    source:
      registry_url: https://components.espressif.com/
      type: service
    version: 8.4.0
  waveshare/esp_lcd_sh8601:
    component_hash: cc73d747d5437f086714f185cf14ef83ad09fe4f5351690c630ec0917f7cefc8
    dependencies:
    - name: espressif/cmake_utilities
      registry_url: https://components.espressif.com
      require: private
      version: 0.*
    - name: idf
      require: private
      version: '>=5.3'
    source:
      registry_url: https://components.espressif.com/
      type: service
    targets:
    - esp32s3
    version: 1.0.1
direct_dependencies:
- 78/esp-ml307
- 78/esp-opus-encoder
- 78/esp-wifi-connect
- 78/esp_lcd_nv3023
- espressif/button
- espressif/esp-sr
- espressif/esp_codec_dev
- espressif/esp_lcd_gc9a01
- espressif/esp_lcd_ili9341
- espressif/esp_lvgl_port
- espressif/led_strip
- idf
- lvgl/lvgl
- waveshare/esp_lcd_sh8601
manifest_hash: fe2ba4df9b0f0ff3b13fa8ef6b71d20bb70503abaaf2a55f533108460f682548
target: esp32s3
version: 2.0.0
