import os
from pathlib import Path
import opuslib
from pydub import AudioSegment

# Opus 配置参数
OPUS_SAMPLE_RATE = 16000  # 采样率必须为 8000, 12000, 16000, 24000, 或 48000
OPUS_CHANNELS = 1         # 单声道
OPUS_APPLICATION = opuslib.APPLICATION_AUDIO  # 音频类型

def create_opus_encoder():
    """创建并返回Opus编码器实例"""
    return opuslib.Encoder(OPUS_SAMPLE_RATE, OPUS_CHANNELS, OPUS_APPLICATION)

def add_binary_protocol3_header(data, type_value=0):
    """添加完整的BinaryProtocol3头部
    
    struct BinaryProtocol3 {
        uint8_t type;        // 1字节
        uint8_t reserved;    // 1字节
        uint16_t payload_size; // 2字节
        uint8_t payload[];    // 可变长度数据
    }
    """
    header = bytearray()
    header.append(type_value)  # type字段
    header.append(0)           # reserved字段
    header.extend(len(data).to_bytes(2, byteorder='big'))  # payload_size字段（大端序）
    return header + data

def process_audio_file(mp3_path):
    """处理单个MP3文件：转码为Opus并保存二进制数据"""
    # 读取并转换音频
    audio = AudioSegment.from_mp3(mp3_path)
    audio = audio.set_frame_rate(OPUS_SAMPLE_RATE)  # 设置采样率
    audio = audio.set_channels(OPUS_CHANNELS)       # 设置单声道
    audio = audio.set_sample_width(2)               # 确保16位采样深度
    
    # 获取PCM原始数据
    pcm_data = audio.raw_data
    
    # 初始化Opus编码器
    encoder = create_opus_encoder()
    frame_size = 960  # 对应60ms的帧长度（16000Hz * 0.06s）
    bytes_per_frame = frame_size * 2  # 每帧字节数（16位单声道）
    
    # 分块编码
    opus_binary = bytearray()
    for i in range(0, len(pcm_data), bytes_per_frame):
        chunk = pcm_data[i:i+bytes_per_frame]
        # 填充不完整的最后一帧
        if len(chunk) < bytes_per_frame:
            chunk += b'\x00' * (bytes_per_frame - len(chunk))
        encoded = encoder.encode(chunk, frame_size)

        # 添加完整的BinaryProtocol3头部
        frame_with_header = add_binary_protocol3_header(encoded)
        opus_binary.extend(frame_with_header)
    
    # 写入文件
    output_path = Path(mp3_path).with_name(f"{Path(mp3_path).stem}_opus_binary.p3")
    with open(output_path, 'wb') as f:
        f.write(opus_binary)
    
    print(f"生成的文件大小: {len(opus_binary)} 字节, 包含 {len(pcm_data) // bytes_per_frame} 个Opus帧")

def process_wav_file(wav_path):
    """处理单个WAV文件：转码为Opus并保存二进制数据"""
    # 读取并转换音频
    audio = AudioSegment.from_wav(wav_path)
    audio = audio.set_frame_rate(OPUS_SAMPLE_RATE)  # 设置采样率
    audio = audio.set_channels(OPUS_CHANNELS)       # 设置单声道
    audio = audio.set_sample_width(2)               # 确保16位采样深度
    
    # 获取PCM原始数据
    pcm_data = audio.raw_data
    
    # 初始化Opus编码器
    encoder = create_opus_encoder()
    frame_size = 960  # 对应60ms的帧长度（16000Hz * 0.06s）
    bytes_per_frame = frame_size * 2  # 每帧字节数（16位单声道）
    
    # 分块编码
    opus_binary = bytearray()
    for i in range(0, len(pcm_data), bytes_per_frame):
        chunk = pcm_data[i:i+bytes_per_frame]
        # 填充不完整的最后一帧
        if len(chunk) < bytes_per_frame:
            chunk += b'\x00' * (bytes_per_frame - len(chunk))
        encoded = encoder.encode(chunk, frame_size)

        # 添加完整的BinaryProtocol3头部
        frame_with_header = add_binary_protocol3_header(encoded)
        opus_binary.extend(frame_with_header)
    
    # 写入文件
    output_path = Path(wav_path).with_name(f"{Path(wav_path).stem}_opus_binary.p3")
    with open(output_path, 'wb') as f:
        f.write(opus_binary)
    
    print(f"生成的文件大小: {len(opus_binary)} 字节, 包含 {len(pcm_data) // bytes_per_frame} 个Opus帧")

def process_audio(file_path):
    """根据文件类型处理音频文件"""
    file_path = Path(file_path)
    extension = file_path.suffix.lower()
    
    if extension == '.mp3':
        process_audio_file(file_path)
    elif extension == '.wav':
        process_wav_file(file_path)
    else:
        print(f"不支持的文件类型: {extension}")

def main():
    # """主函数：遍历目录处理所有MP3文件"""
    # root_dir = Path('/home/<USER>/Downloads/tishici')  # 修改为您的目标目录
    # for mp3_path in root_dir.glob('**/*.mp3'):
    #     try:
    #         process_audio_file(str(mp3_path))
    #         print(f"处理成功：{mp3_path}")
    #     except Exception as e:
    #         print(f"处理失败 {mp3_path}: {str(e)}")
    process_audio('/home/<USER>/project/ai_box/box_firmware/low_voltage_alert.wav')
if __name__ == "__main__":
    main()